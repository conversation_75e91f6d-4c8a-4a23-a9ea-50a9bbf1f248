#!/usr/bin/env python3
"""
地震定位前后差异分析脚本

分析MAP重定位前后的发震时刻和震相到时差异
"""

import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime, timedelta
import matplotlib.pyplot as plt

def load_data():
    """加载所有相关数据"""
    base_dir = Path(__file__).parent
    
    # 原始数据
    original_catalog = pd.read_csv(base_dir.parent / "tvf_hyposvi_data" / "2011_pre_cat.csv")
    original_phases = pd.read_csv(base_dir.parent / "tvf_hyposvi_data" / "2011_phase.csv")
    
    # 重定位结果
    relocated_catalog = pd.read_csv(base_dir / "quick_test_results" / "relocated_catalog.csv")
    relocated_phases = pd.read_csv(base_dir / "quick_test_results" / "relocated_phases.csv")
    
    return original_catalog, original_phases, relocated_catalog, relocated_phases

def parse_time(time_str):
    """解析时间字符串"""
    if 'T' in str(time_str):
        return pd.to_datetime(time_str)
    else:
        return pd.to_datetime(time_str)

def analyze_origin_time_differences(original_cat, relocated_cat):
    """分析发震时刻差异"""
    print("🕐 发震时刻差异分析")
    print("=" * 50)
    
    # 转换时间格式
    original_cat['origin_time'] = original_cat['time'].apply(parse_time)
    relocated_cat['origin_time'] = relocated_cat['time'].apply(parse_time)
    
    differences = []
    
    for _, relocated_event in relocated_cat.iterrows():
        evid = relocated_event['evid']
        original_event = original_cat[original_cat['evid'] == evid]
        
        if not original_event.empty:
            original_event = original_event.iloc[0]
            
            # 计算时间差异
            time_diff = (relocated_event['origin_time'] - original_event['origin_time']).total_seconds()
            
            # 计算位置差异
            lat_diff = relocated_event['latitude'] - original_event['latitude']
            lon_diff = relocated_event['longitude'] - original_event['longitude']
            depth_diff = relocated_event['depth'] - original_event['depth']
            
            # 计算水平距离差异
            horizontal_dist = np.sqrt((lat_diff * 111.0)**2 + (lon_diff * 111.0)**2)
            
            differences.append({
                'evid': evid,
                'original_time': original_event['origin_time'],
                'relocated_time': relocated_event['origin_time'],
                'time_diff_seconds': time_diff,
                'original_lat': original_event['latitude'],
                'relocated_lat': relocated_event['latitude'],
                'lat_diff': lat_diff,
                'original_lon': original_event['longitude'],
                'relocated_lon': relocated_event['longitude'],
                'lon_diff': lon_diff,
                'original_depth': original_event['depth'],
                'relocated_depth': relocated_event['depth'],
                'depth_diff': depth_diff,
                'horizontal_dist_km': horizontal_dist
            })
    
    diff_df = pd.DataFrame(differences)
    
    # 统计分析
    print(f"分析事件数: {len(diff_df)}")
    print(f"\n📊 发震时刻差异统计:")
    print(f"   - 平均时间差异: {diff_df['time_diff_seconds'].mean():.3f} ± {diff_df['time_diff_seconds'].std():.3f} 秒")
    print(f"   - 时间差异范围: [{diff_df['time_diff_seconds'].min():.3f}, {diff_df['time_diff_seconds'].max():.3f}] 秒")
    
    print(f"\n📍 位置差异统计:")
    print(f"   - 纬度差异: {diff_df['lat_diff'].mean():.6f} ± {diff_df['lat_diff'].std():.6f} 度")
    print(f"   - 经度差异: {diff_df['lon_diff'].mean():.6f} ± {diff_df['lon_diff'].std():.6f} 度")
    print(f"   - 深度差异: {diff_df['depth_diff'].mean():.3f} ± {diff_df['depth_diff'].std():.3f} km")
    print(f"   - 水平距离差异: {diff_df['horizontal_dist_km'].mean():.3f} ± {diff_df['horizontal_dist_km'].std():.3f} km")
    
    # 详细信息
    print(f"\n📋 详细差异信息:")
    for _, row in diff_df.iterrows():
        print(f"   事件 {row['evid']}:")
        print(f"      原始时间: {row['original_time']}")
        print(f"      重定位时间: {row['relocated_time']}")
        print(f"      时间差异: {row['time_diff_seconds']:.3f} 秒")
        print(f"      位置变化: ({row['lat_diff']:.6f}°, {row['lon_diff']:.6f}°, {row['depth_diff']:.3f}km)")
        print(f"      水平距离: {row['horizontal_dist_km']:.3f} km")
        print()
    
    return diff_df

def analyze_phase_time_differences(original_phases, relocated_phases):
    """分析震相到时差异"""
    print("🌊 震相到时差异分析")
    print("=" * 50)
    
    # 转换时间格式
    original_phases['arrival_time'] = original_phases['time'].apply(parse_time)
    relocated_phases['arrival_time'] = relocated_phases['time'].apply(parse_time)
    
    phase_differences = []
    
    # 按arid匹配震相
    for _, relocated_phase in relocated_phases.iterrows():
        arid = relocated_phase['arid']
        original_phase = original_phases[original_phases['arid'] == arid]
        
        if not original_phase.empty:
            original_phase = original_phase.iloc[0]
            
            # 计算到时差异
            time_diff = (relocated_phase['arrival_time'] - original_phase['arrival_time']).total_seconds()
            
            phase_differences.append({
                'evid': relocated_phase['evid'],
                'arid': arid,
                'station': relocated_phase['station'],
                'phase_type': relocated_phase['phase'],
                'original_time': original_phase['arrival_time'],
                'relocated_time': relocated_phase['arrival_time'],
                'time_diff_seconds': time_diff
            })
    
    phase_diff_df = pd.DataFrame(phase_differences)
    
    if len(phase_diff_df) > 0:
        print(f"分析震相数: {len(phase_diff_df)}")
        
        # 总体统计
        print(f"\n📊 震相到时差异统计:")
        print(f"   - 平均到时差异: {phase_diff_df['time_diff_seconds'].mean():.3f} ± {phase_diff_df['time_diff_seconds'].std():.3f} 秒")
        print(f"   - 到时差异范围: [{phase_diff_df['time_diff_seconds'].min():.3f}, {phase_diff_df['time_diff_seconds'].max():.3f}] 秒")
        
        # 按震相类型统计
        for phase_type in ['P', 'S']:
            phase_data = phase_diff_df[phase_diff_df['phase_type'] == phase_type]
            if len(phase_data) > 0:
                print(f"\n   {phase_type}波震相 ({len(phase_data)}个):")
                print(f"      - 平均差异: {phase_data['time_diff_seconds'].mean():.3f} ± {phase_data['time_diff_seconds'].std():.3f} 秒")
                print(f"      - 差异范围: [{phase_data['time_diff_seconds'].min():.3f}, {phase_data['time_diff_seconds'].max():.3f}] 秒")
        
        # 按事件统计
        print(f"\n📋 按事件分组的震相差异:")
        for evid in phase_diff_df['evid'].unique():
            event_phases = phase_diff_df[phase_diff_df['evid'] == evid]
            print(f"   事件 {evid} ({len(event_phases)}个震相):")
            print(f"      - 平均差异: {event_phases['time_diff_seconds'].mean():.3f} ± {event_phases['time_diff_seconds'].std():.3f} 秒")
            
            # 显示前几个震相的详细信息
            for _, phase in event_phases.head(5).iterrows():
                print(f"         {phase['station']}-{phase['phase_type']}: {phase['time_diff_seconds']:.3f}s")
            if len(event_phases) > 5:
                print(f"         ... 还有 {len(event_phases) - 5} 个震相")
    else:
        print("❌ 没有找到匹配的震相数据")
    
    return phase_diff_df

def calculate_travel_time_changes(original_cat, relocated_cat, original_phases, relocated_phases):
    """计算走时变化"""
    print("\n⏱️ 走时变化分析")
    print("=" * 50)
    
    travel_time_changes = []
    
    # 转换时间格式
    original_cat['origin_time'] = original_cat['time'].apply(parse_time)
    relocated_cat['origin_time'] = relocated_cat['time'].apply(parse_time)
    original_phases['arrival_time'] = original_phases['time'].apply(parse_time)
    relocated_phases['arrival_time'] = relocated_phases['time'].apply(parse_time)
    
    for _, relocated_phase in relocated_phases.iterrows():
        arid = relocated_phase['arid']
        evid = relocated_phase['evid']
        
        # 找到对应的原始震相
        original_phase = original_phases[original_phases['arid'] == arid]
        if original_phase.empty:
            continue
        original_phase = original_phase.iloc[0]
        
        # 找到对应的事件信息
        original_event = original_cat[original_cat['evid'] == evid]
        relocated_event = relocated_cat[relocated_cat['evid'] == evid]
        
        if original_event.empty or relocated_event.empty:
            continue
            
        original_event = original_event.iloc[0]
        relocated_event = relocated_event.iloc[0]
        
        # 计算原始走时
        original_travel_time = (original_phase['arrival_time'] - original_event['origin_time']).total_seconds()
        
        # 计算重定位后走时
        relocated_travel_time = (relocated_phase['arrival_time'] - relocated_event['origin_time']).total_seconds()
        
        # 走时变化
        travel_time_change = relocated_travel_time - original_travel_time
        
        travel_time_changes.append({
            'evid': evid,
            'arid': arid,
            'station': relocated_phase['station'],
            'phase_type': relocated_phase['phase'],
            'original_travel_time': original_travel_time,
            'relocated_travel_time': relocated_travel_time,
            'travel_time_change': travel_time_change
        })
    
    travel_df = pd.DataFrame(travel_time_changes)
    
    if len(travel_df) > 0:
        print(f"分析走时数: {len(travel_df)}")
        
        # 总体统计
        print(f"\n📊 走时变化统计:")
        print(f"   - 平均走时变化: {travel_df['travel_time_change'].mean():.3f} ± {travel_df['travel_time_change'].std():.3f} 秒")
        print(f"   - 走时变化范围: [{travel_df['travel_time_change'].min():.3f}, {travel_df['travel_time_change'].max():.3f}] 秒")
        
        # 按震相类型统计
        for phase_type in ['P', 'S']:
            phase_data = travel_df[travel_df['phase_type'] == phase_type]
            if len(phase_data) > 0:
                print(f"\n   {phase_type}波走时变化 ({len(phase_data)}个):")
                print(f"      - 平均变化: {phase_data['travel_time_change'].mean():.3f} ± {phase_data['travel_time_change'].std():.3f} 秒")
                print(f"      - 原始走时: {phase_data['original_travel_time'].mean():.3f} ± {phase_data['original_travel_time'].std():.3f} 秒")
                print(f"      - 重定位走时: {phase_data['relocated_travel_time'].mean():.3f} ± {phase_data['relocated_travel_time'].std():.3f} 秒")
    
    return travel_df

def save_analysis_results(origin_diff_df, phase_diff_df, travel_df):
    """保存分析结果"""
    base_dir = Path(__file__).parent
    output_dir = base_dir / "timing_analysis_results"
    output_dir.mkdir(exist_ok=True)
    
    # 保存各种差异分析结果
    if len(origin_diff_df) > 0:
        origin_diff_df.to_csv(output_dir / "origin_time_differences.csv", index=False)
        print(f"✅ 发震时刻差异保存到: {output_dir / 'origin_time_differences.csv'}")
    
    if len(phase_diff_df) > 0:
        phase_diff_df.to_csv(output_dir / "phase_time_differences.csv", index=False)
        print(f"✅ 震相到时差异保存到: {output_dir / 'phase_time_differences.csv'}")
    
    if len(travel_df) > 0:
        travel_df.to_csv(output_dir / "travel_time_changes.csv", index=False)
        print(f"✅ 走时变化分析保存到: {output_dir / 'travel_time_changes.csv'}")

def main():
    """主函数"""
    print("🔍 地震定位前后差异分析")
    print("=" * 70)
    
    try:
        # 加载数据
        print("📂 加载数据...")
        original_cat, original_phases, relocated_cat, relocated_phases = load_data()
        
        print(f"   - 原始地震目录: {len(original_cat)} 个事件")
        print(f"   - 原始震相数据: {len(original_phases)} 条记录")
        print(f"   - 重定位目录: {len(relocated_cat)} 个事件")
        print(f"   - 重定位震相: {len(relocated_phases)} 条记录")
        print()
        
        # 分析发震时刻差异
        origin_diff_df = analyze_origin_time_differences(original_cat, relocated_cat)
        
        # 分析震相到时差异
        phase_diff_df = analyze_phase_time_differences(original_phases, relocated_phases)
        
        # 分析走时变化
        travel_df = calculate_travel_time_changes(original_cat, relocated_cat, original_phases, relocated_phases)
        
        # 保存结果
        print("\n💾 保存分析结果...")
        save_analysis_results(origin_diff_df, phase_diff_df, travel_df)
        
        print("\n🎉 分析完成！")
        
    except Exception as e:
        print(f"❌ 分析过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
