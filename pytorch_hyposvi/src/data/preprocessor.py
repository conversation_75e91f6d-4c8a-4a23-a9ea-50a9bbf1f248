"""
Data preprocessing utilities for earthquake and station data.

This module provides preprocessing functions to prepare data for
earthquake location algorithms.
"""

import numpy as np
import pandas as pd
import torch
from datetime import datetime
from typing import Union, Tuple, Optional, Dict, List

from .transforms import LLAToENU, MinMaxScaler


class DataPreprocessor:
    """
    Data preprocessor for earthquake location.
    
    Handles coordinate transformations, time calculations, and data formatting
    for use with HypoSVI algorithms.
    
    Args:
        coordinate_bounds: Dictionary with coordinate bounds
        scale: Coordinate scaling factor
    """
    
    def __init__(self, coordinate_bounds: Dict[str, float], scale: float = 200.0):
        self.coordinate_bounds = coordinate_bounds
        self.scale = scale
        
        # Setup coordinate transformation using same reference as training
        # Must match the reference point used during EikoNet training
        self.coord_transform = LLAToENU(
            ref_lat=coordinate_bounds['lat_min'],
            ref_lon=coordinate_bounds['lon_min'],
            ref_alt=0.0
        )
        
        # Setup coordinate scaler
        self.scaler = None
        
    def setup_scaler(self, station_data: pd.DataFrame) -> MinMaxScaler:
        """
        Setup coordinate scaler based on station data.
        
        Args:
            station_data: DataFrame with station coordinates
            
        Returns:
            Fitted MinMaxScaler
        """
        # Transform station coordinates to ENU
        east, north, up = self.coord_transform.transform(
            station_data['latitude'].values,
            station_data['longitude'].values,
            station_data['elevation'].values
        )
        
        # Create coordinate array for scaling
        coords = np.column_stack([east, north, up])
        
        # Add source coordinate bounds (assuming similar range)
        z_min = self.coordinate_bounds['z_min']
        z_max = self.coordinate_bounds['z_max']
        
        # Extend coordinate array with source bounds
        coord_bounds = np.array([
            [coords[:, 0].min(), coords[:, 1].min(), z_min],
            [coords[:, 0].max(), coords[:, 1].max(), z_max]
        ])
        
        extended_coords = np.vstack([coords, coord_bounds])
        
        # Fit scaler
        self.scaler = MinMaxScaler()
        self.scaler.fit(extended_coords)
        
        return self.scaler
        
    def format_arrivals(
        self,
        phases: pd.DataFrame,
        stations: pd.DataFrame
    ) -> Tuple[torch.Tensor, torch.Tensor, datetime, pd.DataFrame]:
        """
        Format arrival data for earthquake location.
        
        Args:
            phases: DataFrame with phase picks
            stations: DataFrame with station information
            
        Returns:
            Tuple of (X_obs, T_obs, T_ref, phase_station_data)
        """
        # Join phases with stations
        phase_station = phases.merge(
            stations,
            on=['network', 'station'],
            how='inner'
        )
        
        if len(phase_station) == 0:
            raise ValueError("No matching station data found")
            
        n_obs = len(phase_station)
        
        # Transform coordinates to ENU
        east, north, up = self.coord_transform.transform(
            phase_station['latitude'].values,
            phase_station['longitude'].values,
            phase_station['elevation'].values
        )
        
        # Scale coordinates if scaler is available
        if self.scaler is not None:
            coords = np.column_stack([east, north, up])
            coords_scaled = self.scaler.transform(coords)
            east = coords_scaled[:, 0]
            north = coords_scaled[:, 1]
            up = coords_scaled[:, 2]
        else:
            # Manual scaling - convert to km first, then apply scale consistently
            east = east / 1000.0 / self.scale   # Convert m to km, then scale
            north = north / 1000.0 / self.scale # Convert m to km, then scale
            up = up / self.scale                # Already in km, just scale
            
        # Create observation array
        X_obs = torch.zeros(4, n_obs, dtype=torch.float32)
        X_obs[0] = torch.tensor(east, dtype=torch.float32)
        X_obs[1] = torch.tensor(north, dtype=torch.float32)
        X_obs[2] = torch.tensor(up, dtype=torch.float32)
        
        # Set phase indicators (0 for P, 1 for S)
        phase_indicators = (phase_station['phase'] == 'S').astype(float)
        X_obs[3] = torch.tensor(phase_indicators.values, dtype=torch.float32)
        
        # Calculate relative arrival times
        arrival_times = pd.to_datetime(phase_station['time'])
        reference_time = arrival_times.min()
        
        # Convert to seconds relative to reference time
        T_obs = torch.zeros(n_obs, dtype=torch.float32)
        for i, arrival_time in enumerate(arrival_times):
            dt = (arrival_time - reference_time).total_seconds()
            T_obs[i] = dt
            
        return X_obs, T_obs, reference_time, phase_station
        
    def timedelta_seconds(self, t1: datetime, t2: datetime) -> float:
        """
        Calculate time difference in seconds.
        
        Args:
            t1: First datetime
            t2: Second datetime
            
        Returns:
            Time difference in seconds
        """
        return (t1 - t2).total_seconds()
        
    def sec_to_datetime(self, seconds: float, reference_time: datetime) -> datetime:
        """
        Convert seconds offset to datetime.
        
        Args:
            seconds: Seconds offset
            reference_time: Reference datetime
            
        Returns:
            Datetime object
        """
        from datetime import timedelta
        return reference_time + timedelta(seconds=seconds)
        
    def validate_coordinates(self, X_obs: torch.Tensor) -> bool:
        """
        Validate observation coordinates.
        
        Args:
            X_obs: Observation coordinates tensor
            
        Returns:
            True if coordinates are valid
        """
        # Check for NaN or infinite values
        if torch.any(torch.isnan(X_obs)) or torch.any(torch.isinf(X_obs)):
            return False
            
        # Check coordinate bounds (assuming normalized coordinates)
        if self.scaler is not None:
            # Coordinates should be in [0, 1] range after scaling
            if torch.any(X_obs[:3] < 0) or torch.any(X_obs[:3] > 1):
                return False
                
        # Check phase indicators (should be 0 or 1)
        phases = X_obs[3]
        if torch.any((phases != 0) & (phases != 1)):
            return False
            
        return True
        
    def filter_outlier_picks(
        self,
        phases: pd.DataFrame,
        residuals: Optional[pd.DataFrame] = None,
        max_residual: Optional[float] = None,
        n_mad: float = 3.0
    ) -> pd.DataFrame:
        """
        Filter outlier phase picks based on residuals.
        
        Args:
            phases: DataFrame with phase picks
            residuals: DataFrame with residuals (optional)
            max_residual: Maximum allowed residual (optional)
            n_mad: Number of MAD (Median Absolute Deviation) for outlier detection
            
        Returns:
            Filtered phase DataFrame
        """
        if residuals is None:
            return phases
            
        # Join phases with residuals
        phases_with_resid = phases.merge(
            residuals,
            on=['evid', 'network', 'station', 'phase'],
            how='inner'
        )
        
        if max_residual is not None:
            # Filter by absolute residual threshold
            mask = np.abs(phases_with_resid['residual']) <= max_residual
            phases_filtered = phases_with_resid[mask]
        else:
            # Filter by MAD per event
            def filter_by_mad(group):
                residuals = group['residual'].values
                mad = np.median(np.abs(residuals - np.median(residuals)))
                threshold = n_mad * mad
                mask = np.abs(residuals) <= threshold
                return group[mask]
                
            phases_filtered = phases_with_resid.groupby('evid').apply(filter_by_mad)
            phases_filtered = phases_filtered.reset_index(drop=True)
            
        # Return original columns only
        original_cols = phases.columns
        return phases_filtered[original_cols]
        
    def estimate_pick_uncertainty(
        self,
        phases: pd.DataFrame,
        default_p: float = 0.1,
        default_s: float = 0.1
    ) -> np.ndarray:
        """
        Estimate pick uncertainties.
        
        Args:
            phases: DataFrame with phase picks
            default_p: Default P-wave uncertainty (seconds)
            default_s: Default S-wave uncertainty (seconds)
            
        Returns:
            Array of uncertainties for each pick
        """
        uncertainties = np.zeros(len(phases))
        
        for i, row in phases.iterrows():
            if row['phase'] == 'P':
                uncertainties[i] = default_p
            elif row['phase'] == 'S':
                uncertainties[i] = default_s
            else:
                # Unknown phase, use larger uncertainty
                uncertainties[i] = max(default_p, default_s) * 2
                
        return uncertainties
