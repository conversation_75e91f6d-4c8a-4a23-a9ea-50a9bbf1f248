#!/usr/bin/env python3
"""
查看训练结果的脚本
"""

import os
import glob
from pathlib import Path


def main():
    """主函数"""
    print("=" * 60)
    print("EikoNet 训练结果查看")
    print("=" * 60)
    
    # 检查生成的文件
    current_dir = Path(".")
    
    print("\n1. 模型文件:")
    model_files = list(current_dir.glob("*.pth"))
    if model_files:
        for f in model_files:
            size = f.stat().st_size / (1024*1024)  # MB
            print(f"  - {f.name} ({size:.1f} MB)")
    else:
        print("  未找到模型文件")
    
    print("\n2. 训练历史图:")
    history_files = list(current_dir.glob("training_history.png"))
    if history_files:
        for f in history_files:
            print(f"  - {f.name}")
    else:
        print("  未找到训练历史图")
    
    print("\n3. 速度对比图:")
    plot_dir = current_dir / "plots"
    if plot_dir.exists():
        plot_files = sorted(list(plot_dir.glob("velocity_comparison_epoch_*.png")))
        if plot_files:
            print(f"  共生成 {len(plot_files)} 张速度对比图:")
            for f in plot_files:
                print(f"    - {f.name}")
        else:
            print("  未找到速度对比图")
    else:
        print("  plots目录不存在")
    
    print("\n4. 配置文件:")
    config_files = list(current_dir.glob("*.json"))
    if config_files:
        for f in config_files:
            print(f"  - {f.name}")
    else:
        print("  未找到配置文件")
    
    print("\n5. 使用说明:")
    print("  - 可以使用图片查看器打开 plots/ 目录下的图片")
    print("  - 速度对比图显示了每个训练轮次的预测效果")
    print("  - 红色散点是预测速度，蓝色实线是真实速度")
    print("  - 红色虚线是预测速度的趋势线")
    print("  - MAE和RMSE显示了预测误差")
    
    print("\n6. 下一步:")
    print("  - 如果测试结果满意，可以运行完整训练:")
    print("    python train_eikonet_simple.py")
    print("  - 或使用交互式脚本:")
    print("    python run_training.py")
    
    print("=" * 60)


if __name__ == "__main__":
    main()
