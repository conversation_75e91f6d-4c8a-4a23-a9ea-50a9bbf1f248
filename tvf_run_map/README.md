# TVF数据MAP地震重定位

这个目录包含只使用MAP方法进行地震定位的代码，基于`tvf_run_map_ssst`目录的MAP-SSST代码，移除了SSST部分。

## 文件说明

- `run_tvf_map_ultra_fast.py` - 主要的MAP定位程序
- `tvf_map_config.json` - 配置文件（与MAP-SSST配置基本相同，移除了SSST相关配置）
- `README.md` - 本说明文件

## 主要特点

1. **纯MAP方法**: 只使用最大后验概率(MAP)方法进行地震定位，不使用SSST校正
2. **快速处理**: 相比MAP-SSST方法更快，因为跳过了SSST迭代步骤
3. **输出格式一致**: 生成的输出文件格式与MAP-SSST版本完全一致
4. **优化配置**: 针对MAP方法优化了训练参数

## 配置参数

主要配置参数（相比MAP-SSST的变化）：
- 训练轮数: 300 (适中的训练轮数)
- 早停参数: 50 (适中的早停参数)
- 学习率: 0.008 (稍微提高学习率加速收敛)
- 移除了所有SSST相关配置

## 运行方法

```bash
cd tvf_run_map
python run_tvf_map_ultra_fast.py
```

程序会提示选择处理模式：
1. 超快测试 (20个事件)
2. 快速测试 (50个事件)
3. 中等测试 (100个事件)
4. 大规模测试 (200个事件)
5. 全部事件

## 输出文件

程序会在`tvf_map_ultra_fast_results`目录下生成以下文件：

1. **map_locations.csv** - MAP定位结果
2. **map_residuals.csv** - 残差信息
3. **relocated_catalog.csv** - 重定位地震目录（与原始格式一致）
4. **relocated_phases.csv** - 重定位震相文件（与原始格式一致）
5. **relocated_catalog_simple.csv** - 简化的重定位目录

## 性能对比

相比MAP-SSST方法：
- **速度更快**: 跳过SSST迭代，处理速度提升约30-50%
- **精度略低**: 没有SSST校正，定位精度可能略低于MAP-SSST
- **适用场景**: 适合快速处理大量地震事件，或者作为初步定位结果

## 依赖关系

- 需要训练好的EikoNet模型: `../tvf_run_eikonet/tvf_eikonet_model.pth`
- 需要输入数据: `../tvf_hyposvi_data/`目录下的数据文件
- 依赖`../pytorch_hyposvi/run_map_ssst/map_ssst_locator.py`中的MAP功能

## 注意事项

1. 本程序复用了MAP-SSST定位器中的MAP功能，只是跳过了SSST部分
2. 输出格式与MAP-SSST完全一致，便于结果对比
3. 适合作为MAP-SSST的快速替代方案或初步定位工具
