#!/usr/bin/env python3
"""
Plot earthquake distribution comparison between original and relocated catalogs
Creates three-view plots (longitude-latitude, longitude-depth, latitude-depth)
"""

import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import os

def load_earthquake_data():
    """Load original and relocated earthquake catalogs"""
    # Load original catalog
    original_file = '/home/<USER>/run_eikonet_hyposvi/hyposvi/pre_cat.csv'
    original_df = pd.read_csv(original_file)
    
    # Load relocated catalog
    relocated_file = '/home/<USER>/run_eikonet_hyposvi/pytorch_hyposvi/run_map_ssst/map_ssst_optimized_results/relocated_catalog_optimized_simple.csv'
    relocated_df = pd.read_csv(relocated_file)
    
    return original_df, relocated_df

def create_comparison_plots(original_df, relocated_df, output_dir):
    """Create three-view comparison plots"""

    # Set up the figure with three subplots
    fig, axes = plt.subplots(1, 3, figsize=(20, 7))
    fig.suptitle('Earthquake Distribution Comparison: Original vs Relocated', fontsize=18, fontweight='bold')

    # Colors for original and relocated earthquakes
    original_color = 'blue'
    relocated_color = 'red'
    alpha = 0.7
    markersize = 4
    
    # Plot 1: Longitude vs Latitude (Map view)
    ax1 = axes[0]
    ax1.scatter(original_df['longitude'], original_df['latitude'], 
               c=original_color, alpha=alpha, s=markersize, label='Original')
    ax1.scatter(relocated_df['longitude'], relocated_df['latitude'], 
               c=relocated_color, alpha=alpha, s=markersize, label='Relocated')
    ax1.set_xlabel('Longitude (°)', fontsize=12)
    ax1.set_ylabel('Latitude (°)', fontsize=12)
    ax1.set_title('Map View (Longitude vs Latitude)', fontsize=14, fontweight='bold')
    ax1.grid(True, alpha=0.3)
    ax1.legend(fontsize=10)
    ax1.set_aspect('equal', adjustable='box')
    
    # Plot 2: Longitude vs Depth (East-West cross-section)
    ax2 = axes[1]
    ax2.scatter(original_df['longitude'], original_df['depth'], 
               c=original_color, alpha=alpha, s=markersize, label='Original')
    ax2.scatter(relocated_df['longitude'], relocated_df['depth'], 
               c=relocated_color, alpha=alpha, s=markersize, label='Relocated')
    ax2.set_xlabel('Longitude (°)', fontsize=12)
    ax2.set_ylabel('Depth (km)', fontsize=12)
    ax2.set_title('East-West Cross-section (Longitude vs Depth)', fontsize=14, fontweight='bold')
    ax2.invert_yaxis()  # Invert y-axis so depth increases downward
    ax2.grid(True, alpha=0.3)
    ax2.legend(fontsize=10)
    
    # Plot 3: Latitude vs Depth (North-South cross-section)
    ax3 = axes[2]
    ax3.scatter(original_df['latitude'], original_df['depth'], 
               c=original_color, alpha=alpha, s=markersize, label='Original')
    ax3.scatter(relocated_df['latitude'], relocated_df['depth'], 
               c=relocated_color, alpha=alpha, s=markersize, label='Relocated')
    ax3.set_xlabel('Latitude (°)', fontsize=12)
    ax3.set_ylabel('Depth (km)', fontsize=12)
    ax3.set_title('North-South Cross-section (Latitude vs Depth)', fontsize=14, fontweight='bold')
    ax3.invert_yaxis()  # Invert y-axis so depth increases downward
    ax3.grid(True, alpha=0.3)
    ax3.legend(fontsize=10)
    
    # Adjust layout to prevent overlap
    plt.tight_layout()
    
    # Save the plot
    output_file = os.path.join(output_dir, 'earthquake_comparison_three_views.png')
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    print(f"Three-view comparison plot saved to: {output_file}")
    
    # Close the figure to free memory
    plt.close()

def print_statistics(original_df, relocated_df):
    """Print basic statistics about the earthquake catalogs"""
    print("\n" + "="*60)
    print("EARTHQUAKE CATALOG STATISTICS")
    print("="*60)
    
    print(f"\nOriginal Catalog:")
    print(f"  Number of events: {len(original_df)}")
    print(f"  Longitude range: {original_df['longitude'].min():.4f} to {original_df['longitude'].max():.4f}")
    print(f"  Latitude range: {original_df['latitude'].min():.4f} to {original_df['latitude'].max():.4f}")
    print(f"  Depth range: {original_df['depth'].min():.2f} to {original_df['depth'].max():.2f} km")
    print(f"  Mean depth: {original_df['depth'].mean():.2f} km")
    
    print(f"\nRelocated Catalog:")
    print(f"  Number of events: {len(relocated_df)}")
    print(f"  Longitude range: {relocated_df['longitude'].min():.4f} to {relocated_df['longitude'].max():.4f}")
    print(f"  Latitude range: {relocated_df['latitude'].min():.4f} to {relocated_df['latitude'].max():.4f}")
    print(f"  Depth range: {relocated_df['depth'].min():.2f} to {relocated_df['depth'].max():.2f} km")
    print(f"  Mean depth: {relocated_df['depth'].mean():.2f} km")
    
    # Calculate differences
    if len(original_df) == len(relocated_df):
        lon_diff = np.abs(relocated_df['longitude'].values - original_df['longitude'].values)
        lat_diff = np.abs(relocated_df['latitude'].values - original_df['latitude'].values)
        depth_diff = np.abs(relocated_df['depth'].values - original_df['depth'].values)
        
        print(f"\nRelocation Differences (Mean ± Std):")
        print(f"  Longitude: {lon_diff.mean():.6f} ± {lon_diff.std():.6f} degrees")
        print(f"  Latitude: {lat_diff.mean():.6f} ± {lat_diff.std():.6f} degrees")
        print(f"  Depth: {depth_diff.mean():.3f} ± {depth_diff.std():.3f} km")
        
        # Convert to approximate distances (rough approximation)
        lon_dist_km = lon_diff * 111.0 * np.cos(np.radians(original_df['latitude'].mean()))
        lat_dist_km = lat_diff * 111.0
        
        print(f"\nApproximate Distance Differences:")
        print(f"  East-West: {lon_dist_km.mean():.3f} ± {lon_dist_km.std():.3f} km")
        print(f"  North-South: {lat_dist_km.mean():.3f} ± {lat_dist_km.std():.3f} km")
    
    print("="*60)

def main():
    """Main function"""
    print("Loading earthquake catalogs...")
    
    # Load data
    original_df, relocated_df = load_earthquake_data()
    
    # Print statistics
    print_statistics(original_df, relocated_df)
    
    # Set output directory
    output_dir = '/home/<USER>/run_eikonet_hyposvi/pytorch_hyposvi/run_map_ssst'
    
    # Create comparison plots
    print(f"\nCreating three-view comparison plots...")
    create_comparison_plots(original_df, relocated_df, output_dir)
    
    print("\nPlot generation completed successfully!")

if __name__ == "__main__":
    main()
