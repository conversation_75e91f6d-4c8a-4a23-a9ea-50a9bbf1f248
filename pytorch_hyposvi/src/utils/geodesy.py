"""
Geodetic coordinate transformation utilities.

This module provides functions for geographic coordinate transformations,
distance calculations, and geodetic computations.
"""

import numpy as np
import math
from typing import Tuple, Union


# Earth parameters (WGS84)
WGS84_A = 6378137.0  # Semi-major axis (m)
WGS84_F = 1/298.257223563  # Flattening
WGS84_E2 = 2*WGS84_F - WGS84_F**2  # First eccentricity squared


def lla_to_enu(
    lat: Union[float, np.ndarray],
    lon: Union[float, np.ndarray], 
    alt: Union[float, np.ndarray],
    ref_lat: float,
    ref_lon: float,
    ref_alt: float = 0.0
) -> Tuple[Union[float, np.ndarray], Union[float, np.ndarray], Union[float, np.ndarray]]:
    """
    Convert LLA (Latitude-Longitude-Altitude) to ENU (East-North-Up).
    
    Args:
        lat: Latitude in degrees
        lon: Longitude in degrees
        alt: Altitude in meters
        ref_lat: Reference latitude in degrees
        ref_lon: Reference longitude in degrees
        ref_alt: Reference altitude in meters
        
    Returns:
        Tuple of (east, north, up) in meters
    """
    # Convert to ECEF first
    x, y, z = lla_to_ecef(lat, lon, alt)
    ref_x, ref_y, ref_z = lla_to_ecef(ref_lat, ref_lon, ref_alt)
    
    # Vector from reference to point in ECEF
    dx = x - ref_x
    dy = y - ref_y
    dz = z - ref_z
    
    # Convert reference point to radians
    ref_lat_rad = math.radians(ref_lat)
    ref_lon_rad = math.radians(ref_lon)
    
    # Transformation matrix from ECEF to ENU
    sin_lat = math.sin(ref_lat_rad)
    cos_lat = math.cos(ref_lat_rad)
    sin_lon = math.sin(ref_lon_rad)
    cos_lon = math.cos(ref_lon_rad)
    
    # Apply transformation
    east = -sin_lon * dx + cos_lon * dy
    north = -sin_lat * cos_lon * dx - sin_lat * sin_lon * dy + cos_lat * dz
    up = cos_lat * cos_lon * dx + cos_lat * sin_lon * dy + sin_lat * dz
    
    return east, north, up


def enu_to_lla(
    east: Union[float, np.ndarray],
    north: Union[float, np.ndarray],
    up: Union[float, np.ndarray],
    ref_lat: float,
    ref_lon: float,
    ref_alt: float = 0.0
) -> Tuple[Union[float, np.ndarray], Union[float, np.ndarray], Union[float, np.ndarray]]:
    """
    Convert ENU (East-North-Up) to LLA (Latitude-Longitude-Altitude).
    
    Args:
        east: East coordinate in meters
        north: North coordinate in meters
        up: Up coordinate in meters
        ref_lat: Reference latitude in degrees
        ref_lon: Reference longitude in degrees
        ref_alt: Reference altitude in meters
        
    Returns:
        Tuple of (latitude, longitude, altitude)
    """
    # Convert reference point to ECEF
    ref_x, ref_y, ref_z = lla_to_ecef(ref_lat, ref_lon, ref_alt)
    
    # Convert reference point to radians
    ref_lat_rad = math.radians(ref_lat)
    ref_lon_rad = math.radians(ref_lon)
    
    # Transformation matrix from ENU to ECEF
    sin_lat = math.sin(ref_lat_rad)
    cos_lat = math.cos(ref_lat_rad)
    sin_lon = math.sin(ref_lon_rad)
    cos_lon = math.cos(ref_lon_rad)
    
    # Apply transformation
    dx = -sin_lon * east - sin_lat * cos_lon * north + cos_lat * cos_lon * up
    dy = cos_lon * east - sin_lat * sin_lon * north + cos_lat * sin_lon * up
    dz = cos_lat * north + sin_lat * up
    
    # Convert to ECEF
    x = ref_x + dx
    y = ref_y + dy
    z = ref_z + dz
    
    # Convert ECEF to LLA
    lat, lon, alt = ecef_to_lla(x, y, z)
    
    return lat, lon, alt


def lla_to_ecef(
    lat: Union[float, np.ndarray],
    lon: Union[float, np.ndarray],
    alt: Union[float, np.ndarray]
) -> Tuple[Union[float, np.ndarray], Union[float, np.ndarray], Union[float, np.ndarray]]:
    """
    Convert LLA to ECEF (Earth-Centered, Earth-Fixed) coordinates.
    
    Args:
        lat: Latitude in degrees
        lon: Longitude in degrees
        alt: Altitude in meters
        
    Returns:
        Tuple of (x, y, z) in meters
    """
    # Convert to radians
    lat_rad = np.radians(lat)
    lon_rad = np.radians(lon)
    
    # Radius of curvature in prime vertical
    N = WGS84_A / np.sqrt(1 - WGS84_E2 * np.sin(lat_rad)**2)
    
    # ECEF coordinates
    x = (N + alt) * np.cos(lat_rad) * np.cos(lon_rad)
    y = (N + alt) * np.cos(lat_rad) * np.sin(lon_rad)
    z = (N * (1 - WGS84_E2) + alt) * np.sin(lat_rad)
    
    return x, y, z


def ecef_to_lla(
    x: Union[float, np.ndarray],
    y: Union[float, np.ndarray],
    z: Union[float, np.ndarray]
) -> Tuple[Union[float, np.ndarray], Union[float, np.ndarray], Union[float, np.ndarray]]:
    """
    Convert ECEF to LLA coordinates using iterative method.
    
    Args:
        x: X coordinate in meters
        y: Y coordinate in meters
        z: Z coordinate in meters
        
    Returns:
        Tuple of (latitude, longitude, altitude) in degrees and meters
    """
    # Longitude is straightforward
    lon = np.arctan2(y, x)
    
    # Iterative solution for latitude and altitude
    p = np.sqrt(x**2 + y**2)
    lat = np.arctan2(z, p * (1 - WGS84_E2))
    
    # Iterate to improve accuracy
    for _ in range(5):
        N = WGS84_A / np.sqrt(1 - WGS84_E2 * np.sin(lat)**2)
        alt = p / np.cos(lat) - N
        lat = np.arctan2(z, p * (1 - WGS84_E2 * N / (N + alt)))
        
    # Final altitude calculation
    N = WGS84_A / np.sqrt(1 - WGS84_E2 * np.sin(lat)**2)
    alt = p / np.cos(lat) - N
    
    # Convert to degrees
    lat = np.degrees(lat)
    lon = np.degrees(lon)
    
    return lat, lon, alt


def distance_haversine(
    lat1: float, lon1: float,
    lat2: float, lon2: float
) -> float:
    """
    Calculate great circle distance using Haversine formula.
    
    Args:
        lat1: First point latitude in degrees
        lon1: First point longitude in degrees
        lat2: Second point latitude in degrees
        lon2: Second point longitude in degrees
        
    Returns:
        Distance in meters
    """
    # Convert to radians
    lat1_rad = math.radians(lat1)
    lon1_rad = math.radians(lon1)
    lat2_rad = math.radians(lat2)
    lon2_rad = math.radians(lon2)
    
    # Haversine formula
    dlat = lat2_rad - lat1_rad
    dlon = lon2_rad - lon1_rad
    
    a = (math.sin(dlat/2)**2 + 
         math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(dlon/2)**2)
    c = 2 * math.asin(math.sqrt(a))
    
    # Earth radius in meters
    R = 6371000.0
    
    return R * c


def azimuth_distance(
    lat1: float, lon1: float,
    lat2: float, lon2: float
) -> Tuple[float, float]:
    """
    Calculate azimuth and distance between two points.
    
    Args:
        lat1: First point latitude in degrees
        lon1: First point longitude in degrees
        lat2: Second point latitude in degrees
        lon2: Second point longitude in degrees
        
    Returns:
        Tuple of (azimuth in degrees, distance in meters)
    """
    # Convert to radians
    lat1_rad = math.radians(lat1)
    lon1_rad = math.radians(lon1)
    lat2_rad = math.radians(lat2)
    lon2_rad = math.radians(lon2)
    
    # Calculate distance
    distance = distance_haversine(lat1, lon1, lat2, lon2)
    
    # Calculate azimuth
    dlon = lon2_rad - lon1_rad
    
    y = math.sin(dlon) * math.cos(lat2_rad)
    x = (math.cos(lat1_rad) * math.sin(lat2_rad) - 
         math.sin(lat1_rad) * math.cos(lat2_rad) * math.cos(dlon))
    
    azimuth_rad = math.atan2(y, x)
    azimuth_deg = math.degrees(azimuth_rad)
    
    # Normalize to 0-360 degrees
    azimuth_deg = (azimuth_deg + 360) % 360
    
    return azimuth_deg, distance


def utm_zone(lon: float) -> int:
    """
    Calculate UTM zone number from longitude.
    
    Args:
        lon: Longitude in degrees
        
    Returns:
        UTM zone number
    """
    return int((lon + 180) / 6) + 1


def mgrs_to_utm(mgrs: str) -> Tuple[int, str, float, float]:
    """
    Convert MGRS coordinate to UTM.
    
    Args:
        mgrs: MGRS coordinate string
        
    Returns:
        Tuple of (zone, band, easting, northing)
    """
    # This is a simplified implementation
    # For full MGRS support, use a dedicated library like pyproj
    raise NotImplementedError("MGRS conversion requires additional implementation")


def vincenty_distance(
    lat1: float, lon1: float,
    lat2: float, lon2: float
) -> float:
    """
    Calculate distance using Vincenty's formula (more accurate than Haversine).
    
    Args:
        lat1: First point latitude in degrees
        lon1: First point longitude in degrees
        lat2: Second point latitude in degrees
        lon2: Second point longitude in degrees
        
    Returns:
        Distance in meters
    """
    # Convert to radians
    lat1_rad = math.radians(lat1)
    lon1_rad = math.radians(lon1)
    lat2_rad = math.radians(lat2)
    lon2_rad = math.radians(lon2)
    
    # WGS84 parameters
    a = WGS84_A
    b = a * (1 - WGS84_F)
    f = WGS84_F
    
    L = lon2_rad - lon1_rad
    U1 = math.atan((1 - f) * math.tan(lat1_rad))
    U2 = math.atan((1 - f) * math.tan(lat2_rad))
    
    sin_U1 = math.sin(U1)
    cos_U1 = math.cos(U1)
    sin_U2 = math.sin(U2)
    cos_U2 = math.cos(U2)
    
    lambda_val = L
    lambda_prev = 2 * math.pi
    iter_limit = 100
    
    while abs(lambda_val - lambda_prev) > 1e-12 and iter_limit > 0:
        sin_lambda = math.sin(lambda_val)
        cos_lambda = math.cos(lambda_val)
        
        sin_sigma = math.sqrt((cos_U2 * sin_lambda)**2 + 
                             (cos_U1 * sin_U2 - sin_U1 * cos_U2 * cos_lambda)**2)
        
        if sin_sigma == 0:
            return 0  # Co-incident points
            
        cos_sigma = sin_U1 * sin_U2 + cos_U1 * cos_U2 * cos_lambda
        sigma = math.atan2(sin_sigma, cos_sigma)
        
        sin_alpha = cos_U1 * cos_U2 * sin_lambda / sin_sigma
        cos2_alpha = 1 - sin_alpha**2
        
        cos_2sigma_m = cos_sigma - 2 * sin_U1 * sin_U2 / cos2_alpha
        if math.isnan(cos_2sigma_m):
            cos_2sigma_m = 0  # Equatorial line
            
        C = f / 16 * cos2_alpha * (4 + f * (4 - 3 * cos2_alpha))
        
        lambda_prev = lambda_val
        lambda_val = (L + (1 - C) * f * sin_alpha * 
                     (sigma + C * sin_sigma * 
                      (cos_2sigma_m + C * cos_sigma * (-1 + 2 * cos_2sigma_m**2))))
        
        iter_limit -= 1
        
    if iter_limit == 0:
        return float('nan')  # Formula failed to converge
        
    u2 = cos2_alpha * (a**2 - b**2) / (b**2)
    A = 1 + u2 / 16384 * (4096 + u2 * (-768 + u2 * (320 - 175 * u2)))
    B = u2 / 1024 * (256 + u2 * (-128 + u2 * (74 - 47 * u2)))
    
    delta_sigma = (B * sin_sigma * 
                   (cos_2sigma_m + B / 4 * 
                    (cos_sigma * (-1 + 2 * cos_2sigma_m**2) - 
                     B / 6 * cos_2sigma_m * (-3 + 4 * sin_sigma**2) * 
                     (-3 + 4 * cos_2sigma_m**2))))
    
    s = b * A * (sigma - delta_sigma)
    
    return s
