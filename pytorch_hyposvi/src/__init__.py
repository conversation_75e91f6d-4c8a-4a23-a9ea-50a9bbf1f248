"""
PyTorch HypoSVI - Earthquake Location System with EikoNet Neural Networks

This package provides a PyTorch implementation of the HypoSVI earthquake location
system, including EikoNet neural networks for travel time prediction and both
MAP and SVI methods for earthquake hypocenter inversion.

Main modules:
- eikonet: Neural network models for travel time prediction
- hyposvi: Earthquake location algorithms (MAP and SVI)
- data: Data loading and preprocessing utilities
- utils: General utility functions
- visualization: Plotting and visualization tools
"""

__version__ = "1.0.0"
__author__ = "PyTorch HypoSVI Team"

# Import main classes for easy access
from .eikonet.models import EikoNet
from .hyposvi.locator import HypoSVILocator
from .data.loader import DataLoader
from .utils.config import Config

__all__ = [
    "EikoNet",
    "HypoSVILocator", 
    "DataLoader",
    "Config",
]
