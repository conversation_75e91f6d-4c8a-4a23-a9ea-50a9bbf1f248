"""
EikoNet training utilities.

This module provides training functionality for the EikoNet neural network,
including training loops, checkpointing, and evaluation.
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from typing import Optional, Dict, Any, Tuple
import time
import os
from pathlib import Path

from .models import EikoNet
from .loss import <PERSON><PERSON><PERSON><PERSON><PERSON>, LossTracker
from .utils import plot_solution


class EikoNetTrainer:
    """
    Trainer class for EikoNet neural network.
    
    Handles training loop, validation, checkpointing, and logging.
    
    Args:
        model: EikoNet model to train
        loss_fn: Loss function
        optimizer: PyTorch optimizer
        device: Training device
        scale: Coordinate scaling factor
    """
    
    def __init__(
        self,
        model: EikoNet,
        loss_fn: nn.Module = None,
        optimizer: optim.Optimizer = None,
        device: torch.device = None,
        scale: float = 200.0
    ):
        self.model = model
        self.scale = scale
        
        # Set device
        if device is None:
            self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        else:
            self.device = device
            
        self.model.to(self.device)
        
        # Set loss function
        if loss_fn is None:
            self.loss_fn = EikonalLoss()
        else:
            self.loss_fn = loss_fn
            
        # Set optimizer
        if optimizer is None:
            self.optimizer = optim.Adam(self.model.parameters(), lr=1e-3)
        else:
            self.optimizer = optimizer
            
        # Initialize tracking
        self.loss_tracker = LossTracker()
        self.current_epoch = 0
        
    def train_epoch(self, train_loader: DataLoader, verbose: bool = False) -> float:
        """
        Train for one epoch.

        Args:
            train_loader: Training data loader
            verbose: Whether to show batch progress

        Returns:
            Average training loss for the epoch
        """
        self.model.train()
        total_loss = 0.0
        num_batches = len(train_loader)

        for batch_idx, (x_batch, s_batch) in enumerate(train_loader):
            # Move data to device
            x_batch = x_batch.to(self.device)
            s_batch = s_batch.to(self.device)

            # Convert to Julia-style format (7, n_samples)
            if x_batch.dim() == 2 and x_batch.size(1) == 7:
                x_batch = x_batch.T
            if s_batch.dim() == 2 and s_batch.size(1) == 1:
                s_batch = s_batch.T

            # Zero gradients
            self.optimizer.zero_grad()

            # Calculate loss
            loss = self.loss_fn(x_batch, s_batch, self.model, self.scale)

            # Backward pass
            loss.backward()
            self.optimizer.step()

            total_loss += loss.item()

            # Show batch progress
            if verbose and (batch_idx + 1) % max(1, num_batches // 10) == 0:
                avg_loss = total_loss / (batch_idx + 1)
                print(f"    Batch {batch_idx+1:4d}/{num_batches} | Loss: {loss.item():.6f} | Avg: {avg_loss:.6f}")

        return total_loss / num_batches if num_batches > 0 else 0.0
        
    def validate(self, val_loader: DataLoader) -> float:
        """
        Validate the model.
        
        Args:
            val_loader: Validation data loader
            
        Returns:
            Average validation loss
        """
        self.model.eval()
        total_loss = 0.0
        num_batches = 0
        
        with torch.no_grad():
            for x_batch, s_batch in val_loader:
                # Move data to device
                x_batch = x_batch.to(self.device)
                s_batch = s_batch.to(self.device)
                
                # Convert to Julia-style format
                if x_batch.dim() == 2 and x_batch.size(1) == 7:
                    x_batch = x_batch.T
                if s_batch.dim() == 2 and s_batch.size(1) == 1:
                    s_batch = s_batch.T
                    
                # Calculate loss
                loss = self.loss_fn(x_batch, s_batch, self.model, self.scale)
                
                total_loss += loss.item()
                num_batches += 1
                
        return total_loss / num_batches if num_batches > 0 else 0.0
        
    def train(
        self,
        train_loader: DataLoader,
        val_loader: Optional[DataLoader] = None,
        n_epochs: int = 100,
        save_path: Optional[str] = None,
        checkpoint_interval: int = 10,
        early_stopping_patience: int = 20,
        verbose: bool = True
    ) -> Dict[str, Any]:
        """
        Train the model.
        
        Args:
            train_loader: Training data loader
            val_loader: Validation data loader (optional)
            n_epochs: Number of training epochs
            save_path: Path to save best model
            checkpoint_interval: Interval for saving checkpoints
            early_stopping_patience: Patience for early stopping
            verbose: Whether to print progress
            
        Returns:
            Training history dictionary
        """
        best_val_loss = float('inf')
        patience_counter = 0
        
        if verbose:
            print(f"Training EikoNet for {n_epochs} epochs on {self.device}")
            print(f"Model parameters: {sum(p.numel() for p in self.model.parameters()):,}")
            
        start_time = time.time()
        
        for epoch in range(n_epochs):
            self.current_epoch = epoch
            
            # Training
            train_loss = self.train_epoch(train_loader, verbose=verbose)
            
            # Validation
            val_loss = None
            if val_loader is not None:
                val_loss = self.validate(val_loader)
                
            # Update tracking
            self.loss_tracker.update(train_loss, val_loss, epoch)
            
            # Check for best model
            current_loss = val_loss if val_loss is not None else train_loss
            if current_loss < best_val_loss:
                best_val_loss = current_loss
                patience_counter = 0
                
                # Save best model
                if save_path is not None:
                    self.save_model(save_path)
                    
            else:
                patience_counter += 1
                
            # Print progress - 显示每一轮的进度
            if verbose:
                elapsed = time.time() - start_time
                if val_loss is not None:
                    print(f"Epoch {epoch+1:4d}/{n_epochs} | "
                          f"Train Loss: {train_loss:.6f} | "
                          f"Val Loss: {val_loss:.6f} | "
                          f"Best: {best_val_loss:.6f} | "
                          f"Time: {elapsed:.1f}s")
                else:
                    print(f"Epoch {epoch+1:4d}/{n_epochs} | "
                          f"Train Loss: {train_loss:.6f} | "
                          f"Time: {elapsed:.1f}s")
                    
            # Early stopping
            if patience_counter >= early_stopping_patience:
                if verbose:
                    print(f"Early stopping at epoch {epoch+1}")
                break
                
            # Checkpoint
            if (epoch + 1) % checkpoint_interval == 0 and save_path is not None:
                checkpoint_path = save_path.replace('.pth', f'_epoch_{epoch+1}.pth')
                self.save_model(checkpoint_path)
                
        total_time = time.time() - start_time
        
        if verbose:
            print(f"Training completed in {total_time:.1f}s")
            print(f"Best validation loss: {best_val_loss:.6f}")
            
        return {
            'train_losses': self.loss_tracker.train_losses,
            'val_losses': self.loss_tracker.val_losses,
            'best_loss': best_val_loss,
            'total_time': total_time,
            'epochs_trained': epoch + 1
        }
        
    def save_model(self, filepath: str):
        """
        Save model state.
        
        Args:
            filepath: Path to save the model
        """
        # Create directory if it doesn't exist
        Path(filepath).parent.mkdir(parents=True, exist_ok=True)
        
        state = {
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scale': self.scale,
            'epoch': self.current_epoch,
            'loss_tracker': {
                'train_losses': self.loss_tracker.train_losses,
                'val_losses': self.loss_tracker.val_losses,
                'best_loss': self.loss_tracker.best_loss,
                'best_epoch': self.loss_tracker.best_epoch
            }
        }
        
        torch.save(state, filepath)
        
    def load_model(self, filepath: str):
        """
        Load model state.
        
        Args:
            filepath: Path to load the model from
        """
        state = torch.load(filepath, map_location=self.device)
        
        self.model.load_state_dict(state['model_state_dict'])
        self.optimizer.load_state_dict(state['optimizer_state_dict'])
        self.scale = state.get('scale', self.scale)
        self.current_epoch = state.get('epoch', 0)
        
        # Restore loss tracking
        if 'loss_tracker' in state:
            lt = state['loss_tracker']
            self.loss_tracker.train_losses = lt.get('train_losses', [])
            self.loss_tracker.val_losses = lt.get('val_losses', [])
            self.loss_tracker.best_loss = lt.get('best_loss', float('inf'))
            self.loss_tracker.best_epoch = lt.get('best_epoch', 0)


def main():
    """Main training function for command line usage."""
    import argparse
    import json
    
    parser = argparse.ArgumentParser(description='Train EikoNet model')
    parser.add_argument('--config', type=str, required=True, help='Configuration file')
    parser.add_argument('--data-dir', type=str, default='.', help='Data directory')
    parser.add_argument('--output-dir', type=str, default='.', help='Output directory')
    
    args = parser.parse_args()
    
    # Load configuration
    with open(args.config, 'r') as f:
        config = json.load(f)
        
    print("EikoNet training script - configuration loaded")
    print("This is a placeholder for the full training pipeline")
    

if __name__ == "__main__":
    main()
