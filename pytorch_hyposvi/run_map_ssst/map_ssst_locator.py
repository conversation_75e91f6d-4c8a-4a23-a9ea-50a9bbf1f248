#!/usr/bin/env python3
"""
MAP和SSST地震重定位器

实现基于EikoNet神经网络的MAP (Maximum A Posteriori) 和 SSST (Station-Specific Static Time corrections) 
地震重定位方法。

主要功能:
1. 使用EikoNet神经网络预测地震走时
2. MAP方法进行地震定位
3. SSST方法计算台站静态时间校正
4. 迭代优化地震位置和台站校正
"""

import sys
import os
import json
import time
import numpy as np
import pandas as pd
import torch
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
from sklearn.neighbors import NearestNeighbors
import warnings
warnings.filterwarnings('ignore')

# 添加源代码路径
script_dir = Path(__file__).parent
src_dir = script_dir.parent / "src"
if str(src_dir.resolve()) not in sys.path:
    sys.path.insert(0, str(src_dir.resolve()))

# 导入必要的模块
from eikonet.models import EikoNet
from data.loader import PhaseDataLoader, StationDataLoader
from data.transforms import LLAToENU, ENUToLLA
from hyposvi.locator import HypoSVILocator


class MAPSSTLocator:
    """
    MAP和SSST地震重定位器
    
    结合MAP定位方法和SSST台站校正的迭代地震重定位算法
    """
    
    def __init__(self, config_file: str, model_file: str):
        """
        初始化定位器
        
        Args:
            config_file: 配置文件路径
            model_file: EikoNet模型文件路径
        """
        self.config_file = config_file
        self.model_file = model_file
        
        # 加载配置
        with open(config_file, 'r') as f:
            self.config = json.load(f)
        
        # 设置设备
        if self.config.get('device', {}).get('use_cuda', True) and torch.cuda.is_available():
            self.device = torch.device(f"cuda:{self.config.get('device', {}).get('cuda_device', 0)}")
        else:
            self.device = torch.device('cpu')
        
        print(f"🔧 使用设备: {self.device}")
        
        # 初始化组件
        self.eikonet_model = None
        self.phase_loader = None
        self.station_loader = None
        self.coord_transform = None
        self.map_locator = None
        
        # SSST相关
        self.ssst_corrections = {}  # 台站静态时间校正
        self.residuals_history = []  # 残差历史
        
    def load_model_and_data(self, phase_file: str, station_file: str, catalog_file: str = None):
        """
        加载模型和数据
        
        Args:
            phase_file: 震相文件路径
            station_file: 台站文件路径
            catalog_file: 地震目录文件路径
        """
        print("📂 加载模型和数据...")
        
        # 加载EikoNet模型
        print(f"   - 加载EikoNet模型: {self.model_file}")
        checkpoint = torch.load(self.model_file, map_location=self.device)

        # 检查是否是checkpoint格式
        if isinstance(checkpoint, dict) and 'model_state_dict' in checkpoint:
            # 需要重新构建模型
            from eikonet.models import EikoNet

            # 使用与训练时相同的配置创建模型
            scale = checkpoint.get('scale', self.config['data']['scale'])

            self.eikonet_model = EikoNet(
                scale=scale,
                hidden_dims=None,  # 使用默认配置
                activation="elu",   # 与Julia版本一致
                use_skip_connections=True
            )

            self.eikonet_model.load_state_dict(checkpoint['model_state_dict'])
            self.eikonet_model.to(self.device)
            self.eikonet_model.eval()

            print(f"   - 从checkpoint加载模型 (epoch: {checkpoint.get('epoch', 'unknown')}, scale: {scale})")
        else:
            # 直接是模型对象
            self.eikonet_model = checkpoint
            self.eikonet_model.to(self.device)
            self.eikonet_model.eval()
        
        # 创建MAP定位器
        print("   - 创建MAP定位器...")
        self.map_locator = HypoSVILocator(self.config_file, self.model_file)
        self.map_locator.load_data(phase_file, station_file)
        
        # 获取数据加载器
        self.phase_loader = self.map_locator.phase_loader
        self.station_loader = self.map_locator.station_loader
        self.coord_transform = self.map_locator.preprocessor.coord_transform
        
        # 加载原始地震目录
        if catalog_file:
            print(f"   - 加载地震目录: {catalog_file}")
            self.original_catalog = pd.read_csv(catalog_file)
        else:
            self.original_catalog = None
        
        print("✅ 模型和数据加载完成")
        
    def calculate_travel_time_with_eikonet(self, src_east: float, src_north: float, src_depth_m: float,
                                         rec_east: float, rec_north: float, rec_depth_m: float,
                                         phase_type: str) -> float:
        """
        使用EikoNet计算理论走时
        
        Args:
            src_east, src_north, src_depth_m: 震源坐标 (米)
            rec_east, rec_north, rec_depth_m: 台站坐标 (米)
            phase_type: 震相类型 ('P' 或 'S')
            
        Returns:
            理论走时 (秒)
        """
        # 坐标缩放（转换为km并缩放）
        scale = self.config['data']['scale']
        
        x_src_scaled = (src_east / 1000.0) / scale
        y_src_scaled = (src_north / 1000.0) / scale
        z_src_scaled = (src_depth_m / 1000.0) / scale
        
        x_rec_scaled = (rec_east / 1000.0) / scale
        y_rec_scaled = (rec_north / 1000.0) / scale
        z_rec_scaled = (rec_depth_m / 1000.0) / scale
        
        # 震相类型编码（P=0, S=1）
        phase_code = 0.0 if phase_type == 'P' else 1.0
        
        # 构建输入张量
        x_input = torch.tensor([
            x_src_scaled, y_src_scaled, z_src_scaled,
            x_rec_scaled, y_rec_scaled, z_rec_scaled,
            phase_code
        ], dtype=torch.float32, device=self.device).unsqueeze(0)  # (1, 7)
        
        # 使用EikoNet计算理论走时
        with torch.no_grad():
            travel_time = self.eikonet_model(x_input.T).item()  # 转置为(7, 1)格式
        
        return travel_time
    
    def locate_event_with_map(self, evid: int, initial_location: Optional[Dict] = None,
                             original_prior: Optional[Dict] = None) -> Optional[Dict]:
        """
        使用MAP方法定位单个地震事件
        
        Args:
            evid: 事件ID
            initial_location: 初始位置猜测
            original_prior: 原始先验信息
            
        Returns:
            定位结果字典
        """
        try:
            # 使用MAP定位器进行定位
            result = self.map_locator.locate_single_event(evid, initial_location, original_prior)
            
            if result is not None:
                # 转换为地理坐标
                geo_coords = self._convert_to_geographic_coordinates(result)
                
                # 合并结果
                combined_result = {**result, **geo_coords}
                combined_result['evid'] = evid
                combined_result['method'] = 'MAP'
                
                return combined_result
            
            return None
            
        except Exception as e:
            print(f"   ❌ MAP定位失败 (事件 {evid}): {e}")
            return None
    
    def _convert_to_geographic_coordinates(self, result: Dict) -> Dict:
        """
        将定位结果转换为地理坐标
        
        Args:
            result: 定位结果字典
            
        Returns:
            地理坐标字典
        """
        hypocenter = result['hypocenter']
        scale = self.config['data']['scale']
        
        # 转换主要位置坐标
        east_m = hypocenter[0] * 1000.0 * scale
        north_m = hypocenter[1] * 1000.0 * scale
        depth_m = hypocenter[2] * 1000.0 * scale
        depth_km = depth_m / 1000.0
        
        # 创建逆变换器
        coordinate_bounds = self.config['data']['coordinate_bounds']
        enu_to_lla = ENUToLLA(
            ref_lat=coordinate_bounds['lat_min'],
            ref_lon=coordinate_bounds['lon_min'],
            ref_alt=0.0
        )
        
        # 转换为经纬度
        lat, lon, _ = enu_to_lla.transform(
            np.array([east_m]), np.array([north_m]), np.array([0.0])
        )
        
        # 处理不确定性
        if 'uncertainties' in result and isinstance(result['uncertainties'], dict):
            uncertainties = result['uncertainties']
            east_std_m = uncertainties.get('x', 0.0) * 1000.0 * scale
            north_std_m = uncertainties.get('y', 0.0) * 1000.0 * scale
            depth_std_m = uncertainties.get('z', 0.0) * 1000.0 * scale
        else:
            east_std_m = north_std_m = depth_std_m = 0.0
        
        depth_std_km = depth_std_m / 1000.0
        
        # 转换不确定性为地理坐标
        lat_rad = np.radians(lat[0])
        R_earth = 6371000.0
        
        lat_std_deg = north_std_m / R_earth * 180.0 / np.pi
        lon_std_deg = east_std_m / (R_earth * np.cos(lat_rad)) * 180.0 / np.pi
        
        return {
            'longitude': lon[0],
            'latitude': lat[0],
            'depth_km': depth_km,
            'longitude_std': lon_std_deg,
            'latitude_std': lat_std_deg,
            'depth_std_km': depth_std_km,
            'east_m': east_m,
            'north_m': north_m,
            'east_std_m': east_std_m,
            'north_std_m': north_std_m
        }

    def compute_residuals(self, locations_df: pd.DataFrame, phases_df: pd.DataFrame) -> pd.DataFrame:
        """
        计算地震定位残差

        Args:
            locations_df: 地震位置DataFrame
            phases_df: 震相数据DataFrame

        Returns:
            残差DataFrame
        """
        print("📊 计算地震定位残差...")

        residuals_list = []

        # 预处理台站坐标
        station_coords = {}
        for _, station in self.station_loader.data.iterrows():
            east, north, up = self.coord_transform.transform(
                station['latitude'], station['longitude'], station['elevation']
            )
            station_coords[station['station']] = {
                'east': east,
                'north': north,
                'depth': station['elevation']
            }

        for _, location in locations_df.iterrows():
            evid = location['evid']

            # 获取该事件的震相
            event_phases = phases_df[phases_df['evid'] == evid]
            if len(event_phases) == 0:
                continue

            # 震源坐标
            src_east = location['east_m']
            src_north = location['north_m']
            src_depth_m = location['depth_km'] * 1000.0

            # 发震时刻 - 需要从原始目录获取
            if self.original_catalog is not None:
                original_event = self.original_catalog[self.original_catalog['evid'] == evid]
                if not original_event.empty:
                    origin_time = pd.to_datetime(original_event.iloc[0]['time'])
                else:
                    # 如果没有原始时间，使用第一个震相时间减去估计走时
                    first_phase = event_phases.iloc[0]
                    first_phase_time = pd.to_datetime(first_phase['time'])
                    # 估计一个合理的走时（假设10秒）
                    origin_time = first_phase_time - timedelta(seconds=10)
            else:
                # 使用定位结果中的时间（如果有）
                origin_time_str = location.get('time', location.get('origin_time'))
                if origin_time_str:
                    origin_time = pd.to_datetime(origin_time_str)
                else:
                    # 使用第一个震相时间减去估计走时
                    first_phase = event_phases.iloc[0]
                    first_phase_time = pd.to_datetime(first_phase['time'])
                    origin_time = first_phase_time - timedelta(seconds=10)

            for _, phase in event_phases.iterrows():
                station_name = phase['station']
                phase_type = phase['phase']

                if station_name not in station_coords:
                    continue

                # 台站坐标
                rec_coords = station_coords[station_name]

                # 计算理论走时
                theoretical_time = self.calculate_travel_time_with_eikonet(
                    src_east, src_north, src_depth_m,
                    rec_coords['east'], rec_coords['north'], rec_coords['depth'],
                    phase_type
                )

                # 观测到时
                observed_time = pd.to_datetime(phase['time'])

                # 计算残差 (观测 - 理论)
                observed_travel_time = (observed_time - origin_time).total_seconds()
                residual = observed_travel_time - theoretical_time

                # 应用SSST校正（如果存在）
                ssst_key = f"{station_name}_{phase_type}"
                if ssst_key in self.ssst_corrections:
                    residual -= self.ssst_corrections[ssst_key]

                residuals_list.append({
                    'evid': evid,
                    'station': station_name,
                    'phase': phase_type,
                    'network': phase.get('network', ''),
                    'observed_time': observed_time,
                    'theoretical_time': theoretical_time,
                    'observed_travel_time': observed_travel_time,
                    'residual': residual,
                    'ssst_correction': self.ssst_corrections.get(ssst_key, 0.0),
                    'corrected_residual': residual
                })

        residuals_df = pd.DataFrame(residuals_list)
        print(f"   ✅ 计算了 {len(residuals_df)} 条残差记录")

        return residuals_df

    def compute_ssst_corrections(self, locations_df: pd.DataFrame, residuals_df: pd.DataFrame) -> Dict[str, float]:
        """
        计算SSST (Station-Specific Static Time) 校正

        Args:
            locations_df: 地震位置DataFrame
            residuals_df: 残差DataFrame

        Returns:
            SSST校正字典 {station_phase: correction}
        """
        print("🔧 计算SSST台站静态时间校正...")

        ssst_config = self.config['ssst']
        ssst_mode = ssst_config.get('ssst_mode', 'knn').lower()
        k_nn = ssst_config.get('k-NN', 30)
        min_radius = ssst_config.get('min_ssst_radius', 0.5)  # km
        max_radius = ssst_config.get('max_ssst_radius', 8.0)  # km

        new_corrections = {}

        # 获取所有台站-震相组合
        station_phases = residuals_df.groupby(['station', 'phase'])

        print(f"   - 处理 {len(station_phases)} 个台站-震相组合")
        print(f"   - SSST模式: {ssst_mode}")
        print(f"   - k-NN: {k_nn}, 半径范围: {min_radius}-{max_radius} km")

        for (station, phase), group in station_phases:
            if len(group) < 3:  # 至少需要3个观测
                continue

            ssst_key = f"{station}_{phase}"

            if ssst_mode == 'knn':
                # k-NN方法
                correction = self._compute_ssst_knn(group, locations_df, k_nn)
            elif ssst_mode == 'shrinking':
                # 收缩半径方法
                correction = self._compute_ssst_shrinking(group, locations_df, min_radius, max_radius)
            else:
                # 简单平均方法
                correction = group['residual'].median()

            new_corrections[ssst_key] = correction

        print(f"   ✅ 计算了 {len(new_corrections)} 个SSST校正")

        return new_corrections

    def _compute_ssst_knn(self, residuals_group: pd.DataFrame, locations_df: pd.DataFrame, k: int) -> float:
        """
        使用k-NN方法计算SSST校正

        Args:
            residuals_group: 该台站-震相的残差组
            locations_df: 地震位置DataFrame
            k: 近邻数量

        Returns:
            SSST校正值
        """
        if len(residuals_group) < k:
            return residuals_group['residual'].median()

        # 获取地震位置
        event_locations = []
        residuals = []

        for _, row in residuals_group.iterrows():
            evid = row['evid']
            location = locations_df[locations_df['evid'] == evid]
            if len(location) > 0:
                loc = location.iloc[0]
                event_locations.append([loc['longitude'], loc['latitude'], loc['depth_km']])
                residuals.append(row['residual'])

        if len(event_locations) < k:
            return np.median(residuals)

        # 使用k-NN计算加权平均
        event_locations = np.array(event_locations)
        residuals = np.array(residuals)

        # 构建k-NN模型
        nbrs = NearestNeighbors(n_neighbors=min(k, len(event_locations)), metric='euclidean')
        nbrs.fit(event_locations)

        # 计算每个点的k近邻权重平均
        weighted_residuals = []

        for i, location in enumerate(event_locations):
            distances, indices = nbrs.kneighbors([location])

            # 计算权重（距离的倒数）
            weights = 1.0 / (distances[0] + 1e-8)
            weights = weights / weights.sum()

            # 加权平均
            weighted_residual = np.sum(weights * residuals[indices[0]])
            weighted_residuals.append(weighted_residual)

        return np.median(weighted_residuals)

    def _compute_ssst_shrinking(self, residuals_group: pd.DataFrame, locations_df: pd.DataFrame,
                               min_radius: float, max_radius: float) -> float:
        """
        使用收缩半径方法计算SSST校正

        Args:
            residuals_group: 该台站-震相的残差组
            locations_df: 地震位置DataFrame
            min_radius: 最小半径 (km)
            max_radius: 最大半径 (km)

        Returns:
            SSST校正值
        """
        # 简化实现：使用距离加权平均
        event_locations = []
        residuals = []

        for _, row in residuals_group.iterrows():
            evid = row['evid']
            location = locations_df[locations_df['evid'] == evid]
            if len(location) > 0:
                loc = location.iloc[0]
                event_locations.append([loc['longitude'], loc['latitude'], loc['depth_km']])
                residuals.append(row['residual'])

        if len(event_locations) < 2:
            return np.median(residuals) if residuals else 0.0

        event_locations = np.array(event_locations)
        residuals = np.array(residuals)

        # 计算距离权重
        center = np.mean(event_locations, axis=0)
        distances = np.linalg.norm(event_locations - center, axis=1)

        # 应用半径约束
        valid_mask = (distances >= min_radius) & (distances <= max_radius)
        if not np.any(valid_mask):
            return np.median(residuals)

        valid_distances = distances[valid_mask]
        valid_residuals = residuals[valid_mask]

        # 距离权重
        weights = 1.0 / (valid_distances + 1e-8)
        weights = weights / weights.sum()

        return np.sum(weights * valid_residuals)

    def apply_ssst_corrections(self, phases_df: pd.DataFrame) -> pd.DataFrame:
        """
        应用SSST校正到震相数据

        Args:
            phases_df: 原始震相DataFrame

        Returns:
            应用校正后的震相DataFrame
        """
        print("🔄 应用SSST校正到震相数据...")

        corrected_phases = phases_df.copy()
        corrections_applied = 0

        for idx, row in corrected_phases.iterrows():
            station = row['station']
            phase = row['phase']
            ssst_key = f"{station}_{phase}"

            if ssst_key in self.ssst_corrections:
                # 应用时间校正
                original_time = pd.to_datetime(row['time'])
                corrected_time = original_time - timedelta(seconds=self.ssst_corrections[ssst_key])
                corrected_phases.at[idx, 'time'] = corrected_time.strftime('%Y-%m-%dT%H:%M:%S.%f')[:-3]
                corrections_applied += 1

        print(f"   ✅ 应用了 {corrections_applied} 个SSST校正")

        return corrected_phases

    def run_map_ssst_iteration(self, event_ids: List[int], output_dir: str) -> Dict[str, Any]:
        """
        运行MAP-SSST迭代定位

        Args:
            event_ids: 要处理的事件ID列表
            output_dir: 输出目录

        Returns:
            处理结果统计
        """
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)

        print(f"🚀 开始MAP-SSST迭代定位")
        print(f"   - 事件数量: {len(event_ids)}")
        print(f"   - 输出目录: {output_dir}")

        # 获取配置参数
        n_static_iter = self.config['ssst'].get('n_static_iter', 2)
        n_ssst_iter = self.config['ssst'].get('n_ssst_iter', 2)

        print(f"   - 静态迭代次数: {n_static_iter}")
        print(f"   - SSST迭代次数: {n_ssst_iter}")

        # 初始化
        current_phases = self.phase_loader.data.copy()
        all_results = []
        iteration_stats = []

        # 第一步：初始MAP定位（无SSST校正）
        print("\n" + "="*60)
        print("📍 第1步: 初始MAP定位（无SSST校正）")

        initial_locations = self._locate_events_batch(event_ids, "INITIAL_MAP")
        if len(initial_locations) == 0:
            print("❌ 初始MAP定位失败")
            return {}

        # 保存初始结果
        initial_df = pd.DataFrame(initial_locations)
        initial_file = output_path / "initial_map_locations.csv"
        initial_df.to_csv(initial_file, index=False)
        print(f"   ✅ 初始定位结果: {initial_file}")

        current_locations = initial_df

        # 静态迭代（仅MAP定位）
        for static_iter in range(n_static_iter):
            print(f"\n" + "="*60)
            print(f"📍 静态迭代 {static_iter + 1}/{n_static_iter}")

            # 重新定位
            locations = self._locate_events_batch(event_ids, f"STATIC_{static_iter + 1}")
            if len(locations) == 0:
                print(f"   ❌ 静态迭代 {static_iter + 1} 失败")
                break

            current_locations = pd.DataFrame(locations)

            # 保存结果
            static_file = output_path / f"static_iter_{static_iter + 1}_locations.csv"
            current_locations.to_csv(static_file, index=False)
            print(f"   ✅ 静态迭代 {static_iter + 1} 结果: {static_file}")

        # SSST迭代
        for ssst_iter in range(n_ssst_iter):
            print(f"\n" + "="*60)
            print(f"🔧 SSST迭代 {ssst_iter + 1}/{n_ssst_iter}")

            # 计算残差
            residuals_df = self.compute_residuals(current_locations, current_phases)

            # 计算SSST校正
            new_corrections = self.compute_ssst_corrections(current_locations, residuals_df)

            # 更新SSST校正
            self.ssst_corrections.update(new_corrections)

            # 应用SSST校正到震相数据
            current_phases = self.apply_ssst_corrections(self.phase_loader.data)

            # 使用校正后的震相数据重新定位
            # 临时更新phase_loader的数据
            original_phases = self.phase_loader.data.copy()
            self.phase_loader.data = current_phases

            try:
                locations = self._locate_events_batch(event_ids, f"SSST_{ssst_iter + 1}")
                if len(locations) > 0:
                    current_locations = pd.DataFrame(locations)

                    # 保存结果
                    ssst_file = output_path / f"ssst_iter_{ssst_iter + 1}_locations.csv"
                    current_locations.to_csv(ssst_file, index=False)
                    print(f"   ✅ SSST迭代 {ssst_iter + 1} 结果: {ssst_file}")

                    # 保存SSST校正
                    corrections_file = output_path / f"ssst_iter_{ssst_iter + 1}_corrections.csv"
                    corrections_df = pd.DataFrame([
                        {'station_phase': k, 'correction': v}
                        for k, v in self.ssst_corrections.items()
                    ])
                    corrections_df.to_csv(corrections_file, index=False)
                    print(f"   ✅ SSST校正: {corrections_file}")

                    # 保存残差
                    residuals_file = output_path / f"ssst_iter_{ssst_iter + 1}_residuals.csv"
                    residuals_df.to_csv(residuals_file, index=False)
                    print(f"   ✅ 残差数据: {residuals_file}")

                    # 计算统计信息
                    rms_residual = np.sqrt(np.mean(residuals_df['residual']**2))
                    mad_residual = np.median(np.abs(residuals_df['residual']))

                    print(f"   📊 残差统计:")
                    print(f"      - RMS: {rms_residual:.3f} s")
                    print(f"      - MAD: {mad_residual:.3f} s")
                    print(f"      - SSST校正数: {len(self.ssst_corrections)}")

                    iteration_stats.append({
                        'iteration': f"SSST_{ssst_iter + 1}",
                        'rms_residual': rms_residual,
                        'mad_residual': mad_residual,
                        'n_corrections': len(self.ssst_corrections),
                        'n_events': len(current_locations)
                    })
                else:
                    print(f"   ❌ SSST迭代 {ssst_iter + 1} 定位失败")

            finally:
                # 恢复原始震相数据
                self.phase_loader.data = original_phases

        # 保存最终结果
        print(f"\n" + "="*60)
        print("💾 保存最终结果...")

        final_file = output_path / "final_map_ssst_locations.csv"
        current_locations.to_csv(final_file, index=False)
        print(f"   ✅ 最终定位结果: {final_file}")

        # 保存最终SSST校正
        final_corrections_file = output_path / "final_ssst_corrections.csv"
        final_corrections_df = pd.DataFrame([
            {'station_phase': k, 'correction': v}
            for k, v in self.ssst_corrections.items()
        ])
        final_corrections_df.to_csv(final_corrections_file, index=False)
        print(f"   ✅ 最终SSST校正: {final_corrections_file}")

        # 保存迭代统计
        if iteration_stats:
            stats_file = output_path / "iteration_statistics.csv"
            stats_df = pd.DataFrame(iteration_stats)
            stats_df.to_csv(stats_file, index=False)
            print(f"   ✅ 迭代统计: {stats_file}")

        # 生成最终统计
        final_stats = {
            'total_events': len(current_locations),
            'total_ssst_corrections': len(self.ssst_corrections),
            'final_rms_residual': iteration_stats[-1]['rms_residual'] if iteration_stats else 0,
            'final_mad_residual': iteration_stats[-1]['mad_residual'] if iteration_stats else 0,
            'output_files': {
                'locations': str(final_file),
                'corrections': str(final_corrections_file),
                'statistics': str(stats_file) if iteration_stats else None
            }
        }

        return final_stats

    def _locate_events_batch(self, event_ids: List[int], method_tag: str) -> List[Dict]:
        """
        批量定位事件

        Args:
            event_ids: 事件ID列表
            method_tag: 方法标签

        Returns:
            定位结果列表
        """
        print(f"   🎯 批量定位 {len(event_ids)} 个事件 ({method_tag})")

        results = []
        successful = 0
        failed = 0

        for i, evid in enumerate(event_ids):
            if (i + 1) % 50 == 0:
                print(f"      进度: {i + 1}/{len(event_ids)} ({(i + 1)/len(event_ids)*100:.1f}%)")

            # 获取原始位置作为初始猜测
            initial_location = None
            original_prior = None

            if self.original_catalog is not None:
                original_event = self.original_catalog[self.original_catalog['evid'] == evid]
                if not original_event.empty:
                    original_lat = original_event.iloc[0]['latitude']
                    original_lon = original_event.iloc[0]['longitude']
                    original_depth = original_event.iloc[0]['depth']

                    initial_location = {
                        'latitude': original_lat,
                        'longitude': original_lon,
                        'depth': original_depth
                    }

                    original_prior = {
                        'latitude': original_lat,
                        'longitude': original_lon,
                        'depth': original_depth
                    }

            # 定位事件
            result = self.locate_event_with_map(evid, initial_location, original_prior)

            if result is not None:
                result['method_tag'] = method_tag
                results.append(result)
                successful += 1
            else:
                failed += 1

        print(f"      ✅ 成功: {successful}, 失败: {failed}")

        return results

    def generate_relocated_phases(self, locations_df: pd.DataFrame, output_dir: str) -> pd.DataFrame:
        """
        生成重定位后的震相文件

        Args:
            locations_df: 重定位结果DataFrame
            output_dir: 输出目录

        Returns:
            重定位震相DataFrame
        """
        print("📋 生成重定位后的震相文件...")

        output_path = Path(output_dir)

        # 预处理台站坐标
        station_coords = {}
        for _, station in self.station_loader.data.iterrows():
            east, north, up = self.coord_transform.transform(
                station['latitude'], station['longitude'], station['elevation']
            )
            station_coords[station['station']] = {
                'east': east,
                'north': north,
                'depth': station['elevation']
            }

        relocated_phases = []

        for _, location in locations_df.iterrows():
            evid = location['evid']

            # 获取该事件的原始震相
            event_phases = self.phase_loader.data[self.phase_loader.data['evid'] == evid]
            if len(event_phases) == 0:
                continue

            # 重定位后的震源位置
            src_east = location['east_m']
            src_north = location['north_m']
            src_depth_m = location['depth_km'] * 1000.0

            # 获取原始发震时刻（如果有的话）
            if self.original_catalog is not None:
                original_event = self.original_catalog[self.original_catalog['evid'] == evid]
                if not original_event.empty:
                    original_origin_time = pd.to_datetime(original_event.iloc[0]['time'])
                else:
                    # 使用重定位结果中的时间
                    original_origin_time = pd.to_datetime(location.get('time', '2000-01-01T00:00:00'))
            else:
                original_origin_time = pd.to_datetime(location.get('time', '2000-01-01T00:00:00'))

            # 为每个震相重新计算走时
            for _, phase in event_phases.iterrows():
                station_name = phase['station']
                phase_type = phase['phase']

                if station_name not in station_coords:
                    continue

                # 获取台站坐标
                rec_coords = station_coords[station_name]

                # 使用EikoNet计算理论走时
                travel_time = self.calculate_travel_time_with_eikonet(
                    src_east, src_north, src_depth_m,
                    rec_coords['east'], rec_coords['north'], rec_coords['depth'],
                    phase_type
                )

                # 计算新的震相到达时间
                new_arrival_time = original_origin_time + timedelta(seconds=travel_time)

                # 创建重定位后的震相记录
                relocated_phase = {
                    'time': new_arrival_time.strftime('%Y-%m-%dT%H:%M:%S.%f')[:-3],
                    'evid': evid,
                    'arid': phase.get('arid', 0),
                    'phase': phase_type,
                    'network': phase.get('network', ''),
                    'station': station_name,
                    # 添加重定位相关信息
                    'relocated_longitude': location['longitude'],
                    'relocated_latitude': location['latitude'],
                    'relocated_depth_km': location['depth_km'],
                    'relocation_method': 'MAP_SSST',
                    'rms_residual': location.get('rms_residual', 0),
                    # 添加走时信息
                    'theoretical_travel_time': travel_time,
                    'original_time': phase['time'],
                    'calculation_method': 'EikoNet_MAP_SSST'
                }

                relocated_phases.append(relocated_phase)

        # 创建DataFrame并保存
        relocated_phases_df = pd.DataFrame(relocated_phases)

        if len(relocated_phases_df) > 0:
            # 保存完整的重定位震相文件
            full_output_file = output_path / "relocated_phases_map_ssst_full.csv"
            relocated_phases_df.to_csv(full_output_file, index=False)
            print(f"   ✅ 完整重定位震相文件: {full_output_file}")

            # 保存简化版本
            simple_phases_df = relocated_phases_df[['time', 'evid', 'arid', 'phase', 'network', 'station']].copy()
            simple_output_file = output_path / "relocated_phases_map_ssst_simple.csv"
            simple_phases_df.to_csv(simple_output_file, index=False)
            print(f"   ✅ 简化重定位震相文件: {simple_output_file}")

            print(f"   📊 生成了 {len(relocated_phases_df)} 条重定位震相记录")

            return relocated_phases_df
        else:
            print("   ❌ 没有生成重定位震相数据")
            return pd.DataFrame()
