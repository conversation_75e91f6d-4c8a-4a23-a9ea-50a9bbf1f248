{"data": {"station_file": "/home/<USER>/run_eikonet_hyposvi/hyposvi/station.csv", "phase_file": "/home/<USER>/run_eikonet_hyposvi/hyposvi/phase.csv", "catalog_file": "/home/<USER>/run_eikonet_hyposvi/hyposvi/pre_cat.csv", "catalog_outfile": "/home/<USER>/run_eikonet_hyposvi/run_new_svi/parallel_catalog.csv", "residual_outfile": "/home/<USER>/run_eikonet_hyposvi/run_new_svi/parallel_residuals.csv", "ssst_outfile": "/home/<USER>/run_eikonet_hyposvi/run_new_svi/parallel_ssst_corrections.csv", "coordinate_bounds": {"lon_min": -118.2, "lat_min": 35.3, "z_min": 0.0, "z_max": 25.0}, "scale": 200.0}, "model": {"eikonet_model_file": "/home/<USER>/run_eikonet_hyposvi/pytorch_hyposvi/run_eikonet/eikonet_model.pth", "prevent_airquakes": true}, "inversion": {"method": "SVI", "n_epochs": 150, "learning_rate": 0.001, "iter_tol": 0.001, "optimizer": "adam", "lr_scheduler": "cosine", "warmup_epochs": 10}, "svi": {"n_particles": 20, "n_epochs": 150, "learning_rate": 0.001, "svi_iter_tol": 0.05, "lr_decay_interval": 30, "kernel_fn": "RBF", "svi_verbose": 0, "adaptive_particles": false, "min_particles": 15, "max_particles": 25}, "uncertainty": {"pick_unc_p": 0.08, "pick_unc_s": 0.12, "n_phase_min_pick_std": 12, "likelihood_fn": "huber", "barron_alpha": 1.2, "adaptive_uncertainty": false, "distance_weighting": false, "quality_weighting": false}, "prior": {"prior_z_mean": 8.0, "prior_z_std": 8.0, "prior_scale_param": 8.0, "max_src_rec_dist": 60.0, "adaptive_prior": true, "use_catalog_prior": true, "prior_strength": 0.3}, "ssst": {"n_static_iter": 2, "n_ssst_iter": 2, "ssst_mode": "knn", "k-NN": 20, "min_ssst_radius": 0.5, "max_ssst_radius": 8.0, "ssst_damping": 0.7, "outlier_threshold": 2.5}, "quality_control": {"min_phases": 6, "max_rms_threshold": 10.0, "max_distance_km": 120.0, "outlier_detection": false, "phase_residual_threshold": 8.0, "automatic_reweighting": false}, "optimization": {"multi_scale": false, "coarse_epochs": 30, "fine_epochs": 120, "convergence_check": true, "early_stopping": true, "patience": 20}, "device": {"use_cuda": true, "cuda_device": 0, "memory_management": true, "batch_processing": true}, "logging": {"log_level": "WARNING", "verbose": false, "show_progress": false, "save_diagnostics": false}, "output": {"save_residuals": true, "save_uncertainties": true, "save_ssst": false, "save_iterations": false, "save_diagnostics": false, "output_format": "csv", "generate_plots": false, "save_comparison": false}}