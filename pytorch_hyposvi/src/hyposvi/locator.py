"""
Main HypoSVI earthquake locator.

This module provides the main interface for earthquake location using
both MAP and SVI methods with EikoNet neural networks.
"""

import torch
import numpy as np
import pandas as pd
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
from pathlib import Path
import json

from eikonet.models import EikoNet, build_eikonet_from_config
from data.loader import PhaseDataLoader, StationDataLoader
from data.preprocessor import DataPreprocessor
from data.velocity_model import VelocityModel1D
from .map_solver import MAPSolver
from .svi_solver import SVISolver
from .utils import assign_likelihood, assign_kernel


class HypoSVILocator:
    """
    Main earthquake locator using HypoSVI methods.
    
    This class provides a unified interface for earthquake location using
    both MAP (Maximum A Posteriori) and SVI (Stein Variational Inference)
    methods with EikoNet neural networks for travel time prediction.
    
    Args:
        config_file: Path to configuration file
        eikonet_model_file: Path to trained EikoNet model
        device: Computation device
    """
    
    def __init__(
        self,
        config_file: Union[str, Path],
        eikonet_model_file: Union[str, Path],
        device: torch.device = None
    ):
        # Load configuration
        with open(config_file, 'r') as f:
            self.config = json.load(f)
            
        # Set device
        if device is None:
            self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        else:
            self.device = device
            
        # Load EikoNet model
        self.eikonet_model = self._load_eikonet_model(eikonet_model_file)
        
        # Initialize data preprocessor
        self.preprocessor = DataPreprocessor(
            coordinate_bounds=self.config['data']['coordinate_bounds'],
            scale=self.config['data']['scale']
        )
        
        # Initialize solvers
        self.method = self.config['inversion']['method'].upper()
        if self.method == 'MAP':
            # Merge configuration sections for MAP solver
            map_config = {**self.config['inversion']}
            if 'prior' in self.config:
                map_config.update(self.config['prior'])
            if 'uncertainty' in self.config:
                map_config.update(self.config['uncertainty'])
            if 'data' in self.config:
                map_config.update(self.config['data'])
            self.solver = MAPSolver(self.eikonet_model, map_config, self.device)
        elif self.method == 'SVI':
            # Merge configuration sections for SVI solver
            svi_config = {**self.config['inversion']}
            if 'prior' in self.config:
                svi_config.update(self.config['prior'])
            if 'uncertainty' in self.config:
                svi_config.update(self.config['uncertainty'])
            if 'data' in self.config:
                svi_config.update(self.config['data'])
            if 'svi' in self.config:
                svi_config.update(self.config['svi'])
            self.solver = SVISolver(self.eikonet_model, svi_config, self.device)
        else:
            raise ValueError(f"Unknown inversion method: {self.method}")
            
        # Results storage
        self.results = []
        
    def _load_eikonet_model(self, model_file: Union[str, Path]) -> EikoNet:
        """
        Load trained EikoNet model.
        
        Args:
            model_file: Path to model file
            
        Returns:
            Loaded EikoNet model
        """
        # Load model state
        state = torch.load(model_file, map_location=self.device)
        
        # Create model instance
        scale = state.get('scale', self.config['data']['scale'])
        model = EikoNet(scale=scale)
        
        # Load state dict
        if 'model_state_dict' in state:
            model.load_state_dict(state['model_state_dict'])
        else:
            # Assume the file contains the model state dict directly
            model.load_state_dict(state)
            
        model.to(self.device)
        model.eval()
        
        return model
        
    def load_data(
        self,
        phase_file: Union[str, Path],
        station_file: Union[str, Path]
    ) -> None:
        """
        Load phase and station data.
        
        Args:
            phase_file: Path to phase picks CSV file
            station_file: Path to station information CSV file
        """
        # Load data
        self.phase_loader = PhaseDataLoader(phase_file)
        self.station_loader = StationDataLoader(station_file)
        
        # Setup coordinate scaler
        self.preprocessor.setup_scaler(self.station_loader.data)
        
        print(f"Loaded {len(self.phase_loader.data)} phase picks")
        print(f"Loaded {len(self.station_loader.data)} stations")
        
    def locate_events(
        self,
        event_ids: Optional[List[int]] = None,
        min_phases: int = 4
    ) -> pd.DataFrame:
        """
        Locate multiple earthquakes.
        
        Args:
            event_ids: List of event IDs to locate (if None, locate all)
            min_phases: Minimum number of phases required per event
            
        Returns:
            DataFrame with location results
        """
        if not hasattr(self, 'phase_loader'):
            raise RuntimeError("Data not loaded. Call load_data() first.")
            
        # Get event IDs to process
        if event_ids is None:
            all_event_ids = self.phase_loader.get_event_ids()
        else:
            all_event_ids = event_ids
            
        # Filter events by minimum phase count
        event_phase_counts = self.phase_loader.data.groupby('evid').size()
        valid_events = event_phase_counts[event_phase_counts >= min_phases].index
        events_to_process = [eid for eid in all_event_ids if eid in valid_events]
        
        print(f"Processing {len(events_to_process)} events with >= {min_phases} phases")
        
        # Process each event
        results_list = []
        
        for i, evid in enumerate(events_to_process):
            if (i + 1) % 10 == 0 or i == 0:
                print(f"Processing event {i+1}/{len(events_to_process)}: {evid}")
                
            try:
                result = self.locate_single_event(evid)
                if result is not None:
                    result['evid'] = evid
                    results_list.append(result)
                    
            except Exception as e:
                print(f"Failed to locate event {evid}: {e}")
                continue
                
        # Convert to DataFrame
        if results_list:
            results_df = pd.DataFrame(results_list)
            self.results = results_df
            return results_df
        else:
            print("No events successfully located")
            return pd.DataFrame()
            
    def locate_single_event(self, evid: int, initial_location: Optional[Dict[str, float]] = None,
                           original_prior: Optional[Dict[str, float]] = None) -> Optional[Dict[str, Any]]:
        """
        Locate a single earthquake.

        Args:
            evid: Event ID
            initial_location: Optional initial location guess with keys 'latitude', 'longitude', 'depth'
            original_prior: Optional prior information from original catalog with keys 'latitude', 'longitude', 'depth'

        Returns:
            Dictionary with location results
        """
        try:
            # Get phase data for event
            event_phases = self.phase_loader.get_event_phases(evid)

            # Format arrivals
            X_obs, T_obs, T_ref, phase_station_data = self.preprocessor.format_arrivals(
                event_phases, self.station_loader.data
            )

            # Move to device
            X_obs = X_obs.to(self.device)
            T_obs = T_obs.to(self.device)

            # Prepare initial guess if provided
            initial_guess = None
            if initial_location is not None:
                # Convert lat/lon/depth to ENU coordinates
                lat, lon, depth = initial_location['latitude'], initial_location['longitude'], initial_location['depth']
                east, north, up = self.preprocessor.coord_transform.transform(
                    np.array([lat]), np.array([lon]), np.array([0.0])
                )
                # Apply same scaling as in data preprocessing
                scale = self.config['data']['scale']
                # Convert to km and apply scale (consistent with preprocessor)
                east_km = east[0] / 1000.0  # Convert m to km
                north_km = north[0] / 1000.0  # Convert m to km
                depth_km = depth  # Already in km

                initial_guess = torch.tensor([
                    east_km / scale,
                    north_km / scale,
                    depth_km / scale
                ], dtype=torch.float32, device=self.device)

            # Prepare prior information if provided
            prior_info = None
            if original_prior is not None:
                # Convert lat/lon/depth to ENU coordinates for prior
                prior_lat, prior_lon, prior_depth = original_prior['latitude'], original_prior['longitude'], original_prior['depth']
                prior_east, prior_north, prior_up = self.preprocessor.coord_transform.transform(
                    np.array([prior_lat]), np.array([prior_lon]), np.array([0.0])
                )
                # Apply same scaling as in data preprocessing
                scale = self.config['data']['scale']
                # Convert to km and apply scale (consistent with preprocessor)
                prior_east_km = prior_east[0] / 1000.0  # Convert m to km
                prior_north_km = prior_north[0] / 1000.0  # Convert m to km
                prior_depth_km = prior_depth  # Already in km

                prior_info = {
                    'east_scaled': prior_east_km / scale,
                    'north_scaled': prior_north_km / scale,
                    'depth_scaled': prior_depth_km / scale,
                    'depth_km': prior_depth_km  # Keep original depth in km for adaptive prior
                }

            # Locate using selected method
            if self.method == 'MAP':
                location_result = self.solver.locate_event(X_obs, T_obs, initial_guess=initial_guess, prior_info=prior_info)
            elif self.method == 'SVI':
                location_result = self.solver.locate_event(X_obs, T_obs, initial_guess=initial_guess, prior_info=prior_info)
            else:
                raise NotImplementedError(f"Method {self.method} not implemented")
                
            # Add metadata
            location_result.update({
                'evid': evid,
                'reference_time': T_ref,
                'n_phases': len(event_phases),
                'n_p_phases': (event_phases['phase'] == 'P').sum(),
                'n_s_phases': (event_phases['phase'] == 'S').sum(),
                'method': self.method
            })
            
            return location_result
            
        except Exception as e:
            print(f"Error locating event {evid}: {e}")
            return None
            
    def save_results(
        self,
        catalog_file: Optional[str] = None,
        residuals_file: Optional[str] = None
    ) -> None:
        """
        Save location results to files.
        
        Args:
            catalog_file: Path to save catalog results
            residuals_file: Path to save residuals
        """
        if not hasattr(self, 'results') or self.results.empty:
            print("No results to save")
            return
            
        # Default file names from config
        if catalog_file is None:
            catalog_file = self.config['data'].get('catalog_outfile', 'catalog_out.csv')
        if residuals_file is None:
            residuals_file = self.config['data'].get('residual_outfile', 'residuals_out.csv')
            
        # Prepare catalog data
        catalog_data = []
        residual_data = []
        
        for _, row in self.results.iterrows():
            evid = row['evid']
            hypocenter_scaled = row['hypocenter']  # This is in scaled coordinates
            origin_time_offset = row['origin_time_offset']
            reference_time = row['reference_time']

            # Calculate absolute origin time
            origin_time = reference_time + pd.Timedelta(seconds=origin_time_offset)

            # Convert scaled coordinates back to real-world coordinates
            # Following Julia version: X_best .*= 1f3 .* scale, then depth/1f3
            scale = self.config['data']['scale']

            # Convert scaled coordinates to meters first (like Julia)
            east_m = hypocenter_scaled[0] * 1000.0 * scale
            north_m = hypocenter_scaled[1] * 1000.0 * scale
            depth_m = hypocenter_scaled[2] * 1000.0 * scale

            # Convert depth from meters to km (like Julia: depth/1f3)
            depth_km = depth_m / 1000.0

            # Use inverse coordinate transformation (east_m, north_m already in meters)
            lat, lon, _ = self.preprocessor.coord_transform.inverse_transform(
                np.array([east_m]), np.array([north_m]), np.array([0.0])
            )

            catalog_entry = {
                'evid': evid,
                'origin_time': origin_time,
                'longitude': lon[0],
                'latitude': lat[0],
                'depth': depth_km,
                'n_phases': row['n_phases'],
                'rms_residual': np.sqrt(np.mean(row['residuals']**2)),
                'rms_residual_s': np.sqrt(np.mean(row['residuals']**2)),  # Add for consistency
                'method': row['method']
            }
            catalog_data.append(catalog_entry)
            
            # Residual data
            residuals = row['residuals']
            for i, residual in enumerate(residuals):
                residual_entry = {
                    'evid': evid,
                    'phase_idx': i,
                    'residual': residual
                }
                residual_data.append(residual_entry)
                
        # Save to CSV
        catalog_df = pd.DataFrame(catalog_data)
        catalog_df.to_csv(catalog_file, index=False)
        print(f"Catalog saved to {catalog_file}")
        
        if residual_data:
            residuals_df = pd.DataFrame(residual_data)
            residuals_df.to_csv(residuals_file, index=False)
            print(f"Residuals saved to {residuals_file}")
            
    def get_summary_statistics(self) -> Dict[str, Any]:
        """
        Get summary statistics of location results.
        
        Returns:
            Dictionary with summary statistics
        """
        if not hasattr(self, 'results') or self.results.empty:
            return {}
            
        # Calculate statistics
        all_residuals = np.concatenate([row['residuals'] for _, row in self.results.iterrows()])
        
        stats = {
            'n_events_located': len(self.results),
            'mean_rms_residual': np.sqrt(np.mean(all_residuals**2)),
            'median_rms_residual': np.sqrt(np.median(all_residuals**2)),
            'mean_n_phases': self.results['n_phases'].mean(),
            'depth_range': [
                self.results['hypocenter'].apply(lambda x: x[2]).min(),
                self.results['hypocenter'].apply(lambda x: x[2]).max()
            ],
            'method': self.method
        }
        
        return stats
        
    def plot_results(self, save_path: Optional[str] = None) -> None:
        """
        Plot location results.
        
        Args:
            save_path: Path to save plot
        """
        if not hasattr(self, 'results') or self.results.empty:
            print("No results to plot")
            return
            
        try:
            import matplotlib.pyplot as plt
        except ImportError:
            print("Matplotlib not available for plotting")
            return
            
        # Extract coordinates
        coords = np.array([row['hypocenter'] for _, row in self.results.iterrows()])
        x_coords = coords[:, 0]
        y_coords = coords[:, 1]
        depths = coords[:, 2]
        
        # Create plots
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        
        # Map view
        axes[0, 0].scatter(x_coords, y_coords, c=depths, cmap='viridis_r', s=50, alpha=0.7)
        axes[0, 0].set_xlabel('X (km)')
        axes[0, 0].set_ylabel('Y (km)')
        axes[0, 0].set_title('Earthquake Locations (Map View)')
        
        # Depth histogram
        axes[0, 1].hist(depths, bins=20, alpha=0.7, edgecolor='black')
        axes[0, 1].set_xlabel('Depth (km)')
        axes[0, 1].set_ylabel('Count')
        axes[0, 1].set_title('Depth Distribution')
        
        # Cross-section
        axes[1, 0].scatter(x_coords, depths, s=50, alpha=0.7)
        axes[1, 0].set_xlabel('X (km)')
        axes[1, 0].set_ylabel('Depth (km)')
        axes[1, 0].invert_yaxis()
        axes[1, 0].set_title('Cross-section (X-Z)')
        
        # Residual statistics
        all_residuals = np.concatenate([row['residuals'] for _, row in self.results.iterrows()])
        axes[1, 1].hist(all_residuals, bins=30, alpha=0.7, edgecolor='black')
        axes[1, 1].set_xlabel('Residual (s)')
        axes[1, 1].set_ylabel('Count')
        axes[1, 1].set_title('Residual Distribution')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Plot saved to {save_path}")
        else:
            plt.show()
            
        plt.close()


def main():
    """Main function for command line usage."""
    import argparse
    
    parser = argparse.ArgumentParser(description='HypoSVI earthquake location')
    parser.add_argument('--config', required=True, help='Configuration file')
    parser.add_argument('--model', required=True, help='EikoNet model file')
    parser.add_argument('--phases', required=True, help='Phase picks file')
    parser.add_argument('--stations', required=True, help='Station file')
    parser.add_argument('--output-dir', default='.', help='Output directory')
    
    args = parser.parse_args()
    
    # Initialize locator
    locator = HypoSVILocator(args.config, args.model)
    
    # Load data
    locator.load_data(args.phases, args.stations)
    
    # Locate events
    results = locator.locate_events()
    
    # Save results
    catalog_file = Path(args.output_dir) / 'catalog_out.csv'
    residuals_file = Path(args.output_dir) / 'residuals_out.csv'
    locator.save_results(str(catalog_file), str(residuals_file))
    
    # Print summary
    stats = locator.get_summary_statistics()
    print("\nLocation Summary:")
    for key, value in stats.items():
        print(f"  {key}: {value}")
        
    # Plot results
    plot_file = Path(args.output_dir) / 'location_results.png'
    locator.plot_results(str(plot_file))


if __name__ == "__main__":
    main()
