#!/usr/bin/env python3
"""
绘制P波与S波走时曲线图 - 简化版本
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os
from math import radians, cos, sin, asin, sqrt

def haversine_distance(lat1, lon1, lat2, lon2):
    """计算两点间的球面距离 (km)"""
    R = 6371  # 地球半径 km
    
    lat1, lon1, lat2, lon2 = map(radians, [lat1, lon1, lat2, lon2])
    dlat = lat2 - lat1
    dlon = lon2 - lon1
    
    a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
    c = 2 * asin(sqrt(a))
    
    return R * c

def create_travel_time_curves():
    """创建P波和S波走时曲线图"""
    
    print("正在生成P波与S波走时曲线图...")
    
    # 创建输出目录
    output_dir = "travel_time_curves"
    os.makedirs(output_dir, exist_ok=True)
    
    # 1. 创建理论走时曲线对比图
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
    fig.suptitle('P波与S波走时曲线分析', fontsize=16, fontweight='bold')
    
    # 距离范围
    distances = np.linspace(0, 300, 100)
    
    # 不同模型的走时计算
    # 简化均匀模型
    p_simple = distances / 6.0  # P波速度 6.0 km/s
    s_simple = distances / 3.5  # S波速度 3.5 km/s
    
    # 地壳模型
    p_crust = distances / 6.2  # 地壳P波速度
    s_crust = distances / 3.6  # 地壳S波速度
    
    # 上地幔模型
    p_mantle = distances / 8.0  # 上地幔P波速度
    s_mantle = distances / 4.5  # 上地幔S波速度
    
    # 第一个子图：理论模型对比
    ax1.plot(distances, p_simple, 'b-', linewidth=2, label='P波 - 简化模型 (6.0 km/s)')
    ax1.plot(distances, s_simple, 'r-', linewidth=2, label='S波 - 简化模型 (3.5 km/s)')
    ax1.plot(distances, p_crust, 'b--', linewidth=2, alpha=0.7, label='P波 - 地壳 (6.2 km/s)')
    ax1.plot(distances, s_crust, 'r--', linewidth=2, alpha=0.7, label='S波 - 地壳 (3.6 km/s)')
    ax1.plot(distances, p_mantle, 'b:', linewidth=2, alpha=0.7, label='P波 - 上地幔 (8.0 km/s)')
    ax1.plot(distances, s_mantle, 'r:', linewidth=2, alpha=0.7, label='S波 - 上地幔 (4.5 km/s)')
    
    ax1.set_xlabel('震中距 (km)', fontsize=12)
    ax1.set_ylabel('走时 (秒)', fontsize=12)
    ax1.set_title('理论走时曲线模型对比', fontsize=14)
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    ax1.set_xlim(0, 280)
    ax1.set_ylim(0, 80)
    
    # 尝试加载和分析实际数据
    try:
        # 检查重定位结果数据
        relocated_catalog_path = "quick_test_results/relocated_catalog.csv"
        relocated_phases_path = "quick_test_results/relocated_phases.csv"
        station_path = "../tvf_hyposvi_data/hyposvi_station.csv"
        
        if all(os.path.exists(path) for path in [relocated_catalog_path, relocated_phases_path, station_path]):
            # 读取数据
            catalog_df = pd.read_csv(relocated_catalog_path)
            phases_df = pd.read_csv(relocated_phases_path)
            stations_df = pd.read_csv(station_path)
            
            # 合并数据
            merged_df = phases_df.merge(catalog_df[['evid', 'lat', 'lon', 'depth']], on='evid')
            merged_df = merged_df.merge(stations_df[['station', 'lat', 'lon']], 
                                      left_on='sta', right_on='station', suffixes=('_event', '_station'))
            
            # 计算震中距
            merged_df['distance_km'] = merged_df.apply(
                lambda row: haversine_distance(row['lat_event'], row['lon_event'], 
                                             row['lat_station'], row['lon_station']), axis=1
            )
            
            # 分离P波和S波数据
            p_waves = merged_df[merged_df['phase'] == 'P'].copy()
            s_waves = merged_df[merged_df['phase'] == 'S'].copy()
            
            print(f"P波记录: {len(p_waves)} 个")
            print(f"S波记录: {len(s_waves)} 个")
            
            # 第二个子图：实际数据与理论对比
            if len(p_waves) > 0:
                ax2.scatter(p_waves['distance_km'], p_waves['travel_time'], 
                           alpha=0.6, s=30, color='blue', label=f'观测P波 (n={len(p_waves)})')
            
            if len(s_waves) > 0:
                ax2.scatter(s_waves['distance_km'], s_waves['travel_time'], 
                           alpha=0.6, s=30, color='red', label=f'观测S波 (n={len(s_waves)})')
            
            # 添加理论曲线
            ax2.plot(distances, p_simple, 'b--', linewidth=2, alpha=0.8, label='P波理论 (6.0 km/s)')
            ax2.plot(distances, s_simple, 'r--', linewidth=2, alpha=0.8, label='S波理论 (3.5 km/s)')
            
            ax2.set_xlabel('震中距 (km)', fontsize=12)
            ax2.set_ylabel('走时 (秒)', fontsize=12)
            ax2.set_title('观测走时 vs 理论走时', fontsize=14)
            ax2.legend()
            ax2.grid(True, alpha=0.3)
            
            # 计算并显示统计信息
            stats_text = ""
            if len(p_waves) > 0:
                p_waves['theoretical_time'] = p_waves['distance_km'] / 6.0
                p_waves['residual'] = p_waves['travel_time'] - p_waves['theoretical_time']
                stats_text += f"P波: 残差均值={p_waves['residual'].mean():.2f}s, 标准差={p_waves['residual'].std():.2f}s\n"
            
            if len(s_waves) > 0:
                s_waves['theoretical_time'] = s_waves['distance_km'] / 3.5
                s_waves['residual'] = s_waves['travel_time'] - s_waves['theoretical_time']
                stats_text += f"S波: 残差均值={s_waves['residual'].mean():.2f}s, 标准差={s_waves['residual'].std():.2f}s"
            
            if stats_text:
                ax2.text(0.02, 0.98, stats_text, transform=ax2.transAxes, 
                        fontsize=10, verticalalignment='top', 
                        bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))
        
        else:
            # 如果没有实际数据，显示TauP理论曲线
            ax2.text(0.5, 0.5, '实际观测数据不可用\n显示TauP理论模型', 
                    transform=ax2.transAxes, ha='center', va='center', fontsize=12,
                    bbox=dict(boxstyle='round', facecolor='lightyellow', alpha=0.8))
            
            # 尝试使用TauP模型
            try:
                from obspy.taup import TauPyModel
                model = TauPyModel(model="iasp91")
                
                distance_deg = np.linspace(0.1, 2.5, 50)
                depth = 10  # km
                
                p_taup = []
                s_taup = []
                
                for dist in distance_deg:
                    try:
                        # P波
                        arrivals_p = model.get_travel_times(source_depth_in_km=depth,
                                                           distance_in_degree=dist,
                                                           phase_list=['P'])
                        if arrivals_p:
                            p_taup.append(arrivals_p[0].time)
                        else:
                            p_taup.append(np.nan)
                        
                        # S波
                        arrivals_s = model.get_travel_times(source_depth_in_km=depth,
                                                           distance_in_degree=dist,
                                                           phase_list=['S'])
                        if arrivals_s:
                            s_taup.append(arrivals_s[0].time)
                        else:
                            s_taup.append(np.nan)
                            
                    except Exception:
                        p_taup.append(np.nan)
                        s_taup.append(np.nan)
                
                distance_km_taup = distance_deg * 111.32
                
                ax2.plot(distance_km_taup, p_taup, 'bo', markersize=4, alpha=0.8, 
                        label='P波 - TauP iasp91')
                ax2.plot(distance_km_taup, s_taup, 'ro', markersize=4, alpha=0.8, 
                        label='S波 - TauP iasp91')
                
                ax2.set_xlabel('震中距 (km)', fontsize=12)
                ax2.set_ylabel('走时 (秒)', fontsize=12)
                ax2.set_title('TauP理论走时曲线 (iasp91模型)', fontsize=14)
                ax2.legend()
                ax2.grid(True, alpha=0.3)
                
            except Exception as e:
                print(f"无法加载TauP模型: {e}")
                ax2.set_title('数据不可用', fontsize=14)
    
    except Exception as e:
        print(f"数据加载错误: {e}")
        ax2.text(0.5, 0.5, f'数据加载失败:\n{str(e)}', 
                transform=ax2.transAxes, ha='center', va='center')
        ax2.set_title('数据加载失败', fontsize=14)
    
    plt.tight_layout()
    plt.savefig(f'{output_dir}/travel_time_curves_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"已生成: {output_dir}/travel_time_curves_analysis.png")
    
    # 2. 创建简单的走时曲线对比图
    create_simple_curves(output_dir)

def create_simple_curves(output_dir):
    """创建简单的P波S波走时曲线图"""
    
    fig, ax = plt.subplots(1, 1, figsize=(12, 8))
    
    # 距离范围
    distances = np.linspace(0, 300, 100)
    
    # 计算不同速度模型的走时
    models = {
        'P波 - 简化 (6.0 km/s)': (distances / 6.0, 'blue', '-'),
        'S波 - 简化 (3.5 km/s)': (distances / 3.5, 'red', '-'),
        'P波 - 地壳 (6.2 km/s)': (distances / 6.2, 'blue', '--'),
        'S波 - 地壳 (3.6 km/s)': (distances / 3.6, 'red', '--'),
        'P波 - 上地幔 (8.0 km/s)': (distances / 8.0, 'blue', ':'),
        'S波 - 上地幔 (4.5 km/s)': (distances / 4.5, 'red', ':')
    }
    
    for label, (times, color, style) in models.items():
        ax.plot(distances, times, color=color, linestyle=style, linewidth=2, 
               label=label, alpha=0.8)
    
    # 添加重要的距离标记
    important_distances = [50, 100, 150, 200, 250]
    for dist in important_distances:
        ax.axvline(x=dist, color='gray', linestyle=':', alpha=0.3)
        ax.text(dist, 75, f'{dist}km', rotation=90, ha='right', va='top', 
               fontsize=9, alpha=0.7)
    
    ax.set_xlabel('震中距 (km)', fontsize=12)
    ax.set_ylabel('走时 (秒)', fontsize=12)
    ax.set_title('P波与S波走时曲线对比\n(不同地球模型)', fontsize=14, fontweight='bold')
    ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    ax.grid(True, alpha=0.3)
    
    # 设置合理的轴范围
    ax.set_xlim(0, 300)
    ax.set_ylim(0, 80)
    
    # 添加说明文字
    explanation = ("实线: 简化均匀模型\n"
                  "虚线: 地壳模型\n"
                  "点线: 上地幔模型")
    
    ax.text(0.02, 0.98, explanation, transform=ax.transAxes, 
           fontsize=10, verticalalignment='top',
           bbox=dict(boxstyle='round', facecolor='lightyellow', alpha=0.8))
    
    plt.tight_layout()
    plt.savefig(f'{output_dir}/travel_time_curves_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"已生成: {output_dir}/travel_time_curves_comparison.png")

if __name__ == "__main__":
    create_travel_time_curves()
    print("\nP波与S波走时曲线图生成完成！")
    print("输出文件:")
    print("1. travel_time_curves/travel_time_curves_analysis.png - 综合分析图")
    print("2. travel_time_curves/travel_time_curves_comparison.png - 模型对比图")
