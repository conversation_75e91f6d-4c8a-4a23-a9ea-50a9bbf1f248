# MAP-SSST重定位效果分析报告

## 问题描述

通过分析重定位结果发现，MAP-SSST重定位后的地震分布与原始分布形态几乎没有区别，平均水平位移仅为0.23±0.04 km，深度变化几乎为0。这表明重定位效果不够明显。

## 根本原因分析

### 1. 过强的先验约束

**问题所在：**
```python
# 在map_solver.py中的先验设置
# 深度先验：标准差仅2km
prior_std_scaled = 2.0 / self.scale  # 约0.01 (scale=200)
z_prior = -0.5 * ((X_src[2] - adaptive_prior_center) / prior_std_scaled)**2

# 水平位置先验：标准差仅5km  
xy_prior_std_scaled = 5.0 / self.scale / 1000.0  # 约0.000025
east_prior = -0.5 * ((X_src[0] - self.adaptive_prior_east) / xy_prior_std_scaled)**2
```

**影响：**
- 深度先验标准差2km过于严格，限制了深度调整
- 水平先验标准差5km也较为保守，限制了水平重定位
- 先验权重过大，压制了数据驱动的重定位

### 2. 自适应先验机制

**问题所在：**
```python
# 使用原始地震位置作为先验中心
if prior_info is not None:
    self.adaptive_prior_east = prior_info['east_scaled']
    self.adaptive_prior_north = prior_info['north_scaled'] 
    self.adaptive_prior_z_mean = prior_info['depth_scaled']
```

**影响：**
- 每个地震都以其原始位置为先验中心
- 这种设计天然地倾向于保持原始位置
- 减弱了基于观测数据的重定位能力

### 3. SSST校正幅度较小

**观察结果：**
- SSST校正范围：-0.67至-0.04秒
- 平均校正：-0.31秒
- 校正幅度相对较小，对定位影响有限

### 4. 优化参数设置

**当前设置：**
```json
"inversion": {
    "n_epochs": 200,
    "learning_rate": 1e-3,
    "iter_tol": 1e-2
}
```

**潜在问题：**
- 收敛容差1e-2可能过于宽松
- 学习率可能需要调整
- 迭代次数可能不足

## 参数调整建议

### 1. 放宽先验约束

**修改配置文件 `map_ssst_config.json`：**
```json
{
    "prior": {
        "prior_z_mean": 10.0,
        "prior_z_std": 20.0,          // 从10.0增加到20.0
        "prior_scale_param": 20.0,    // 从10.0增加到20.0
        "max_src_rec_dist": 75.0,
        "horizontal_prior_std": 15.0, // 新增：水平先验标准差15km
        "depth_prior_std": 8.0,       // 新增：深度先验标准差8km
        "use_weak_priors": true       // 新增：使用弱先验
    }
}
```

**修改 `map_solver.py` 中的先验计算：**
```python
# 更宽松的深度先验
if hasattr(self, 'adaptive_prior_z_mean') and self.adaptive_prior_z_mean is not None:
    # 增加深度先验标准差到8km
    prior_std_scaled = 8.0 / self.scale  # 从2.0增加到8.0
    z_prior = -0.5 * ((X_src[2] - adaptive_prior_center) / prior_std_scaled)**2

# 更宽松的水平先验
if hasattr(self, 'adaptive_prior_east') and self.adaptive_prior_east is not None:
    # 增加水平先验标准差到15km
    xy_prior_std_scaled = 15.0 / self.scale / 1000.0  # 从5.0增加到15.0
```

### 2. 优化算法参数

**修改配置文件：**
```json
{
    "inversion": {
        "method": "MAP",
        "n_epochs": 500,              // 从200增加到500
        "learning_rate": 5e-3,        // 从1e-3增加到5e-3
        "iter_tol": 1e-4,            // 从1e-2减少到1e-4
        "optimizer": "adam",
        "patience": 100,              // 新增：早停耐心值
        "min_improvement": 1e-5       // 新增：最小改善阈值
    }
}
```

### 3. 增强SSST效果

**修改SSST配置：**
```json
{
    "ssst": {
        "n_static_iter": 3,           // 从2增加到3
        "n_ssst_iter": 4,            // 从2增加到4
        "ssst_mode": "knn",
        "k-NN": 20,                  // 从30减少到20（更局部化）
        "min_ssst_radius": 0.2,      // 从0.5减少到0.2
        "max_ssst_radius": 12.0,     // 从8.0增加到12.0
        "ssst_weight": 2.0,          // 新增：SSST权重
        "min_events_per_station": 5   // 新增：每台站最小事件数
    }
}
```

### 4. 调整不确定性参数

**修改不确定性设置：**
```json
{
    "uncertainty": {
        "pick_unc_p": 0.15,          // 从0.10增加到0.15
        "pick_unc_s": 0.20,          // 从0.10增加到0.20
        "n_phase_min_pick_std": 10,   // 从15减少到10
        "likelihood_fn": "huber",
        "barron_alpha": 1.0,         // 从1.5减少到1.0
        "adaptive_uncertainty": true  // 新增：自适应不确定性
    }
}
```

### 5. 实现渐进式先验衰减

**新增先验衰减机制：**
```python
# 在map_solver.py中添加
def _calculate_prior_with_decay(self, X_src: torch.Tensor, iteration: int) -> torch.Tensor:
    """
    计算带衰减的先验，随迭代减弱先验影响
    """
    # 先验权重随迭代衰减
    decay_factor = max(0.1, 1.0 - iteration * 0.1)  # 每10次迭代衰减10%
    
    total_prior = torch.tensor(0.0, device=self.device)
    
    # 应用衰减的深度先验
    if hasattr(self, 'adaptive_prior_z_mean') and self.adaptive_prior_z_mean is not None:
        prior_std_scaled = (8.0 / decay_factor) / self.scale  # 随迭代增大标准差
        z_prior = -0.5 * ((X_src[2] - self.adaptive_prior_z_mean) / prior_std_scaled)**2
        total_prior += z_prior * decay_factor
    
    return total_prior
```

## 实施步骤

### 第一阶段：基础参数调整
1. 修改先验标准差（深度8km，水平15km）
2. 增加迭代次数到500
3. 减小收敛容差到1e-4
4. 增加SSST迭代次数

### 第二阶段：算法优化
1. 实现先验衰减机制
2. 添加自适应不确定性
3. 优化SSST权重和半径参数

### 第三阶段：验证和调优
1. 运行测试验证改善效果
2. 分析残差和位移分布
3. 根据结果进一步微调参数

## 预期效果

通过以上调整，预期能够实现：
- 平均水平位移增加到1-3km
- 深度变化范围扩大到±2-5km
- RMS残差进一步降低10-20%
- 重定位结果更好地反映地震的真实空间分布

## 监控指标

1. **位移统计**：
   - 平均水平位移
   - 深度变化范围
   - 最大位移距离

2. **质量指标**：
   - RMS残差改善
   - 定位不确定性
   - 收敛性能

3. **SSST效果**：
   - 校正幅度分布
   - 台站校正一致性
   - 残差系统性改善

通过系统性的参数调整和算法优化，可以显著提升MAP-SSST重定位的效果，使其更好地发挥神经网络走时预测和台站校正的优势。
