# TVF数据MAP地震重定位 - 超级加速版本（带TauP验证）

## 程序概述

这个程序基于MAP-SSST代码，移除SSST部分，只使用MAP方法进行地震定位，并集成了TauP模型进行走时计算验证。

## 运行逻辑分析

### 1. 核心定位流程

```
输入数据 → EikoNet神经网络 → MAP定位 → 结果输出
    ↓
走时验证（TauP模型）
```

### 2. 主要组件

#### EikoNet神经网络
- **位置**: `map_ssst_locator.py` 中的 `calculate_travel_time_with_eikonet()` 方法
- **功能**: 使用训练好的神经网络预测地震走时
- **输入**: 震源坐标、台站坐标、震相类型
- **输出**: 理论走时（秒）

#### MAP定位器
- **位置**: `UltraFastMAPProcessor.locate_event_with_map()` 
- **功能**: 使用最大后验概率方法进行地震定位
- **优化**: 适中的学习率、早停机制、批量处理

#### 简化走时计算
- **位置**: `_generate_relocated_phases()` 方法
- **功能**: 使用简化的速度模型重新计算震相到时
- **公式**: `travel_time = distance / velocity`
- **参数**: P波速度 6.0 km/s, S波速度 3.5 km/s

### 3. TauP验证系统

#### TravelTimeValidator类
新增的验证器，用于比较不同走时计算方法的结果：

```python
class TravelTimeValidator:
    def __init__(self, taup_model_name='iasp91')
    def calculate_taup_travel_time(source_depth_km, distance_deg, phase_type)
    def validate_travel_time(eikonet_time, coordinates, ...)
    def get_validation_statistics()
```

#### 验证流程
1. **简化模型计算**: 使用固定速度计算走时
2. **TauP模型计算**: 使用IASP91标准地球模型
3. **结果比较**: 计算时差、相对误差
4. **统计分析**: 生成验证报告

## 使用方法

### 1. 安装依赖

```bash
# 安装TauP依赖
python install_taup.py

# 或手动安装
pip install obspy
```

### 2. 运行程序

```bash
python run_tvf_map_ultra_fast.py
```

### 3. 选择处理模式

程序会提示选择处理模式：
- 1) 超快测试 (20个事件)
- 2) 快速测试 (50个事件)  
- 3) 中等测试 (100个事件)
- 4) 大规模测试 (200个事件)
- 5) 全部事件

## 输出文件

### 定位结果
- `map_locations.csv`: MAP定位结果
- `map_residuals.csv`: 定位残差
- `relocated_catalog.csv`: 重定位地震目录
- `relocated_phases.csv`: 重定位震相文件

### 验证结果
- `travel_time_validation.csv`: 走时验证详情
- `detailed_eikonet_taup_comparison.csv`: 详细对比结果

## 验证指标

### 时差统计
- **平均时差**: EikoNet与TauP走时的平均差值
- **标准偏差**: 时差的标准偏差
- **最大时差**: 最大绝对时差

### 相对误差
- **平均相对误差**: `|时差| / TauP走时 × 100%`
- **最大相对误差**: 最大相对误差百分比

### 验证成功率
- **成功率**: TauP成功计算的比例
- **总验证次数**: 执行验证的总次数

## 程序优化特性

### 1. MAP方法优化
- 适中的训练轮数 (300 epochs)
- 适中的早停参数 (50 patience)
- 优化的学习率 (8e-3)

### 2. 性能优化
- 串行处理避免CUDA多进程问题
- 批量进度显示
- 内存优化减少数据复制

### 3. 验证优化
- 限制验证样本数量（避免过度计算）
- 智能错误处理
- 详细统计输出

## 代码结构

```
run_tvf_map_ultra_fast.py
├── TravelTimeValidator          # 走时验证器
├── UltraFastMAPProcessor       # 主处理器
│   ├── __init__()              # 初始化（含验证器）
│   ├── run_ultra_fast_map()    # 主处理流程
│   ├── _generate_relocated_phases()  # 生成震相（含验证）
│   └── validate_eikonet_vs_taup()   # 专门验证
└── main()                      # 主函数
```

## 验证示例输出

```
📊 走时验证统计 (样本: 100):
   - 平均时差: 0.123 ± 0.045 秒
   - 最大时差: 0.234 秒  
   - 平均相对误差: 2.1%
   - 验证成功率: 98.0%

📊 走时验证总结:
   - 总验证样本: 150
   - 成功验证率: 96.7%
   - 平均时差: 0.089 ± 0.067 秒
   - 最大时差: 0.456 秒
   - 平均相对误差: 1.8%
   - 最大相对误差: 8.9%
```

## 注意事项

1. **TauP模型**: 默认使用IASP91模型，可在代码中修改
2. **验证限制**: 为避免计算开销，限制验证样本数量
3. **坐标系统**: 注意震源和台站坐标的一致性
4. **依赖要求**: 需要ObsPy库支持TauP功能

## 扩展功能

### 可能的改进方向
1. **其他地球模型**: 支持PREM、ak135等模型
2. **3D速度模型**: 集成区域3D速度模型
3. **实时验证**: 实时显示验证结果
4. **可视化**: 添加走时曲线对比图

### 自定义验证
可以修改 `TravelTimeValidator` 类来：
- 使用不同的地球模型
- 调整验证采样策略  
- 添加更多统计指标
- 输出详细的验证报告
