"""
EikoNet Neural Network Module

This module implements the EikoNet neural network for solving the Eikonal equation
and predicting seismic travel times. It includes custom layers, loss functions,
and training utilities.

Key components:
- EikoNet: Main neural network model
- CylindricalSymmetry: Custom layer for coordinate transformation
- EikonalLoss: Physics-informed loss function
- EikoNetTrainer: Training utilities
"""

from .models import EikoNet
from .layers import CylindricalSymmetry, PosEncoding
from .loss import EikonalLoss
from .trainer import EikoNetTrainer
from .utils import tau0, grad_tau0, finite_diff, eikonal_pde

__all__ = [
    "EikoNet",
    "CylindricalSymmetry",
    "PosEncoding",
    "EikonalLoss",
    "EikoNetTrainer",
    "tau0",
    "grad_tau0",
    "finite_diff",
    "eikonal_pde",
]
