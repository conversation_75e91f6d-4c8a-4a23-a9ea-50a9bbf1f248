"""
Utility functions for EikoNet neural network.

This module provides utility functions for computing gradients, finite differences,
and solving the Eikonal PDE.
"""

import torch
from typing import Tuple


def tau0(x: torch.Tensor) -> torch.Tensor:
    """
    Calculate straight-line travel time τ0.
    
    Args:
        x: Input coordinates tensor of shape (7, n_samples) or (batch_size, 7)
           [x_src, y_src, z_src, x_rec, y_rec, z_rec, phase]
           
    Returns:
        Straight-line travel time
    """
    if x.dim() == 2 and x.size(0) == 7:
        # Julia-style format: (7, n_samples)
        src_coords = x[0:3]  # [x_src, y_src, z_src]
        rec_coords = x[3:6]  # [x_rec, y_rec, z_rec]
        
    elif x.dim() == 2 and x.size(1) == 7:
        # PyTorch format: (batch_size, 7)
        src_coords = x[:, 0:3].T
        rec_coords = x[:, 3:6].T
        
    elif x.dim() == 3:
        # 3D format
        src_coords = x[0:3]
        rec_coords = x[3:6]
        
    else:
        raise ValueError(f"Unexpected input shape: {x.shape}")
        
    # Calculate Euclidean distance
    distance = torch.sqrt(torch.sum((rec_coords - src_coords)**2, dim=0, keepdim=True))
    
    return distance


def grad_tau0(x: torch.Tensor) -> torch.Tensor:
    """
    Calculate gradient of τ0 with respect to receiver coordinates.
    
    Args:
        x: Input coordinates tensor
        
    Returns:
        Gradient of τ0: ∇τ0 = (x_rec - x_src) / τ0
    """
    if x.dim() == 2 and x.size(0) == 7:
        # Julia-style format: (7, n_samples)
        src_coords = x[0:3]
        rec_coords = x[3:6]
        
    elif x.dim() == 2 and x.size(1) == 7:
        # PyTorch format: (batch_size, 7)
        src_coords = x[:, 0:3].T
        rec_coords = x[:, 3:6].T
        
    else:
        raise ValueError(f"Unexpected input shape: {x.shape}")
        
    tau0_val = tau0(x)
    
    # Gradient: (x_rec - x_src) / τ0
    grad = (rec_coords - src_coords) / (tau0_val + 1e-8)  # Add small epsilon for stability
    
    return grad


def finite_diff(
    x: torch.Tensor, 
    model: torch.nn.Module, 
    epsilon: float = 1e-3
) -> torch.Tensor:
    """
    Calculate finite difference gradients for receiver coordinates using vectorized operations.

    This optimized version processes all samples in batches rather than individually,
    significantly reducing computation time.

    Args:
        x: Input coordinates tensor of shape (7, n_samples)
        model: Neural network model (τ1 network)
        epsilon: Finite difference step size

    Returns:
        Finite difference gradient of shape (3, n_samples)
    """
    # Vectorized finite difference calculation for better performance
    n_samples = x.size(1)
    device = x.device

    # Create perturbation tensors for all samples at once
    # We need to perturb receiver coordinates (indices 3, 4, 5)
    perturbations = torch.zeros(3, 7, n_samples, device=device, dtype=x.dtype)
    perturbations[0, 3, :] = 0.5 * epsilon  # x_rec perturbation
    perturbations[1, 4, :] = 0.5 * epsilon  # y_rec perturbation
    perturbations[2, 5, :] = 0.5 * epsilon  # z_rec perturbation

    gradients = []

    # Process each direction (x, y, z) with vectorized operations
    for j in range(3):
        # Get perturbation for this direction
        dx_j = perturbations[j]  # Shape: (7, n_samples)

        # Create forward and backward perturbations
        x_plus = x + dx_j   # Shape: (7, n_samples)
        x_minus = x - dx_j  # Shape: (7, n_samples)

        # Convert to batch format for model evaluation
        x_plus_batch = x_plus.T   # Shape: (n_samples, 7)
        x_minus_batch = x_minus.T # Shape: (n_samples, 7)

        # Batch model evaluations (much more efficient than individual calls)
        f_plus = model(x_plus_batch)   # Shape: (n_samples, 1)
        f_minus = model(x_minus_batch) # Shape: (n_samples, 1)

        # Finite difference for this direction
        grad_j = (f_plus - f_minus) / epsilon  # Shape: (n_samples, 1)
        gradients.append(grad_j.T)  # Shape: (1, n_samples)

    # Stack all gradients
    all_gradients = torch.cat(gradients, dim=0)  # Shape: (3, n_samples)
    
    return all_gradients


def eikonal_pde(
    x: torch.Tensor, 
    model: torch.nn.Module, 
    scale: float = 200.0,
    epsilon: float = 1e-3
) -> torch.Tensor:
    """
    Solve Eikonal PDE to get slowness values.
    
    The Eikonal equation is: |∇τ| = s (slowness)
    where τ = τ0 * τ1
    
    Args:
        x: Input coordinates tensor
        model: EikoNet model
        scale: Coordinate scaling factor
        epsilon: Finite difference step size
        
    Returns:
        Predicted slowness values
    """
    # Get τ1 from the neural network part
    if hasattr(model, 'tau1_network'):
        tau1_net = model.tau1_network
    else:
        tau1_net = model
        
    # Convert input format for τ1 network
    if x.dim() == 2 and x.size(0) == 7:
        x_batch = x.T  # Convert to (n_samples, 7)
    else:
        x_batch = x
        
    tau1_val = tau1_net(x_batch).T  # Shape: (1, n_samples)
    
    # Calculate gradients
    grad_tau1 = finite_diff(x, tau1_net, epsilon)  # Shape: (3, n_samples)
    grad_tau0_val = grad_tau0(x)  # Shape: (3, n_samples)
    tau0_val = tau0(x)  # Shape: (1, n_samples)
    
    # Chain rule: ∇τ = τ1 * ∇τ0 + τ0 * ∇τ1
    grad_tau = (tau1_val * grad_tau0_val + tau0_val * grad_tau1) / scale
    
    # Slowness: s = |∇τ|
    slowness = torch.sqrt(torch.sum(grad_tau**2, dim=0, keepdim=True))
    
    return slowness


def plot_solution(
    model: torch.nn.Module,
    test_data: torch.Tensor,
    target_slowness: torch.Tensor,
    save_path: str = "test_v.pdf",
    max_points: int = 1000
) -> None:
    """
    Plot predicted vs true velocity/slowness for visualization.
    
    Args:
        model: Trained EikoNet model
        test_data: Test input data
        target_slowness: True slowness values
        save_path: Path to save the plot
        max_points: Maximum number of points to plot
    """
    try:
        import matplotlib.pyplot as plt
    except ImportError:
        print("Matplotlib not available for plotting")
        return
        
    model.eval()
    with torch.no_grad():
        # Get predictions
        predicted_slowness = eikonal_pde(test_data, model)
        
        # Convert to velocity (v = 1/s)
        pred_velocity = 1.0 / (predicted_slowness + 1e-8)
        true_velocity = 1.0 / (target_slowness + 1e-8)
        
        # Limit number of points for visualization
        n_points = min(max_points, test_data.size(1))
        
        # Get depth values for x-axis (assuming z is at index 5)
        if test_data.dim() == 2 and test_data.size(0) == 7:
            depths = test_data[5, :n_points].cpu().numpy()
        else:
            depths = test_data[:n_points, 5].cpu().numpy()
            
        pred_v = pred_velocity[0, :n_points].cpu().numpy()
        true_v = true_velocity[0, :n_points].cpu().numpy()
        
        # Create plot
        plt.figure(figsize=(10, 6))
        plt.scatter(depths, pred_v, label='Predicted', alpha=0.6, s=20)
        plt.scatter(depths, true_v, label='True', alpha=0.6, s=20)
        plt.xlabel('Depth')
        plt.ylabel('Velocity')
        plt.legend()
        plt.ylim(0, 10)
        plt.title('Velocity Prediction Comparison')
        plt.tight_layout()
        plt.savefig(save_path)
        plt.close()
        
        print(f"Plot saved to {save_path}")
