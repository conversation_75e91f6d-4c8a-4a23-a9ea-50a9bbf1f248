# EikoNet 训练脚本

这个目录包含了用于训练PyTorch版本EikoNet神经网络的脚本和配置文件。

## 文件说明

- `eikonet_config.json` - 训练配置文件
- `train_eikonet_simple.py` - 简化的训练脚本
- `run_training.py` - 交互式运行脚本
- `train_eikonet.py` - 原始完整训练脚本
- `README.md` - 本说明文件

## 快速开始

### 方法1：使用交互式脚本（推荐）

```bash
cd /home/<USER>/run_eikonet_hyposvi/pytorch_hyposvi/run_eikonet
python run_training.py
```

然后根据提示选择：
- 选择 `1` - 测试模式（10轮训练，快速验证）
- 选择 `2` - 完整训练模式（100轮训练）

### 方法2：直接运行训练脚本

测试模式（10轮训练）：
```bash
python train_eikonet_simple.py --test
```

完整训练模式（100轮训练）：
```bash
python train_eikonet_simple.py
```

## 配置说明

配置文件 `eikonet_config.json` 包含以下主要参数：

### 数据配置
- `station_file`: 台站文件路径
- `velmod_file`: 速度模型文件路径
- `coordinate_bounds`: 坐标边界
- `scale`: 坐标缩放因子

### 训练配置
- `n_epochs`: 训练轮次（测试模式会自动调整为10）
- `n_train`: 训练样本数
- `n_test`: 测试样本数
- `batch_size`: 批次大小
- `lr`: 学习率

### 输出配置
- `plot_interval`: 画图间隔（每隔多少轮次画一次速度对比图）

## 输出文件

训练完成后会生成以下文件：

1. **模型文件**
   - `eikonet_model.pth` - 训练好的模型

2. **可视化文件**
   - `training_history.png` - 训练损失曲线
   - `plots/velocity_comparison_epoch_XXX.png` - 每隔指定轮次的速度对比图

3. **日志文件**
   - 控制台输出包含详细的训练进度信息

## 速度差异记录

脚本会在训练过程中：

1. **每隔指定轮次**（默认每10轮或测试模式每2轮）生成速度对比图
2. **记录预测速度与真实速度的差异**，包括：
   - MAE (平均绝对误差)
   - RMSE (均方根误差)
3. **可视化P波和S波的速度对比**
4. **保存图片到plots目录**

## 训练模式对比

| 模式 | 训练轮次 | 训练样本 | 测试样本 | 画图间隔 | 预计时间 |
|------|----------|----------|----------|----------|----------|
| 测试模式 | 10 | 1,000 | 500 | 2轮 | ~2-5分钟 |
| 完整训练 | 100 | 50,000 | 10,000 | 10轮 | ~30-60分钟 |

## 系统要求

- Python 3.7+
- PyTorch
- NumPy
- Matplotlib
- Pandas
- SciPy
- CUDA（可选，用于GPU加速）

## 故障排除

1. **导入错误**: 确保在正确的目录下运行脚本
2. **CUDA错误**: 如果没有GPU，脚本会自动使用CPU
3. **内存不足**: 可以减少batch_size或样本数量
4. **文件路径错误**: 检查配置文件中的路径是否正确

## 自定义配置

可以修改 `eikonet_config.json` 文件来调整训练参数：

```json
{
  "training": {
    "n_epochs": 100,        // 训练轮次
    "batch_size": 512,      // 批次大小
    "lr": 0.001            // 学习率
  },
  "logging": {
    "plot_interval": 10     // 画图间隔
  }
}
```

## 联系信息

如有问题，请检查：
1. 数据文件是否存在
2. Python环境是否正确安装所需包
3. 配置文件格式是否正确
