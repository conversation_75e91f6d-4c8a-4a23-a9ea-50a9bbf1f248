{"data": {"station_file": "/home/<USER>/run_eikonet_hyposvi/hyposvi/station.csv", "phase_file": "/home/<USER>/run_eikonet_hyposvi/hyposvi/phase.csv", "catalog_file": "/home/<USER>/run_eikonet_hyposvi/hyposvi/pre_cat.csv", "catalog_outfile": "/home/<USER>/run_eikonet_hyposvi/pytorch_hyposvi/run_map_ssst/map_ssst_optimized_results/map_ssst_catalog.csv", "residual_outfile": "/home/<USER>/run_eikonet_hyposvi/pytorch_hyposvi/run_map_ssst/map_ssst_optimized_results/map_ssst_residuals.csv", "ssst_outfile": "/home/<USER>/run_eikonet_hyposvi/pytorch_hyposvi/run_map_ssst/map_ssst_optimized_results/ssst_corrections.csv", "coordinate_bounds": {"lon_min": -118.2, "lat_min": 35.3, "z_min": 0.0, "z_max": 25.0}, "scale": 200.0}, "model": {"eikonet_model_file": "/home/<USER>/run_eikonet_hyposvi/pytorch_hyposvi/run_eikonet/eikonet_model.pth", "prevent_airquakes": false}, "inversion": {"method": "MAP", "n_epochs": 500, "learning_rate": 0.005, "iter_tol": 0.0001, "optimizer": "adam", "patience": 100, "min_improvement": 1e-05}, "uncertainty": {"pick_unc_p": 0.15, "pick_unc_s": 0.2, "n_phase_min_pick_std": 10, "likelihood_fn": "huber", "barron_alpha": 1.0, "adaptive_uncertainty": true}, "prior": {"prior_z_mean": 10.0, "prior_z_std": 20.0, "prior_scale_param": 20.0, "max_src_rec_dist": 75.0, "horizontal_prior_std": 15.0, "depth_prior_std": 8.0, "use_weak_priors": true, "prior_decay_rate": 0.1, "min_prior_weight": 0.1}, "ssst": {"n_static_iter": 3, "n_ssst_iter": 4, "ssst_mode": "knn", "k-NN": 20, "min_ssst_radius": 0.2, "max_ssst_radius": 12.0, "ssst_weight": 2.0, "min_events_per_station": 5, "use_robust_estimation": true, "outlier_threshold": 3.0}, "device": {"use_cuda": true, "cuda_device": 0}, "logging": {"log_level": "INFO", "verbose": true, "show_progress": true, "save_convergence": true}, "output": {"save_residuals": true, "save_uncertainties": true, "save_ssst": true, "save_iterations": true, "save_convergence_plots": true, "output_format": "csv"}}