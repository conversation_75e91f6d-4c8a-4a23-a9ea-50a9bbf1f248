"""
Coordinate transformation utilities.

This module provides coordinate transformations between different
reference systems, particularly LLA (Latitude-Longitude-Altitude)
and ENU (East-North-Up) coordinates.
"""

import numpy as np
import torch
from typing import Union, Tuple, Optional
try:
    import pyproj
    HAS_PYPROJ = True
except ImportError:
    HAS_PYPROJ = False


class CoordinateTransform:
    """
    Base class for coordinate transformations.
    """
    
    def transform(self, *args, **kwargs):
        """Transform coordinates."""
        raise NotImplementedError
        
    def inverse_transform(self, *args, **kwargs):
        """Inverse transform coordinates."""
        raise NotImplementedError


class LLAToENU(CoordinateTransform):
    """
    Transform from LLA (Latitude-Longitude-Altitude) to ENU (East-North-Up).
    
    This transformation converts geographic coordinates to a local
    Cartesian coordinate system centered at a reference point.
    
    Args:
        ref_lat: Reference latitude in degrees
        ref_lon: Reference longitude in degrees
        ref_alt: Reference altitude in meters (default: 0)
    """
    
    def __init__(self, ref_lat: float, ref_lon: float, ref_alt: float = 0.0):
        self.ref_lat = ref_lat
        self.ref_lon = ref_lon
        self.ref_alt = ref_alt
        
        # Earth parameters (WGS84)
        self.a = 6378137.0  # Semi-major axis (m)
        self.f = 1/298.257223563  # Flattening
        self.e2 = 2*self.f - self.f**2  # First eccentricity squared
        
        # Precompute reference point parameters
        self._setup_reference()
        
    def _setup_reference(self):
        """Setup reference point parameters."""
        lat_rad = np.radians(self.ref_lat)
        lon_rad = np.radians(self.ref_lon)
        
        # Radius of curvature in prime vertical
        N = self.a / np.sqrt(1 - self.e2 * np.sin(lat_rad)**2)
        
        # ECEF coordinates of reference point
        self.ref_x = (N + self.ref_alt) * np.cos(lat_rad) * np.cos(lon_rad)
        self.ref_y = (N + self.ref_alt) * np.cos(lat_rad) * np.sin(lon_rad)
        self.ref_z = (N * (1 - self.e2) + self.ref_alt) * np.sin(lat_rad)
        
        # Transformation matrix from ECEF to ENU
        sin_lat = np.sin(lat_rad)
        cos_lat = np.cos(lat_rad)
        sin_lon = np.sin(lon_rad)
        cos_lon = np.cos(lon_rad)
        
        self.R = np.array([
            [-sin_lon, cos_lon, 0],
            [-sin_lat*cos_lon, -sin_lat*sin_lon, cos_lat],
            [cos_lat*cos_lon, cos_lat*sin_lon, sin_lat]
        ])
        
    def _lla_to_ecef(self, lat: float, lon: float, alt: float) -> Tuple[float, float, float]:
        """Convert LLA to ECEF coordinates."""
        lat_rad = np.radians(lat)
        lon_rad = np.radians(lon)
        
        N = self.a / np.sqrt(1 - self.e2 * np.sin(lat_rad)**2)
        
        x = (N + alt) * np.cos(lat_rad) * np.cos(lon_rad)
        y = (N + alt) * np.cos(lat_rad) * np.sin(lon_rad)
        z = (N * (1 - self.e2) + alt) * np.sin(lat_rad)
        
        return x, y, z
        
    def transform(
        self, 
        lat: Union[float, np.ndarray], 
        lon: Union[float, np.ndarray], 
        alt: Union[float, np.ndarray] = 0.0
    ) -> Tuple[Union[float, np.ndarray], Union[float, np.ndarray], Union[float, np.ndarray]]:
        """
        Transform LLA coordinates to ENU.
        
        Args:
            lat: Latitude in degrees
            lon: Longitude in degrees
            alt: Altitude in meters
            
        Returns:
            Tuple of (east, north, up) in meters
        """
        # Handle scalar inputs
        is_scalar = np.isscalar(lat)
        
        lat = np.atleast_1d(lat)
        lon = np.atleast_1d(lon)
        alt = np.atleast_1d(alt)
        
        # Convert to ECEF
        east = np.zeros_like(lat)
        north = np.zeros_like(lat)
        up = np.zeros_like(lat)
        
        for i in range(len(lat)):
            x, y, z = self._lla_to_ecef(lat[i], lon[i], alt[i])
            
            # Vector from reference to point in ECEF
            dx = x - self.ref_x
            dy = y - self.ref_y
            dz = z - self.ref_z
            
            # Transform to ENU
            enu = self.R @ np.array([dx, dy, dz])
            east[i] = enu[0]
            north[i] = enu[1]
            up[i] = enu[2]
            
        if is_scalar:
            return float(east[0]), float(north[0]), float(up[0])
        else:
            return east, north, up

    def inverse_transform(
        self,
        east: Union[float, np.ndarray],
        north: Union[float, np.ndarray],
        up: Union[float, np.ndarray] = 0.0
    ) -> Tuple[Union[float, np.ndarray], Union[float, np.ndarray], Union[float, np.ndarray]]:
        """
        Transform ENU coordinates back to LLA using proper inverse transformation.

        Args:
            east: East coordinate in meters
            north: North coordinate in meters
            up: Up coordinate in meters

        Returns:
            Tuple of (latitude, longitude, altitude)
        """
        # Handle scalar inputs
        is_scalar = np.isscalar(east)

        east = np.atleast_1d(east)
        north = np.atleast_1d(north)
        up = np.atleast_1d(up)

        # Convert ENU to ECEF
        enu_vec = np.array([east, north, up])
        ecef_vec = self.R.T @ enu_vec  # Transpose of R for inverse transformation

        # Add reference ECEF coordinates
        x = ecef_vec[0] + self.ref_x
        y = ecef_vec[1] + self.ref_y
        z = ecef_vec[2] + self.ref_z

        # Convert ECEF to LLA
        lat, lon, alt = self._ecef_to_lla(x, y, z)

        if is_scalar:
            return float(lat[0]), float(lon[0]), float(alt[0])
        else:
            return lat, lon, alt

    def _ecef_to_lla(self, x: np.ndarray, y: np.ndarray, z: np.ndarray) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """Convert ECEF coordinates to LLA."""
        # Longitude is straightforward
        lon = np.arctan2(y, x)

        # Latitude and altitude require iteration
        p = np.sqrt(x**2 + y**2)
        lat = np.arctan2(z, p * (1 - self.e2))

        # Iterate to improve latitude estimate
        for _ in range(3):  # Usually converges quickly
            N = self.a / np.sqrt(1 - self.e2 * np.sin(lat)**2)
            alt = p / np.cos(lat) - N
            lat = np.arctan2(z, p * (1 - self.e2 * N / (N + alt)))

        # Final altitude calculation
        N = self.a / np.sqrt(1 - self.e2 * np.sin(lat)**2)
        alt = p / np.cos(lat) - N

        # Convert to degrees
        lat = np.degrees(lat)
        lon = np.degrees(lon)

        return lat, lon, alt


class ENUToLLA(CoordinateTransform):
    """
    Transform from ENU (East-North-Up) to LLA (Latitude-Longitude-Altitude).
    
    Inverse transformation of LLAToENU.
    
    Args:
        ref_lat: Reference latitude in degrees
        ref_lon: Reference longitude in degrees
        ref_alt: Reference altitude in meters (default: 0)
    """
    
    def __init__(self, ref_lat: float, ref_lon: float, ref_alt: float = 0.0):
        self.lla_to_enu = LLAToENU(ref_lat, ref_lon, ref_alt)
        self.ref_lat = ref_lat
        self.ref_lon = ref_lon
        self.ref_alt = ref_alt
        
    def transform(
        self,
        east: Union[float, np.ndarray],
        north: Union[float, np.ndarray], 
        up: Union[float, np.ndarray] = 0.0
    ) -> Tuple[Union[float, np.ndarray], Union[float, np.ndarray], Union[float, np.ndarray]]:
        """
        Transform ENU coordinates to LLA.
        
        Args:
            east: East coordinate in meters
            north: North coordinate in meters
            up: Up coordinate in meters
            
        Returns:
            Tuple of (latitude, longitude, altitude)
        """
        # This is a simplified implementation
        # For more accurate results, use pyproj or implement full inverse transformation
        
        # Handle scalar inputs
        is_scalar = np.isscalar(east)
        
        east = np.atleast_1d(east)
        north = np.atleast_1d(north)
        up = np.atleast_1d(up)
        
        # Approximate conversion using local tangent plane
        # This assumes small displacements from reference point
        
        # Earth radius at reference latitude
        lat_rad = np.radians(self.ref_lat)
        R_earth = 6371000.0  # Mean Earth radius in meters
        R_lat = R_earth
        R_lon = R_earth * np.cos(lat_rad)
        
        # Convert to lat/lon differences
        dlat = north / R_lat
        dlon = east / R_lon
        
        # Convert to degrees and add to reference
        lat = self.ref_lat + np.degrees(dlat)
        lon = self.ref_lon + np.degrees(dlon)
        alt = self.ref_alt + up
        
        if is_scalar:
            return float(lat[0]), float(lon[0]), float(alt[0])
        else:
            return lat, lon, alt


class MinMaxScaler:
    """
    Min-max scaler for coordinate normalization.
    
    Scales coordinates to a specified range, typically [0, 1].
    
    Args:
        feature_range: Target range for scaling (default: (0, 1))
    """
    
    def __init__(self, feature_range: Tuple[float, float] = (0.0, 1.0)):
        self.feature_range = feature_range
        self.min_vals = None
        self.scale_vals = None
        self.fitted = False
        
    def fit(self, X: Union[np.ndarray, torch.Tensor]) -> 'MinMaxScaler':
        """
        Fit the scaler to data.
        
        Args:
            X: Input data of shape (n_samples, n_features) or (n_features, n_samples)
            
        Returns:
            Self for method chaining
        """
        if isinstance(X, torch.Tensor):
            X = X.detach().cpu().numpy()
            
        X = np.asarray(X)
        
        # Handle different input shapes
        if X.ndim == 2:
            if X.shape[0] == 6 or X.shape[0] == 7:  # Julia-style (features, samples)
                X = X[:6].T  # Take first 6 features and transpose
            else:  # Standard (samples, features)
                X = X[:, :6]  # Take first 6 features
        else:
            raise ValueError(f"Expected 2D array, got {X.ndim}D")
            
        # Calculate min and scale
        self.min_vals = np.min(X, axis=0)
        max_vals = np.max(X, axis=0)
        
        data_range = max_vals - self.min_vals
        scale_range = self.feature_range[1] - self.feature_range[0]
        
        self.scale_vals = scale_range / (data_range + 1e-8)  # Add small epsilon
        self.fitted = True
        
        return self
        
    def transform(self, X: Union[np.ndarray, torch.Tensor]) -> Union[np.ndarray, torch.Tensor]:
        """
        Transform data using fitted scaler.
        
        Args:
            X: Input data to transform
            
        Returns:
            Scaled data
        """
        if not self.fitted:
            raise RuntimeError("Scaler must be fitted before transform")
            
        is_torch = isinstance(X, torch.Tensor)
        if is_torch:
            device = X.device
            dtype = X.dtype
            X_np = X.detach().cpu().numpy()
        else:
            X_np = np.asarray(X)
            
        # Apply scaling
        X_scaled = (X_np - self.min_vals) * self.scale_vals + self.feature_range[0]
        
        if is_torch:
            return torch.from_numpy(X_scaled).to(device).type(dtype)
        else:
            return X_scaled
            
    def inverse_transform(self, X: Union[np.ndarray, torch.Tensor]) -> Union[np.ndarray, torch.Tensor]:
        """
        Inverse transform scaled data back to original scale.
        
        Args:
            X: Scaled data to inverse transform
            
        Returns:
            Data in original scale
        """
        if not self.fitted:
            raise RuntimeError("Scaler must be fitted before inverse_transform")
            
        is_torch = isinstance(X, torch.Tensor)
        if is_torch:
            device = X.device
            dtype = X.dtype
            X_np = X.detach().cpu().numpy()
        else:
            X_np = np.asarray(X)
            
        # Apply inverse scaling
        X_orig = (X_np - self.feature_range[0]) / self.scale_vals + self.min_vals
        
        if is_torch:
            return torch.from_numpy(X_orig).to(device).type(dtype)
        else:
            return X_orig
            
    def fit_transform(self, X: Union[np.ndarray, torch.Tensor]) -> Union[np.ndarray, torch.Tensor]:
        """
        Fit scaler and transform data in one step.
        
        Args:
            X: Input data
            
        Returns:
            Scaled data
        """
        return self.fit(X).transform(X)
