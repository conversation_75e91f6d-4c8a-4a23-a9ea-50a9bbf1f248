#!/usr/bin/env python3
"""
快速测试MAP定位程序的走时验证功能
"""

import sys
from pathlib import Path

# 添加路径
script_dir = Path(__file__).parent
sys.path.insert(0, str(script_dir))

# 导入主程序
from run_tvf_map_ultra_fast import UltraFastMAPProcessor
import pandas as pd

def quick_test():
    """快速测试"""
    print("🚀 快速测试MAP定位程序的走时验证功能")
    print("=" * 60)
    
    # 配置文件路径
    config_file = script_dir / "tvf_map_config.json"
    model_file = script_dir.parent / "tvf_run_eikonet" / "tvf_eikonet_model.pth"
    phase_file = script_dir.parent / "tvf_hyposvi_data" / "2011_phase.csv"
    
    # 检查文件
    if not config_file.exists():
        print(f"❌ 配置文件不存在: {config_file}")
        return
    if not model_file.exists():
        print(f"❌ 模型文件不存在: {model_file}")
        return
    if not phase_file.exists():
        print(f"❌ 震相文件不存在: {phase_file}")
        return
    
    print("✅ 所有必需文件都存在")
    
    # 获取少量事件进行测试
    phases_df = pd.read_csv(phase_file)
    event_ids = sorted(phases_df['evid'].unique())[:3]  # 只测试前3个事件
    
    print(f"📊 测试事件: {event_ids}")
    
    # 创建输出目录
    output_dir = script_dir / "quick_test_results"
    
    try:
        # 创建处理器
        processor = UltraFastMAPProcessor(str(config_file), str(model_file))
        
        # 运行定位和验证
        results = processor.run_ultra_fast_map(
            event_ids=event_ids,
            output_dir=str(output_dir)
        )
        
        print("\n🎉 快速测试完成！")
        print(f"📊 结果: {results}")
        
        # 检查验证结果文件
        validation_file = output_dir / "travel_time_validation.csv"
        if validation_file.exists():
            validation_df = pd.read_csv(validation_file)
            print(f"✅ 走时验证结果: {len(validation_df)} 条记录")
            
            # 显示验证统计
            if len(validation_df) > 0:
                successful = validation_df[validation_df['validation_status'] == 'success']
                if len(successful) > 0:
                    print(f"   - 验证成功率: {len(successful)/len(validation_df)*100:.1f}%")
                    if 'time_difference' in successful.columns:
                        time_diffs = successful['time_difference']
                        print(f"   - 平均时差: {time_diffs.mean():.3f} ± {time_diffs.std():.3f} 秒")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    quick_test()
