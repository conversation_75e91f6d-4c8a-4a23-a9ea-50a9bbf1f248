{"data": {"station_file": "./station.csv", "phase_file": "./phase.csv", "catalog_outfile": "./catalog_out.csv", "residual_outfile": "./residuals_out.csv", "ssst_outfile": "./ssst_out.csv", "coordinate_bounds": {"lon_min": -117.5, "lat_min": 33.0, "z_min": -3.0, "z_max": 25.0}, "scale": 200.0}, "model": {"eikonet_model_file": "./model.pth", "prevent_airquakes": true}, "inversion": {"method": "MAP", "n_epochs": 2000, "learning_rate": 1e-05, "iter_tol": 0.01, "svi_iter_tol": 0.001, "n_particles": 101, "lr_decay_interval": 100, "optimizer": "adam"}, "uncertainty": {"pick_unc_p": 0.1, "pick_unc_s": 0.1, "n_phase_min_pick_std": 15, "likelihood_fn": "huber", "barron_alpha": 1.5}, "prior": {"prior_z_mean": 5.5, "prior_z_std": 3.0, "prior_scale_param": 2.0, "max_src_rec_dist": 75.0}, "ssst": {"n_ssst_iter": 3, "ssst_mode": "knn", "outlier_ndev": 3.0, "n_det": 8, "knn_k": 10, "knn_max_dist": 50.0}, "svi": {"kernel_fn": "RBF", "svi_verbose": 1, "plot_particles": false}, "device": {"use_cuda": true, "cuda_device": 0}, "logging": {"log_level": "INFO", "log_file": "hyposvi_location.log", "verbose": true, "show_progress": true}, "output": {"save_residuals": true, "save_uncertainties": true, "output_format": "csv"}}