"""
Mathematical utility functions.

This module provides mathematical utilities including angle normalization,
statistical functions, and numerical computations.
"""

import numpy as np
import math
from typing import Union, List, Tuple, Optional
import torch


def normalize_angle(angle: Union[float, np.ndarray], unit: str = "degrees") -> Union[float, np.ndarray]:
    """
    Normalize angle to [0, 2π) or [0, 360°).
    
    Args:
        angle: Angle value(s)
        unit: "degrees" or "radians"
        
    Returns:
        Normalized angle(s)
    """
    if unit == "degrees":
        return angle % 360.0
    elif unit == "radians":
        return angle % (2.0 * np.pi)
    else:
        raise ValueError("Unit must be 'degrees' or 'radians'")


def deg2rad(degrees: Union[float, np.ndarray]) -> Union[float, np.ndarray]:
    """
    Convert degrees to radians.
    
    Args:
        degrees: Angle in degrees
        
    Returns:
        Angle in radians
    """
    return np.radians(degrees)


def rad2deg(radians: Union[float, np.ndarray]) -> Union[float, np.ndarray]:
    """
    Convert radians to degrees.
    
    Args:
        radians: Angle in radians
        
    Returns:
        Angle in degrees
    """
    return np.degrees(radians)


def moving_average(data: Union[List, np.ndarray], window: int) -> np.ndarray:
    """
    Calculate moving average.
    
    Args:
        data: Input data
        window: Window size
        
    Returns:
        Moving average
    """
    data = np.asarray(data)
    if window > len(data):
        return np.array([np.mean(data)])
        
    return np.convolve(data, np.ones(window)/window, mode='valid')


def median_absolute_deviation(data: Union[List, np.ndarray], scale_factor: float = 1.4826) -> float:
    """
    Calculate Median Absolute Deviation (MAD).
    
    Args:
        data: Input data
        scale_factor: Scale factor for normal distribution consistency
        
    Returns:
        MAD value
    """
    data = np.asarray(data)
    median = np.median(data)
    mad = np.median(np.abs(data - median))
    return scale_factor * mad


def robust_statistics(data: Union[List, np.ndarray]) -> dict:
    """
    Calculate robust statistics.
    
    Args:
        data: Input data
        
    Returns:
        Dictionary with robust statistics
    """
    data = np.asarray(data)
    
    stats = {
        'median': np.median(data),
        'mad': median_absolute_deviation(data),
        'q25': np.percentile(data, 25),
        'q75': np.percentile(data, 75),
        'iqr': np.percentile(data, 75) - np.percentile(data, 25),
        'min': np.min(data),
        'max': np.max(data),
        'count': len(data)
    }
    
    return stats


def outlier_detection(data: Union[List, np.ndarray], method: str = "iqr", threshold: float = 1.5) -> np.ndarray:
    """
    Detect outliers in data.
    
    Args:
        data: Input data
        method: Detection method ("iqr", "mad", "zscore")
        threshold: Threshold for outlier detection
        
    Returns:
        Boolean array indicating outliers
    """
    data = np.asarray(data)
    
    if method == "iqr":
        q25, q75 = np.percentile(data, [25, 75])
        iqr = q75 - q25
        lower = q25 - threshold * iqr
        upper = q75 + threshold * iqr
        return (data < lower) | (data > upper)
        
    elif method == "mad":
        median = np.median(data)
        mad = median_absolute_deviation(data)
        return np.abs(data - median) > threshold * mad
        
    elif method == "zscore":
        mean = np.mean(data)
        std = np.std(data)
        return np.abs(data - mean) > threshold * std
        
    else:
        raise ValueError(f"Unknown outlier detection method: {method}")


def circular_mean(angles: Union[List, np.ndarray], unit: str = "radians") -> float:
    """
    Calculate circular mean of angles.
    
    Args:
        angles: Array of angles
        unit: "degrees" or "radians"
        
    Returns:
        Circular mean
    """
    angles = np.asarray(angles)
    
    if unit == "degrees":
        angles = np.radians(angles)
        
    # Convert to unit vectors and average
    x = np.mean(np.cos(angles))
    y = np.mean(np.sin(angles))
    
    mean_angle = np.arctan2(y, x)
    
    if unit == "degrees":
        mean_angle = np.degrees(mean_angle)
        
    return mean_angle


def circular_std(angles: Union[List, np.ndarray], unit: str = "radians") -> float:
    """
    Calculate circular standard deviation.
    
    Args:
        angles: Array of angles
        unit: "degrees" or "radians"
        
    Returns:
        Circular standard deviation
    """
    angles = np.asarray(angles)
    
    if unit == "degrees":
        angles = np.radians(angles)
        
    # Calculate resultant vector length
    x = np.mean(np.cos(angles))
    y = np.mean(np.sin(angles))
    R = np.sqrt(x**2 + y**2)
    
    # Circular standard deviation
    std_rad = np.sqrt(-2 * np.log(R))
    
    if unit == "degrees":
        std_rad = np.degrees(std_rad)
        
    return std_rad


def interpolate_1d(x: np.ndarray, y: np.ndarray, x_new: Union[float, np.ndarray], 
                   method: str = "linear") -> Union[float, np.ndarray]:
    """
    1D interpolation.
    
    Args:
        x: X coordinates
        y: Y coordinates
        x_new: New X coordinates for interpolation
        method: Interpolation method ("linear", "cubic")
        
    Returns:
        Interpolated values
    """
    if method == "linear":
        return np.interp(x_new, x, y)
    elif method == "cubic":
        from scipy.interpolate import interp1d
        f = interp1d(x, y, kind='cubic', fill_value='extrapolate')
        return f(x_new)
    else:
        raise ValueError(f"Unknown interpolation method: {method}")


def smooth_data(data: Union[List, np.ndarray], method: str = "gaussian", 
                sigma: float = 1.0) -> np.ndarray:
    """
    Smooth data using various methods.
    
    Args:
        data: Input data
        method: Smoothing method ("gaussian", "savgol", "median")
        sigma: Smoothing parameter
        
    Returns:
        Smoothed data
    """
    data = np.asarray(data)
    
    if method == "gaussian":
        from scipy.ndimage import gaussian_filter1d
        return gaussian_filter1d(data, sigma)
    elif method == "savgol":
        from scipy.signal import savgol_filter
        window_length = max(3, int(2 * sigma + 1))
        if window_length % 2 == 0:
            window_length += 1
        return savgol_filter(data, window_length, 2)
    elif method == "median":
        from scipy.signal import medfilt
        kernel_size = max(3, int(2 * sigma + 1))
        if kernel_size % 2 == 0:
            kernel_size += 1
        return medfilt(data, kernel_size)
    else:
        raise ValueError(f"Unknown smoothing method: {method}")


def correlation_coefficient(x: Union[List, np.ndarray], y: Union[List, np.ndarray]) -> float:
    """
    Calculate Pearson correlation coefficient.
    
    Args:
        x: First variable
        y: Second variable
        
    Returns:
        Correlation coefficient
    """
    x = np.asarray(x)
    y = np.asarray(y)
    
    return np.corrcoef(x, y)[0, 1]


def root_mean_square(data: Union[List, np.ndarray]) -> float:
    """
    Calculate root mean square.
    
    Args:
        data: Input data
        
    Returns:
        RMS value
    """
    data = np.asarray(data)
    return np.sqrt(np.mean(data**2))


def weighted_mean(values: Union[List, np.ndarray], weights: Union[List, np.ndarray]) -> float:
    """
    Calculate weighted mean.
    
    Args:
        values: Values to average
        weights: Weights for each value
        
    Returns:
        Weighted mean
    """
    values = np.asarray(values)
    weights = np.asarray(weights)
    
    return np.average(values, weights=weights)


def weighted_std(values: Union[List, np.ndarray], weights: Union[List, np.ndarray]) -> float:
    """
    Calculate weighted standard deviation.
    
    Args:
        values: Values
        weights: Weights for each value
        
    Returns:
        Weighted standard deviation
    """
    values = np.asarray(values)
    weights = np.asarray(weights)
    
    weighted_avg = weighted_mean(values, weights)
    variance = np.average((values - weighted_avg)**2, weights=weights)
    
    return np.sqrt(variance)


def confidence_interval(data: Union[List, np.ndarray], confidence: float = 0.95) -> Tuple[float, float]:
    """
    Calculate confidence interval.
    
    Args:
        data: Input data
        confidence: Confidence level (0-1)
        
    Returns:
        Tuple of (lower_bound, upper_bound)
    """
    data = np.asarray(data)
    alpha = 1 - confidence
    
    lower_percentile = (alpha / 2) * 100
    upper_percentile = (1 - alpha / 2) * 100
    
    lower_bound = np.percentile(data, lower_percentile)
    upper_bound = np.percentile(data, upper_percentile)
    
    return lower_bound, upper_bound


def bootstrap_statistic(data: Union[List, np.ndarray], statistic_func: callable, 
                       n_bootstrap: int = 1000) -> np.ndarray:
    """
    Calculate bootstrap statistics.
    
    Args:
        data: Input data
        statistic_func: Function to calculate statistic
        n_bootstrap: Number of bootstrap samples
        
    Returns:
        Array of bootstrap statistics
    """
    data = np.asarray(data)
    n = len(data)
    
    bootstrap_stats = []
    for _ in range(n_bootstrap):
        # Resample with replacement
        bootstrap_sample = np.random.choice(data, size=n, replace=True)
        stat = statistic_func(bootstrap_sample)
        bootstrap_stats.append(stat)
        
    return np.array(bootstrap_stats)


def numerical_gradient(func: callable, x: Union[float, np.ndarray], h: float = 1e-5) -> Union[float, np.ndarray]:
    """
    Calculate numerical gradient using finite differences.
    
    Args:
        func: Function to differentiate
        x: Point at which to calculate gradient
        h: Step size
        
    Returns:
        Numerical gradient
    """
    if np.isscalar(x):
        return (func(x + h) - func(x - h)) / (2 * h)
    else:
        x = np.asarray(x)
        grad = np.zeros_like(x)
        
        for i in range(len(x)):
            x_plus = x.copy()
            x_minus = x.copy()
            x_plus[i] += h
            x_minus[i] -= h
            
            grad[i] = (func(x_plus) - func(x_minus)) / (2 * h)
            
        return grad


def numerical_hessian(func: callable, x: np.ndarray, h: float = 1e-5) -> np.ndarray:
    """
    Calculate numerical Hessian matrix.
    
    Args:
        func: Function to differentiate
        x: Point at which to calculate Hessian
        h: Step size
        
    Returns:
        Hessian matrix
    """
    x = np.asarray(x)
    n = len(x)
    hessian = np.zeros((n, n))
    
    for i in range(n):
        for j in range(n):
            if i == j:
                # Diagonal elements
                x_plus = x.copy()
                x_minus = x.copy()
                x_plus[i] += h
                x_minus[i] -= h
                
                hessian[i, j] = (func(x_plus) - 2*func(x) + func(x_minus)) / (h**2)
            else:
                # Off-diagonal elements
                x_pp = x.copy()
                x_pm = x.copy()
                x_mp = x.copy()
                x_mm = x.copy()
                
                x_pp[i] += h
                x_pp[j] += h
                x_pm[i] += h
                x_pm[j] -= h
                x_mp[i] -= h
                x_mp[j] += h
                x_mm[i] -= h
                x_mm[j] -= h
                
                hessian[i, j] = (func(x_pp) - func(x_pm) - func(x_mp) + func(x_mm)) / (4 * h**2)
                
    return hessian
