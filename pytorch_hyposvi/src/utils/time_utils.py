"""
Time and date utility functions.

This module provides utilities for time and date handling,
including parsing, conversion, and formatting functions.
"""

import datetime
import time
from typing import Union, Optional
import re
import numpy as np


def datetime_to_seconds(dt: datetime.datetime, reference: Optional[datetime.datetime] = None) -> float:
    """
    Convert datetime to seconds since reference time.
    
    Args:
        dt: Datetime to convert
        reference: Reference datetime (default: Unix epoch)
        
    Returns:
        Seconds since reference time
    """
    if reference is None:
        reference = datetime.datetime(1970, 1, 1, tzinfo=datetime.timezone.utc)
        
    if dt.tzinfo is None:
        dt = dt.replace(tzinfo=datetime.timezone.utc)
    if reference.tzinfo is None:
        reference = reference.replace(tzinfo=datetime.timezone.utc)
        
    delta = dt - reference
    return delta.total_seconds()


def seconds_to_datetime(seconds: float, reference: Optional[datetime.datetime] = None) -> datetime.datetime:
    """
    Convert seconds since reference time to datetime.
    
    Args:
        seconds: Seconds since reference time
        reference: Reference datetime (default: Unix epoch)
        
    Returns:
        Datetime object
    """
    if reference is None:
        reference = datetime.datetime(1970, 1, 1, tzinfo=datetime.timezone.utc)
        
    if reference.tzinfo is None:
        reference = reference.replace(tzinfo=datetime.timezone.utc)
        
    delta = datetime.timedelta(seconds=seconds)
    return reference + delta


def parse_datetime(dt_string: str) -> datetime.datetime:
    """
    Parse datetime string in various formats.
    
    Args:
        dt_string: Datetime string
        
    Returns:
        Parsed datetime object
    """
    # Common datetime formats
    formats = [
        "%Y-%m-%dT%H:%M:%S.%f",      # ISO format with microseconds
        "%Y-%m-%dT%H:%M:%S",         # ISO format
        "%Y-%m-%d %H:%M:%S.%f",      # Space-separated with microseconds
        "%Y-%m-%d %H:%M:%S",         # Space-separated
        "%Y/%m/%d %H:%M:%S.%f",      # Slash-separated with microseconds
        "%Y/%m/%d %H:%M:%S",         # Slash-separated
        "%d/%m/%Y %H:%M:%S",         # European format
        "%m/%d/%Y %H:%M:%S",         # US format
        "%Y-%m-%d",                  # Date only
        "%d-%m-%Y",                  # European date
        "%m-%d-%Y",                  # US date
    ]
    
    # Try each format
    for fmt in formats:
        try:
            return datetime.datetime.strptime(dt_string, fmt)
        except ValueError:
            continue
            
    # Try parsing with timezone info
    try:
        return datetime.datetime.fromisoformat(dt_string)
    except ValueError:
        pass
        
    # Try pandas-style parsing
    try:
        import pandas as pd
        return pd.to_datetime(dt_string).to_pydatetime()
    except (ImportError, ValueError):
        pass
        
    raise ValueError(f"Unable to parse datetime string: {dt_string}")


def format_datetime(dt: datetime.datetime, format_type: str = "iso") -> str:
    """
    Format datetime object to string.
    
    Args:
        dt: Datetime object
        format_type: Format type ("iso", "readable", "compact")
        
    Returns:
        Formatted datetime string
    """
    if format_type == "iso":
        return dt.isoformat()
    elif format_type == "readable":
        return dt.strftime("%Y-%m-%d %H:%M:%S")
    elif format_type == "compact":
        return dt.strftime("%Y%m%d_%H%M%S")
    else:
        return dt.strftime(format_type)


def time_difference(dt1: datetime.datetime, dt2: datetime.datetime, unit: str = "seconds") -> float:
    """
    Calculate time difference between two datetimes.
    
    Args:
        dt1: First datetime
        dt2: Second datetime
        unit: Unit for result ("seconds", "minutes", "hours", "days")
        
    Returns:
        Time difference in specified unit
    """
    delta = dt1 - dt2
    seconds = delta.total_seconds()
    
    if unit == "seconds":
        return seconds
    elif unit == "minutes":
        return seconds / 60.0
    elif unit == "hours":
        return seconds / 3600.0
    elif unit == "days":
        return seconds / 86400.0
    else:
        raise ValueError(f"Unknown time unit: {unit}")


def julian_day(dt: datetime.datetime) -> float:
    """
    Calculate Julian day number.
    
    Args:
        dt: Datetime object
        
    Returns:
        Julian day number
    """
    a = (14 - dt.month) // 12
    y = dt.year + 4800 - a
    m = dt.month + 12 * a - 3
    
    jdn = dt.day + (153 * m + 2) // 5 + 365 * y + y // 4 - y // 100 + y // 400 - 32045
    
    # Add fractional day
    fraction = (dt.hour + dt.minute / 60.0 + dt.second / 3600.0) / 24.0
    
    return jdn + fraction


def modified_julian_day(dt: datetime.datetime) -> float:
    """
    Calculate Modified Julian Day (MJD).
    
    Args:
        dt: Datetime object
        
    Returns:
        Modified Julian Day
    """
    return julian_day(dt) - 2400000.5


def gps_time(dt: datetime.datetime) -> tuple:
    """
    Convert datetime to GPS time (week number and seconds of week).
    
    Args:
        dt: Datetime object
        
    Returns:
        Tuple of (GPS week, seconds of week)
    """
    # GPS epoch: January 6, 1980, 00:00:00 UTC
    gps_epoch = datetime.datetime(1980, 1, 6, tzinfo=datetime.timezone.utc)
    
    if dt.tzinfo is None:
        dt = dt.replace(tzinfo=datetime.timezone.utc)
        
    delta = dt - gps_epoch
    total_seconds = delta.total_seconds()
    
    gps_week = int(total_seconds // (7 * 24 * 3600))
    seconds_of_week = total_seconds % (7 * 24 * 3600)
    
    return gps_week, seconds_of_week


def leap_seconds(dt: datetime.datetime) -> int:
    """
    Get number of leap seconds for given datetime.
    
    Args:
        dt: Datetime object
        
    Returns:
        Number of leap seconds
    """
    # Simplified leap second table (as of 2023)
    leap_second_dates = [
        datetime.datetime(1972, 7, 1),
        datetime.datetime(1973, 1, 1),
        datetime.datetime(1974, 1, 1),
        datetime.datetime(1975, 1, 1),
        datetime.datetime(1976, 1, 1),
        datetime.datetime(1977, 1, 1),
        datetime.datetime(1978, 1, 1),
        datetime.datetime(1979, 1, 1),
        datetime.datetime(1980, 1, 1),
        datetime.datetime(1981, 7, 1),
        datetime.datetime(1982, 7, 1),
        datetime.datetime(1983, 7, 1),
        datetime.datetime(1985, 7, 1),
        datetime.datetime(1988, 1, 1),
        datetime.datetime(1990, 1, 1),
        datetime.datetime(1991, 1, 1),
        datetime.datetime(1992, 7, 1),
        datetime.datetime(1993, 7, 1),
        datetime.datetime(1994, 7, 1),
        datetime.datetime(1996, 1, 1),
        datetime.datetime(1997, 7, 1),
        datetime.datetime(1999, 1, 1),
        datetime.datetime(2006, 1, 1),
        datetime.datetime(2009, 1, 1),
        datetime.datetime(2012, 7, 1),
        datetime.datetime(2015, 7, 1),
        datetime.datetime(2017, 1, 1),
    ]
    
    # Count leap seconds before given date
    count = 0
    for leap_date in leap_second_dates:
        if dt >= leap_date:
            count += 1
        else:
            break
            
    return count


def utc_to_tai(dt: datetime.datetime) -> datetime.datetime:
    """
    Convert UTC to TAI (International Atomic Time).
    
    Args:
        dt: UTC datetime
        
    Returns:
        TAI datetime
    """
    leap_secs = leap_seconds(dt)
    tai_offset = 32.184 + leap_secs  # TAI = UTC + 32.184 + leap_seconds
    
    return dt + datetime.timedelta(seconds=tai_offset)


def tai_to_utc(dt: datetime.datetime) -> datetime.datetime:
    """
    Convert TAI to UTC.
    
    Args:
        dt: TAI datetime
        
    Returns:
        UTC datetime
    """
    # Approximate UTC for leap second calculation
    approx_utc = dt - datetime.timedelta(seconds=35)  # Rough estimate
    leap_secs = leap_seconds(approx_utc)
    tai_offset = 32.184 + leap_secs
    
    return dt - datetime.timedelta(seconds=tai_offset)


def time_zone_offset(dt: datetime.datetime, timezone: str) -> datetime.timedelta:
    """
    Get timezone offset for given datetime.
    
    Args:
        dt: Datetime object
        timezone: Timezone name (e.g., 'US/Pacific')
        
    Returns:
        Timezone offset as timedelta
    """
    try:
        import pytz
        tz = pytz.timezone(timezone)
        localized_dt = tz.localize(dt.replace(tzinfo=None))
        return localized_dt.utcoffset()
    except ImportError:
        raise ImportError("pytz is required for timezone operations")


def is_leap_year(year: int) -> bool:
    """
    Check if year is a leap year.
    
    Args:
        year: Year to check
        
    Returns:
        True if leap year, False otherwise
    """
    return year % 4 == 0 and (year % 100 != 0 or year % 400 == 0)


def days_in_month(year: int, month: int) -> int:
    """
    Get number of days in given month.
    
    Args:
        year: Year
        month: Month (1-12)
        
    Returns:
        Number of days in month
    """
    days_per_month = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31]
    
    if month == 2 and is_leap_year(year):
        return 29
    else:
        return days_per_month[month - 1]


def day_of_year(dt: datetime.datetime) -> int:
    """
    Get day of year (1-366).
    
    Args:
        dt: Datetime object
        
    Returns:
        Day of year
    """
    return dt.timetuple().tm_yday


def week_of_year(dt: datetime.datetime) -> int:
    """
    Get week of year (1-53).
    
    Args:
        dt: Datetime object
        
    Returns:
        Week of year
    """
    return dt.isocalendar()[1]
