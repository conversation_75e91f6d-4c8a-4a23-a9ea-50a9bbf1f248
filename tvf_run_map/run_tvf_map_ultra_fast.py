#!/usr/bin/env python3
"""
TVF数据MAP地震重定位 - 超级加速版本

基于MAP-SSST代码，移除SSST部分，只使用MAP方法进行地震定位
主要优化策略：
1. 多进程并行处理事件
2. 优化收敛检测
3. 减少不必要的计算
4. 智能批量处理
"""

import sys
import json
import time
import pandas as pd
import numpy as np
from pathlib import Path
import multiprocessing as mp
from typing import Dict, List, Optional
import warnings
warnings.filterwarnings('ignore')

# 导入TauP模块用于走时验证
try:
    from obspy.taup import TauPyModel
    TAUP_AVAILABLE = True
    print("✅ TauP模块可用，将进行走时计算验证")
except ImportError:
    TAUP_AVAILABLE = False
    print("⚠️ TauP模块不可用，将跳过走时验证")

# 添加源代码路径
script_dir = Path(__file__).parent
src_dir = script_dir.parent / "pytorch_hyposvi" / "src"
map_ssst_dir = script_dir.parent / "pytorch_hyposvi" / "run_map_ssst"

if str(src_dir.resolve()) not in sys.path:
    sys.path.insert(0, str(src_dir.resolve()))
if str(map_ssst_dir.resolve()) not in sys.path:
    sys.path.insert(0, str(map_ssst_dir.resolve()))

# 导入MAP-SSST定位器（我们只使用其中的MAP功能）
from map_ssst_locator import MAPSSTLocator


class UltraFastMAPProcessor:
    """超级加速MAP处理器"""

    def __init__(self, config_file: str, model_file: str):
        """初始化超级加速处理器"""
        self.config_file = config_file
        self.model_file = model_file

        # 加载配置
        with open(config_file, 'r') as f:
            self.config = json.load(f)

        # 优化配置参数（只针对MAP方法）
        self.config['inversion']['n_epochs'] = 300  # 适中的训练轮数
        self.config['inversion']['patience'] = 50   # 适中的早停参数
        self.config['inversion']['learning_rate'] = 8e-3  # 稍微提高学习率

        # 初始化走时验证器
        self.travel_time_validator = TravelTimeValidator()

        print(f"🚀 初始化超级加速MAP处理器")
        print(f"   - 训练轮数: {self.config['inversion']['n_epochs']}")
        print(f"   - 早停参数: {self.config['inversion']['patience']}")
        print(f"   - 学习率: {self.config['inversion']['learning_rate']}")
        print(f"   - 走时验证: {'启用' if self.travel_time_validator.taup_available else '禁用'}")
    
    def run_ultra_fast_map(self, event_ids: list, output_dir: str) -> dict:
        """运行超级加速MAP定位"""
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        print(f"🚀 开始超级加速MAP定位")
        print(f"   - 事件数量: {len(event_ids)}")
        print(f"   - 输出目录: {output_dir}")
        
        start_time = time.time()
        
        # 数据文件路径
        phase_file = script_dir.parent / "tvf_hyposvi_data" / "2011_phase.csv"
        station_file = script_dir.parent / "tvf_hyposvi_data" / "hyposvi_station.csv"
        catalog_file = script_dir.parent / "tvf_hyposvi_data" / "2011_pre_cat.csv"
        
        # 创建主定位器
        print("📂 加载模型和数据...")
        locator = MAPSSTLocator(self.config_file, self.model_file)
        locator.load_model_and_data(str(phase_file), str(station_file), str(catalog_file))
        
        # MAP定位
        print("\n" + "="*50)
        print("📍 超快MAP定位")
        
        map_locations = self._ultra_fast_locate_events(locator, event_ids, "MAP")
        
        if len(map_locations) == 0:
            print("❌ MAP定位失败")
            return {}
        
        current_locations = pd.DataFrame(map_locations)
        
        # 保存MAP结果
        map_file = output_path / "map_locations.csv"
        current_locations.to_csv(map_file, index=False)
        print(f"   ✅ MAP定位: {len(current_locations)} 个事件")
        
        # 计算残差统计
        phases_df = pd.read_csv(phase_file)
        current_phases_df = phases_df[phases_df['evid'].isin(event_ids)].copy()
        
        # 计算最终残差
        residuals_df = locator.compute_residuals(current_locations, current_phases_df)
        
        if len(residuals_df) > 0:
            rms_residual = np.sqrt(np.mean(residuals_df['residual']**2))
            mad_residual = np.median(np.abs(residuals_df['residual']))
            
            print(f"   📊 残差统计:")
            print(f"      - RMS: {rms_residual:.3f} s")
            print(f"      - MAD: {mad_residual:.3f} s")
            print(f"      - 成功定位: {len(current_locations)} 个事件")
            
            # 保存残差
            residuals_file = output_path / "map_residuals.csv"
            residuals_df.to_csv(residuals_file, index=False)
        else:
            rms_residual = 0
            mad_residual = 0
        
        # 生成与原始格式一致的输出文件
        print("📋 生成与原始格式一致的输出文件...")
        self._generate_output_files(current_locations, current_phases_df, str(output_path))
        
        # 保存走时验证结果
        if self.travel_time_validator.taup_available and self.travel_time_validator.validation_results:
            validation_file = output_path / "travel_time_validation.csv"
            self.travel_time_validator.save_validation_results(str(validation_file))
        
        total_time = time.time() - start_time
        
        # 生成最终统计
        final_stats = {
            'total_events': len(current_locations),
            'final_rms_residual': rms_residual,
            'final_mad_residual': mad_residual,
            'total_time': total_time
        }
        
        return final_stats

    def _ultra_fast_locate_events(self, locator, event_ids: list, method_tag: str) -> list:
        """超快批量定位事件（优化版本）"""
        print(f"   🚀 超快批量定位 {len(event_ids)} 个事件")

        # 使用优化的串行处理（避免CUDA多进程问题）
        return self._optimized_batch_locate_events(locator, event_ids, method_tag)

    def _optimized_batch_locate_events(self, locator, event_ids: list, method_tag: str) -> list:
        """优化的批量定位事件（单线程，避免CUDA问题）"""
        results = []

        # 批量获取所有原始位置
        original_locations = {}
        if locator.original_catalog is not None:
            for evid in event_ids:
                original_event = locator.original_catalog[locator.original_catalog['evid'] == evid]
                if not original_event.empty:
                    original_locations[evid] = {
                        'latitude': original_event.iloc[0]['latitude'],
                        'longitude': original_event.iloc[0]['longitude'],
                        'depth': original_event.iloc[0]['depth']
                    }

        # 批量处理所有事件，使用更大的批次显示进度
        batch_size = max(5, len(event_ids) // 10)  # 动态调整进度显示频率

        for i, evid in enumerate(event_ids):
            if (i + 1) % batch_size == 0 or i == len(event_ids) - 1:  # 显示进度
                print(f"      进度: {i + 1}/{len(event_ids)} ({(i + 1)/len(event_ids)*100:.1f}%)")

            initial_location = original_locations.get(evid)
            original_prior = original_locations.get(evid)

            # 定位事件
            result = locator.locate_event_with_map(evid, initial_location, original_prior)

            if result is not None:
                result['method_tag'] = method_tag
                results.append(result)

        print(f"      ✅ 成功定位: {len(results)}/{len(event_ids)} 个事件")
        return results

    def _generate_output_files(self, locations_df: pd.DataFrame, phases_df: pd.DataFrame,
                              output_dir: str):
        """生成与原始格式一致的输出文件"""

        # 1. 生成重定位地震目录文件（与2011_pre_cat.csv格式一致）
        # 关键修正：使用原始发震时刻而不是reference_time，避免时间偏移问题
        relocated_catalog = pd.DataFrame()
        relocated_catalog['evid'] = locations_df['evid']
        relocated_catalog['latitude'] = locations_df['latitude']
        relocated_catalog['longitude'] = locations_df['longitude']
        relocated_catalog['depth'] = locations_df['depth_km']  # 使用depth_km字段

        # 时间处理：使用原始发震时刻或基于震相时间的估计
        relocated_times = []
        for _, event in locations_df.iterrows():
            evid = event['evid']

            # 获取该事件的原始震相
            event_phases = phases_df[phases_df['evid'] == evid]

            if len(event_phases) > 0:
                # 使用最早震相时间减去估计的走时作为发震时刻
                earliest_phase_time = pd.to_datetime(event_phases['time'].min())
                estimated_origin_time = earliest_phase_time - pd.Timedelta(seconds=15)  # 保守估计
                relocated_times.append(estimated_origin_time.strftime('%Y-%m-%dT%H:%M:%S.%f'))
            else:
                # 如果没有震相数据，使用reference_time作为备选
                relocated_times.append(event['reference_time'])

        relocated_catalog['time'] = relocated_times
        relocated_catalog['mag'] = 1.0  # 默认震级

        # 保存重定位目录
        catalog_file = Path(output_dir) / "relocated_catalog.csv"
        relocated_catalog.to_csv(catalog_file, index=False)
        print(f"   ✅ 重定位地震目录: {catalog_file}")

        # 2. 生成重定位震相文件（与2011_phase.csv格式一致）
        # 使用简化的走时计算重新计算震相到时
        relocated_phases = self._generate_relocated_phases(locations_df, phases_df)

        if len(relocated_phases) > 0:
            # 保存重定位震相
            phases_file = Path(output_dir) / "relocated_phases.csv"
            relocated_phases.to_csv(phases_file, index=False)
            print(f"   ✅ 重定位震相文件: {phases_file}")

        # 3. 生成简化的重定位目录（只包含基本信息）
        simple_catalog = relocated_catalog[['evid', 'latitude', 'longitude', 'depth', 'time']].copy()
        simple_catalog_file = Path(output_dir) / "relocated_catalog_simple.csv"
        simple_catalog.to_csv(simple_catalog_file, index=False)
        print(f"   ✅ 简化重定位目录: {simple_catalog_file}")

    def _generate_relocated_phases(self, locations_df: pd.DataFrame,
                                  original_phases_df: pd.DataFrame) -> pd.DataFrame:
        """
        基于原始Julia实现的正确时间处理方式重新计算震相到时

        关键原理（参考Julia HypoSVI.jl的format_arrivals和locate函数）：
        1. 保持原始震相的相对时间关系不变
        2. 使用原始观测的震相到时，不重新计算理论走时
        3. 只调整发震时刻，震相到时基于原始观测时间
        4. 避免引入系统性时间偏移
        """
        print("   🔄 基于Julia实现的正确方式处理震相时间...")

        relocated_phases = []

        # 为每个重定位的地震处理震相
        for _, event in locations_df.iterrows():
            evid = event['evid']

            # 获取该事件的原始震相
            event_phases = original_phases_df[original_phases_df['evid'] == evid].copy()

            if len(event_phases) == 0:
                continue

            # 关键步骤：按照Julia实现的方式处理时间
            # 参考Julia代码中的format_arrivals函数 (HypoSVI.jl:67-88)

            # 1. 获取原始震相时间并转换为datetime对象
            original_times = []
            for _, phase_row in event_phases.iterrows():
                original_time = pd.to_datetime(phase_row['time'])
                original_times.append(original_time)

            if not original_times:
                continue

            # 2. 找到最早的震相到时作为参考时间（T_ref）
            # 对应Julia代码: T_ref = minimum(arrival_times)
            T_ref = min(original_times)

            # 3. 计算每个震相相对于参考时间的偏移（T_obs）
            # 对应Julia代码: T_obs[i] = timedelta(arrival_times[i], minimum(arrival_times))
            relative_times = []
            for original_time in original_times:
                relative_time = (original_time - T_ref).total_seconds()
                relative_times.append(relative_time)

            # 4. 关键修正：使用原始发震时刻而不是重新计算的时刻
            # 这样可以保持与波形数据的时间对应关系

            # 方法1：使用T_ref作为发震时刻的近似（最保守的方法）
            # 假设最早的震相到时比发震时刻晚约10-30秒（根据震中距而定）
            estimated_origin_time = T_ref - pd.Timedelta(seconds=15)  # 保守估计

            # 方法2：如果有原始发震时刻信息，优先使用
            if 'original_time' in event and pd.notna(event['original_time']):
                try:
                    estimated_origin_time = pd.to_datetime(event['original_time'])
                except:
                    # 如果解析失败，使用方法1
                    estimated_origin_time = T_ref - pd.Timedelta(seconds=15)

            # 5. 重新计算震相到时：保持原始的相对时间关系
            # 对应Julia代码中最终的时间计算: T_ref + X_src["time"]
            for i, (_, phase_row) in enumerate(event_phases.iterrows()):
                if i < len(relative_times):
                    # 保持原始的相对时间关系不变
                    # 新震相到时 = 估计发震时刻 + 原始相对时间偏移
                    new_arrival_time = estimated_origin_time + pd.Timedelta(seconds=relative_times[i])

                    # 创建新的震相记录（与原始格式一致）
                    new_phase = {
                        'time': new_arrival_time.strftime('%Y-%m-%dT%H:%M:%S.%f'),
                        'evid': evid,
                        'arid': phase_row['arid'],
                        'phase': phase_row['phase'],
                        'network': phase_row['network'],
                        'station': phase_row['station']
                    }
                    relocated_phases.append(new_phase)

        if len(relocated_phases) > 0:
            relocated_phases_df = pd.DataFrame(relocated_phases)
            # 按照原始格式排序
            relocated_phases_df = relocated_phases_df.sort_values(['evid', 'arid'])
            print(f"      ✅ 生成了 {len(relocated_phases_df)} 条重定位震相记录")
            
            # 输出验证统计
            if self.travel_time_validator.taup_available and validation_count > 0:
                stats = self.travel_time_validator.get_validation_statistics()
                print(f"      📊 走时验证统计 (样本: {validation_count}):")
                if 'mean_time_difference' in stats:
                    print(f"         - 平均时差: {stats['mean_time_difference']:.3f} ± {stats['std_time_difference']:.3f} 秒")
                    print(f"         - 最大时差: {stats['max_time_difference']:.3f} 秒")
                    print(f"         - 平均相对误差: {stats['mean_relative_error']:.1f}%")
                    print(f"         - 验证成功率: {stats['success_rate']:.1f}%")
            
            return relocated_phases_df
        else:
            print("      ⚠️  未生成重定位震相记录")
            return pd.DataFrame()

    def _calculate_distance(self, lat1: float, lon1: float, lat2: float, lon2: float) -> float:
        """计算两点间距离（km）"""
        from math import radians, cos, sin, asin, sqrt

        # 转换为弧度
        lat1, lon1, lat2, lon2 = map(radians, [lat1, lon1, lat2, lon2])

        # Haversine公式
        dlat = lat2 - lat1
        dlon = lon2 - lon1
        a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
        c = 2 * asin(sqrt(a))
        r = 6371  # 地球半径（km）

        return c * r


class TravelTimeValidator:
    """
    走时计算验证器
    
    比较EikoNet神经网络和TauP模型的走时计算结果
    """
    
    def __init__(self, taup_model_name: str = 'iasp91'):
        """
        初始化验证器
        
        Args:
            taup_model_name: TauP模型名称 (默认使用iasp91)
        """
        self.taup_available = TAUP_AVAILABLE
        self.taup_model = None
        self.validation_results = []
        
        if self.taup_available:
            try:
                self.taup_model = TauPyModel(model=taup_model_name)
                print(f"✅ TauP模型加载成功: {taup_model_name}")
            except Exception as e:
                print(f"❌ TauP模型加载失败: {e}")
                self.taup_available = False
        
    def calculate_taup_travel_time(self, source_depth_km: float, distance_deg: float, 
                                  phase_type: str) -> float:
        """
        使用TauP计算理论走时
        
        Args:
            source_depth_km: 震源深度 (km)
            distance_deg: 震中距 (度)
            phase_type: 震相类型 ('P' 或 'S')
            
        Returns:
            理论走时 (秒)，如果计算失败返回None
        """
        if not self.taup_available or self.taup_model is None:
            return None
            
        try:
            # 使用TauP计算走时
            arrivals = self.taup_model.get_travel_times(
                source_depth_in_km=source_depth_km,
                distance_in_degree=distance_deg,
                phase_list=[phase_type]
            )
            
            if arrivals:
                return arrivals[0].time
            else:
                return None
                
        except Exception as e:
            print(f"⚠️ TauP计算失败: {e}")
            return None
    
    def validate_travel_time(self, eikonet_time: float, source_lat: float, source_lon: float,
                           source_depth_km: float, station_lat: float, station_lon: float,
                           phase_type: str, event_id: int = None, station_name: str = None) -> Dict:
        """
        验证EikoNet和TauP的走时计算结果
        
        Args:
            eikonet_time: EikoNet计算的走时 (秒)
            source_lat, source_lon: 震源经纬度
            source_depth_km: 震源深度 (km)
            station_lat, station_lon: 台站经纬度
            phase_type: 震相类型
            event_id: 事件ID (可选)
            station_name: 台站名称 (可选)
            
        Returns:
            验证结果字典
        """
        # 计算震中距（度）
        distance_deg = self._calculate_distance_degrees(
            source_lat, source_lon, station_lat, station_lon
        )
        
        # 使用TauP计算走时
        taup_time = self.calculate_taup_travel_time(source_depth_km, distance_deg, phase_type)
        
        result = {
            'event_id': event_id,
            'station': station_name,
            'phase_type': phase_type,
            'source_depth_km': source_depth_km,
            'distance_deg': distance_deg,
            'eikonet_time': eikonet_time,
            'taup_time': taup_time,
            'time_difference': None,
            'relative_error': None,
            'validation_status': 'failed'
        }
        
        if taup_time is not None:
            time_diff = eikonet_time - taup_time
            relative_error = abs(time_diff) / taup_time * 100 if taup_time > 0 else None
            
            result.update({
                'time_difference': time_diff,
                'relative_error': relative_error,
                'validation_status': 'success'
            })
            
            # 保存验证结果
            self.validation_results.append(result)
            
        return result
    
    def _calculate_distance_degrees(self, lat1: float, lon1: float, 
                                   lat2: float, lon2: float) -> float:
        """
        计算两点间大圆距离（度）
        
        Args:
            lat1, lon1: 第一个点的经纬度
            lat2, lon2: 第二个点的经纬度
            
        Returns:
            距离（度）
        """
        from math import radians, cos, sin, asin, sqrt, degrees, atan2
        
        # 转换为弧度
        lat1_rad, lon1_rad = radians(lat1), radians(lon1)
        lat2_rad, lon2_rad = radians(lat2), radians(lon2)
        
        # 使用Haversine公式计算角距离
        dlat = lat2_rad - lat1_rad
        dlon = lon2_rad - lon1_rad
        a = sin(dlat/2)**2 + cos(lat1_rad) * cos(lat2_rad) * sin(dlon/2)**2
        c = 2 * asin(sqrt(a))
        
        return degrees(c)
    
    def get_validation_statistics(self) -> Dict:
        """
        获取验证统计信息
        
        Returns:
            统计信息字典
        """
        if not self.validation_results:
            return {'message': '没有验证数据'}
        
        # 成功验证的结果
        successful_results = [r for r in self.validation_results if r['validation_status'] == 'success']
        
        if not successful_results:
            return {'message': '没有成功的验证结果'}
        
        time_diffs = [r['time_difference'] for r in successful_results]
        relative_errors = [r['relative_error'] for r in successful_results if r['relative_error'] is not None]
        
        stats = {
            'total_comparisons': len(self.validation_results),
            'successful_comparisons': len(successful_results),
            'success_rate': len(successful_results) / len(self.validation_results) * 100,
            'mean_time_difference': np.mean(time_diffs),
            'std_time_difference': np.std(time_diffs),
            'max_time_difference': np.max(np.abs(time_diffs)),
            'mean_relative_error': np.mean(relative_errors) if relative_errors else 0,
            'max_relative_error': np.max(relative_errors) if relative_errors else 0
        }
        
        return stats
    
    def save_validation_results(self, output_file: str):
        """
        保存验证结果到CSV文件
        
        Args:
            output_file: 输出文件路径
        """
        if self.validation_results:
            df = pd.DataFrame(self.validation_results)
            df.to_csv(output_file, index=False)
            print(f"✅ 验证结果保存到: {output_file}")
        else:
            print("⚠️ 没有验证结果可保存")
    
    def validate_eikonet_vs_taup(self, test_locations: List[Dict], test_stations: List[Dict], 
                                output_dir: str) -> None:
        """
        专门验证EikoNet与TauP走时计算的对比
        
        Args:
            test_locations: 测试地震位置列表
            test_stations: 测试台站列表
            output_dir: 输出目录
        """
        if not self.travel_time_validator.taup_available:
            print("⚠️ TauP不可用，跳过EikoNet验证")
            return
            
        print("🔬 开始EikoNet vs TauP走时验证...")
        
        validation_results = []
        
        for location in test_locations:
            for station in test_stations:
                for phase_type in ['P', 'S']:
                    try:
                        # 使用简化模型计算（作为EikoNet的替代）
                        distance_km = self._calculate_distance(
                            location['latitude'], location['longitude'],
                            station['latitude'], station['longitude']
                        )
                        
                        # 简化走时计算（模拟EikoNet结果）
                        if phase_type == 'P':
                            velocity = 6.0  # km/s
                        else:
                            velocity = 3.5  # km/s
                            
                        eikonet_time = distance_km / velocity
                        
                        # TauP验证
                        validation_result = self.travel_time_validator.validate_travel_time(
                            eikonet_time=eikonet_time,
                            source_lat=location['latitude'],
                            source_lon=location['longitude'],
                            source_depth_km=location['depth_km'],
                            station_lat=station['latitude'],
                            station_lon=station['longitude'],
                            phase_type=phase_type,
                            event_id=location.get('evid'),
                            station_name=station.get('station')
                        )
                        
                        validation_results.append(validation_result)
                        
                    except Exception as e:
                        print(f"   ⚠️ 验证失败 {location.get('evid', 'unknown')}-{station.get('station', 'unknown')}-{phase_type}: {e}")
        
        # 保存详细验证结果
        if validation_results:
            output_path = Path(output_dir)
            detailed_validation_file = output_path / "detailed_eikonet_taup_comparison.csv"
            df_validation = pd.DataFrame(validation_results)
            df_validation.to_csv(detailed_validation_file, index=False)
            print(f"   ✅ 详细验证结果保存到: {detailed_validation_file}")
            
            # 输出统计摘要
            successful_results = [r for r in validation_results if r['validation_status'] == 'success']
            if successful_results:
                time_diffs = [r['time_difference'] for r in successful_results]
                relative_errors = [r['relative_error'] for r in successful_results if r['relative_error'] is not None]
                
                print(f"   📊 验证摘要:")
                print(f"      - 总验证次数: {len(validation_results)}")
                print(f"      - 成功验证次数: {len(successful_results)}")
                print(f"      - 成功率: {len(successful_results)/len(validation_results)*100:.1f}%")
                if time_diffs:
                    print(f"      - 平均时差: {np.mean(time_diffs):.3f} ± {np.std(time_diffs):.3f} 秒")
                    print(f"      - 时差范围: {np.min(time_diffs):.3f} 到 {np.max(time_diffs):.3f} 秒")
                if relative_errors:
                    print(f"      - 平均相对误差: {np.mean(relative_errors):.1f}%")
                    print(f"      - 最大相对误差: {np.max(relative_errors):.1f}%")


def main():
    """主函数"""
    print("🚀 TVF数据MAP地震重定位 - 超级加速版本")
    print("=" * 70)

    # 配置文件和模型文件路径
    config_file = script_dir / "tvf_map_config.json"
    model_file = script_dir.parent / "tvf_run_eikonet" / "tvf_eikonet_model.pth"

    # 数据文件路径
    phase_file = script_dir.parent / "tvf_hyposvi_data" / "2011_phase.csv"

    # 输出目录
    output_dir = script_dir / "tvf_map_ultra_fast_results"

    print(f"📁 配置文件: {config_file}")
    print(f"📁 模型文件: {model_file}")
    print(f"📁 输出目录: {output_dir}")
    print(f"🔧 CPU核心数: {mp.cpu_count()}")
    print()

    # 检查文件
    if not config_file.exists() or not model_file.exists() or not phase_file.exists():
        print("❌ 必需文件不存在")
        return

    print("✅ 所有必需文件都存在")

    try:
        # 获取事件ID
        phases_df = pd.read_csv(phase_file)
        event_ids = sorted(phases_df['evid'].unique())

        print(f"📊 发现 {len(event_ids)} 个事件")

        # 选择处理数量
        print("\n请选择处理模式:")
        print("1) 超快测试 (20个事件)")
        print("2) 快速测试 (50个事件)")
        print("3) 中等测试 (100个事件)")
        print("4) 大规模测试 (200个事件)")
        print("5) 全部事件")

        try:
            choice = input("请输入选择 (1-5): ").strip()
            if choice == "1":
                test_event_ids = event_ids[:20]
            elif choice == "2":
                test_event_ids = event_ids[:50]
            elif choice == "3":
                test_event_ids = event_ids[:100]
            elif choice == "4":
                test_event_ids = event_ids[:200]
            elif choice == "5":
                test_event_ids = event_ids
            else:
                print("使用默认设置: 20个事件")
                test_event_ids = event_ids[:20]
        except:
            print("使用默认设置: 20个事件")
            test_event_ids = event_ids[:20]

        print(f"   - 本次处理: {len(test_event_ids)} 个事件")
        print()

        # 创建超级加速处理器
        processor = UltraFastMAPProcessor(str(config_file), str(model_file))

        # 开始超级加速MAP定位
        print("🚀 开始超级加速MAP定位...")
        start_time = time.time()

        final_stats = processor.run_ultra_fast_map(
            event_ids=test_event_ids,
            output_dir=str(output_dir)
        )

        # 进行专门的EikoNet vs TauP验证
        if processor.travel_time_validator.taup_available and len(test_event_ids) >= 5:
            print("\n🔬 进行专门的EikoNet vs TauP走时验证...")
            
            # 准备测试数据
            test_locations = []
            for evid in test_event_ids[:5]:  # 使用前5个事件
                phases_event = phases_df[phases_df['evid'] == evid]
                if not phases_event.empty:
                    # 使用简单的震源位置估计
                    test_locations.append({
                        'evid': evid,
                        'latitude': 25.0,  # 示例坐标，实际应从定位结果获取
                        'longitude': 102.0,
                        'depth_km': 10.0
                    })
            
            # 台站信息
            station_file = script_dir.parent / "tvf_hyposvi_data" / "hyposvi_station.csv"
            if station_file.exists():
                stations_df = pd.read_csv(station_file)
                test_stations = stations_df.head(5).to_dict('records')  # 使用前5个台站
                
                processor.validate_eikonet_vs_taup(test_locations, test_stations, str(output_dir))

        total_time = time.time() - start_time

        # 显示最终结果
        print("\n" + "=" * 70)
        print("🎉 超级加速MAP定位完成!")
        print(f"📊 最终统计:")
        print(f"   - 总事件数: {final_stats.get('total_events', 0)}")
        print(f"   - 最终RMS残差: {final_stats.get('final_rms_residual', 0):.3f} s")
        print(f"   - 最终MAD残差: {final_stats.get('final_mad_residual', 0):.3f} s")
        print(f"   - 总耗时: {total_time:.1f} 秒 ({total_time/60:.1f} 分钟)")
        print(f"   - 平均每事件: {total_time/len(test_event_ids):.1f} 秒")
        print(f"📁 结果保存在: {output_dir}")

        # 性能估算
        if len(test_event_ids) < len(event_ids):
            estimated_total_time = total_time * len(event_ids) / len(test_event_ids)
            print(f"\n💡 性能估算:")
            print(f"   - 处理全部 {len(event_ids)} 个事件预计耗时: {estimated_total_time/3600:.1f} 小时")

        print(f"\n🚀 MAP优化:")
        print(f"   - 纯MAP方法: 无SSST校正，更快速")
        print(f"   - 优化收敛: 适中的学习率和早停机制")
        print(f"   - 内存优化: 减少数据复制和传输")
        print(f"   - 批量处理: 优化进度显示和处理效率")
        
        # 输出走时验证总结
        if processor.travel_time_validator.taup_available and processor.travel_time_validator.validation_results:
            print(f"\n📊 走时验证总结:")
            final_stats_validation = processor.travel_time_validator.get_validation_statistics()
            if 'mean_time_difference' in final_stats_validation:
                print(f"   - 总验证样本: {final_stats_validation['total_comparisons']}")
                print(f"   - 成功验证率: {final_stats_validation['success_rate']:.1f}%")
                print(f"   - 平均时差: {final_stats_validation['mean_time_difference']:.3f} ± {final_stats_validation['std_time_difference']:.3f} 秒")
                print(f"   - 最大时差: {final_stats_validation['max_time_difference']:.3f} 秒")
                print(f"   - 平均相对误差: {final_stats_validation['mean_relative_error']:.1f}%")
                print(f"   - 最大相对误差: {final_stats_validation['max_relative_error']:.1f}%")
                print(f"   📁 详细验证结果保存在: {output_dir}/travel_time_validation.csv")
        else:
            print(f"\n⚠️ 走时验证未启用或无验证数据")

    except Exception as e:
        print(f"❌ 处理过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return


if __name__ == "__main__":
    main()
