#!/usr/bin/env python3
"""
测试走时验证功能

这个脚本专门测试TauP与EikoNet/简化模型的走时计算对比
"""

import sys
import pandas as pd
import numpy as np
from pathlib import Path
import json

# 添加源代码路径
script_dir = Path(__file__).parent
src_dir = script_dir.parent / "pytorch_hyposvi" / "src"
if str(src_dir.resolve()) not in sys.path:
    sys.path.insert(0, str(src_dir.resolve()))

# 尝试导入TauP
try:
    from obspy.taup import TauPyModel
    TAUP_AVAILABLE = True
    print("✅ TauP模块可用")
except ImportError:
    TAUP_AVAILABLE = False
    print("❌ TauP模块不可用")

class TravelTimeComparison:
    """走时计算对比器"""
    
    def __init__(self):
        """初始化对比器"""
        self.taup_model = None
        self.taup_available = TAUP_AVAILABLE
        if self.taup_available:
            try:
                self.taup_model = TauPyModel(model='iasp91')
                print("✅ TauP模型加载成功: iasp91")
            except Exception as e:
                print(f"❌ TauP模型加载失败: {e}")
                self.taup_available = False
    
    def calculate_distance_km(self, lat1, lon1, lat2, lon2):
        """计算两点间距离（km）"""
        from math import radians, cos, sin, asin, sqrt
        
        lat1, lon1, lat2, lon2 = map(radians, [lat1, lon1, lat2, lon2])
        dlat = lat2 - lat1
        dlon = lon2 - lon1
        a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
        c = 2 * asin(sqrt(a))
        return 6371 * c  # 地球半径
    
    def calculate_distance_degrees(self, lat1, lon1, lat2, lon2):
        """计算两点间大圆距离（度）"""
        from math import radians, cos, sin, asin, sqrt, degrees
        
        lat1_rad, lon1_rad = radians(lat1), radians(lon1)
        lat2_rad, lon2_rad = radians(lat2), radians(lon2)
        
        dlat = lat2_rad - lat1_rad
        dlon = lon2_rad - lon1_rad
        a = sin(dlat/2)**2 + cos(lat1_rad) * cos(lat2_rad) * sin(dlon/2)**2
        c = 2 * asin(sqrt(a))
        
        return degrees(c)
    
    def calculate_simple_travel_time(self, distance_km, phase_type):
        """使用简化速度模型计算走时"""
        if phase_type == 'P':
            velocity = 6.0  # km/s
        elif phase_type == 'S':
            velocity = 3.5  # km/s
        else:
            return None
        
        return distance_km / velocity
    
    def calculate_taup_travel_time(self, source_depth_km, distance_deg, phase_type):
        """使用TauP计算理论走时"""
        if not self.taup_available or self.taup_model is None:
            return None
        
        try:
            arrivals = self.taup_model.get_travel_times(
                source_depth_in_km=source_depth_km,
                distance_in_degree=distance_deg,
                phase_list=[phase_type]
            )
            
            if arrivals:
                return arrivals[0].time
            else:
                return None
        except Exception as e:
            print(f"⚠️ TauP计算失败: {e}")
            return None
    
    def compare_travel_times(self, source_lat, source_lon, source_depth_km,
                           station_lat, station_lon, phase_type):
        """比较不同方法的走时计算结果"""
        
        # 计算距离
        distance_km = self.calculate_distance_km(source_lat, source_lon, station_lat, station_lon)
        distance_deg = self.calculate_distance_degrees(source_lat, source_lon, station_lat, station_lon)
        
        # 简化模型走时
        simple_time = self.calculate_simple_travel_time(distance_km, phase_type)
        
        # TauP走时
        taup_time = self.calculate_taup_travel_time(source_depth_km, distance_deg, phase_type)
        
        result = {
            'distance_km': distance_km,
            'distance_deg': distance_deg,
            'source_depth_km': source_depth_km,
            'phase_type': phase_type,
            'simple_time': simple_time,
            'taup_time': taup_time,
            'time_difference': None,
            'relative_error': None,
            'status': 'failed'
        }
        
        if simple_time is not None and taup_time is not None:
            time_diff = simple_time - taup_time
            relative_error = abs(time_diff) / taup_time * 100 if taup_time > 0 else None
            
            result.update({
                'time_difference': time_diff,
                'relative_error': relative_error,
                'status': 'success'
            })
        
        return result

def load_test_data():
    """加载测试数据"""
    script_dir = Path(__file__).parent
    
    # 加载地震目录
    catalog_file = script_dir.parent / "tvf_hyposvi_data" / "2011_pre_cat.csv"
    catalog_df = pd.read_csv(catalog_file)
    
    # 加载台站信息
    station_file = script_dir.parent / "tvf_hyposvi_data" / "hyposvi_station.csv"
    stations_df = pd.read_csv(station_file)
    
    # 加载震相数据
    phase_file = script_dir.parent / "tvf_hyposvi_data" / "2011_phase.csv"
    phases_df = pd.read_csv(phase_file)
    
    return catalog_df, stations_df, phases_df

def run_comprehensive_test():
    """运行综合测试"""
    print("🧪 开始走时计算对比测试")
    print("=" * 60)
    
    # 初始化对比器
    comparator = TravelTimeComparison()
    
    if not TAUP_AVAILABLE:
        print("❌ TauP不可用，无法进行对比测试")
        return
    
    # 加载数据
    print("📂 加载测试数据...")
    catalog_df, stations_df, phases_df = load_test_data()
    
    print(f"   - 地震事件: {len(catalog_df)}")
    print(f"   - 台站数量: {len(stations_df)}")
    print(f"   - 震相记录: {len(phases_df)}")
    
    # 选择测试样本
    test_events = catalog_df.head(5)  # 前5个事件
    test_stations = stations_df.head(10)  # 前10个台站
    test_phases = ['P', 'S']
    
    print(f"\n🔬 测试设置:")
    print(f"   - 测试事件: {len(test_events)}")
    print(f"   - 测试台站: {len(test_stations)}")
    print(f"   - 测试震相: {test_phases}")
    print(f"   - 总测试数: {len(test_events) * len(test_stations) * len(test_phases)}")
    
    # 进行对比测试
    results = []
    
    for _, event in test_events.iterrows():
        for _, station in test_stations.iterrows():
            for phase_type in test_phases:
                try:
                    result = comparator.compare_travel_times(
                        source_lat=event['latitude'],
                        source_lon=event['longitude'],
                        source_depth_km=event['depth'],
                        station_lat=station['latitude'],
                        station_lon=station['longitude'],
                        phase_type=phase_type
                    )
                    
                    result.update({
                        'evid': event['evid'],
                        'station': station['station']
                    })
                    
                    results.append(result)
                    
                except Exception as e:
                    print(f"   ⚠️ 测试失败: 事件{event['evid']}-台站{station['station']}-{phase_type}: {e}")
    
    # 保存详细结果
    if results:
        results_df = pd.DataFrame(results)
        output_file = script_dir / "travel_time_comparison_results.csv"
        results_df.to_csv(output_file, index=False)
        print(f"\n📊 详细结果保存到: {output_file}")
        
        # 统计分析
        successful_results = results_df[results_df['status'] == 'success']
        
        if len(successful_results) > 0:
            print(f"\n📈 统计分析:")
            print(f"   - 总测试数: {len(results)}")
            print(f"   - 成功测试数: {len(successful_results)}")
            print(f"   - 成功率: {len(successful_results)/len(results)*100:.1f}%")
            
            # P波和S波分别统计
            for phase in ['P', 'S']:
                phase_results = successful_results[successful_results['phase_type'] == phase]
                if len(phase_results) > 0:
                    time_diffs = phase_results['time_difference']
                    rel_errors = phase_results['relative_error']
                    
                    print(f"\n   {phase}波统计:")
                    print(f"      - 样本数: {len(phase_results)}")
                    print(f"      - 时差均值: {time_diffs.mean():.3f} ± {time_diffs.std():.3f} 秒")
                    print(f"      - 时差范围: [{time_diffs.min():.3f}, {time_diffs.max():.3f}] 秒")
                    print(f"      - 相对误差: {rel_errors.mean():.1f} ± {rel_errors.std():.1f} %")
                    print(f"      - 最大相对误差: {rel_errors.max():.1f}%")
            
            # 距离vs误差分析
            print(f"\n🎯 距离vs误差分析:")
            distance_bins = [0, 50, 100, 200, 300, float('inf')]
            distance_labels = ['0-50km', '50-100km', '100-200km', '200-300km', '>300km']
            
            for i, (bin_start, bin_end) in enumerate(zip(distance_bins[:-1], distance_bins[1:])):
                bin_data = successful_results[
                    (successful_results['distance_km'] >= bin_start) & 
                    (successful_results['distance_km'] < bin_end)
                ]
                
                if len(bin_data) > 0:
                    avg_rel_error = bin_data['relative_error'].mean()
                    print(f"      - {distance_labels[i]}: {len(bin_data)}样本, 平均相对误差: {avg_rel_error:.1f}%")
        
        else:
            print("❌ 没有成功的测试结果")
    
    else:
        print("❌ 没有测试结果")

def run_real_data_test():
    """使用真实震相数据进行测试"""
    print("\n🌍 真实震相数据测试")
    print("=" * 60)
    
    comparator = TravelTimeComparison()
    
    if not TAUP_AVAILABLE:
        print("❌ TauP不可用，跳过真实数据测试")
        return
    
    # 加载数据
    catalog_df, stations_df, phases_df = load_test_data()
    
    # 选择一个有足够震相的事件
    event_phase_counts = phases_df.groupby('evid').size()
    rich_event_id = event_phase_counts.idxmax()  # 震相最多的事件
    
    print(f"📍 选择事件 {rich_event_id} (震相数: {event_phase_counts[rich_event_id]})")
    
    # 获取事件信息
    event_info = catalog_df[catalog_df['evid'] == rich_event_id].iloc[0]
    event_phases = phases_df[phases_df['evid'] == rich_event_id]
    
    print(f"   - 位置: ({event_info['latitude']:.3f}, {event_info['longitude']:.3f})")
    print(f"   - 深度: {event_info['depth']:.1f} km")
    print(f"   - 震相数: {len(event_phases)}")
    
    # 对每个震相进行走时对比
    real_results = []
    
    for _, phase in event_phases.iterrows():
        station_info = stations_df[stations_df['station'] == phase['station']]
        
        if station_info.empty:
            continue
        
        station_info = station_info.iloc[0]
        
        try:
            result = comparator.compare_travel_times(
                source_lat=event_info['latitude'],
                source_lon=event_info['longitude'],
                source_depth_km=event_info['depth'],
                station_lat=station_info['latitude'],
                station_lon=station_info['longitude'],
                phase_type=phase['phase']
            )
            
            result.update({
                'evid': rich_event_id,
                'station': phase['station'],
                'arid': phase['arid'],
                'observed_time': phase['time']
            })
            
            real_results.append(result)
            
        except Exception as e:
            print(f"   ⚠️ 处理失败: {phase['station']}-{phase['phase']}: {e}")
    
    # 分析真实数据结果
    if real_results:
        real_df = pd.DataFrame(real_results)
        real_output_file = script_dir / "real_data_comparison_results.csv"
        real_df.to_csv(real_output_file, index=False)
        
        successful_real = real_df[real_df['status'] == 'success']
        
        print(f"\n📊 真实数据分析:")
        print(f"   - 处理震相数: {len(real_df)}")
        print(f"   - 成功计算数: {len(successful_real)}")
        print(f"   - 成功率: {len(successful_real)/len(real_df)*100:.1f}%")
        
        if len(successful_real) > 0:
            # 按震相类型分析
            for phase_type in ['P', 'S']:
                phase_data = successful_real[successful_real['phase_type'] == phase_type]
                if len(phase_data) > 0:
                    print(f"\n   {phase_type}波 ({len(phase_data)}个):")
                    print(f"      - 简化模型走时: {phase_data['simple_time'].mean():.2f} ± {phase_data['simple_time'].std():.2f} 秒")
                    print(f"      - TauP走时: {phase_data['taup_time'].mean():.2f} ± {phase_data['taup_time'].std():.2f} 秒")
                    print(f"      - 平均时差: {phase_data['time_difference'].mean():.3f} ± {phase_data['time_difference'].std():.3f} 秒")
                    print(f"      - 平均相对误差: {phase_data['relative_error'].mean():.1f}%")
        
        print(f"\n✅ 真实数据结果保存到: {real_output_file}")

def main():
    """主函数"""
    print("🧪 走时验证测试程序")
    print("=" * 70)
    
    # 运行综合测试
    run_comprehensive_test()
    
    # 运行真实数据测试
    run_real_data_test()
    
    print("\n🎉 所有测试完成！")
    print("现在可以运行主程序进行带TauP验证的MAP定位了。")

if __name__ == "__main__":
    main()
