#!/usr/bin/env python3
"""
地震重定位时间问题修复脚本

基于原始Julia实现的正确时间处理方式，修复重定位后地震震相与波形数据无法对应的问题。

问题分析：
1. 原始Python实现使用了错误的时间基准，导致重定位后的震相时间发生了系统性偏移
2. Julia实现使用相对时间差的方式，保持了震相间的相对时间关系
3. 需要修正时间处理逻辑，确保重定位后的震相时间与波形数据保持对应

解决方案：
1. 使用原始观测的震相到时作为基准
2. 保持震相间的相对时间关系不变
3. 只调整发震时刻，不重新计算理论走时
"""

import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime, timedelta
import json

def analyze_timing_differences(original_catalog_file, original_phases_file, 
                             relocated_catalog_file, relocated_phases_file):
    """分析重定位前后的时间差异"""
    
    print("🔍 分析重定位前后的时间差异...")
    
    # 读取数据
    orig_catalog = pd.read_csv(original_catalog_file)
    orig_phases = pd.read_csv(original_phases_file)
    reloc_catalog = pd.read_csv(relocated_catalog_file)
    reloc_phases = pd.read_csv(relocated_phases_file)
    
    # 转换时间格式
    orig_catalog['time'] = pd.to_datetime(orig_catalog['time'])
    orig_phases['time'] = pd.to_datetime(orig_phases['time'])
    reloc_catalog['time'] = pd.to_datetime(reloc_catalog['time'])
    reloc_phases['time'] = pd.to_datetime(reloc_phases['time'])
    
    # 分析发震时刻差异
    print("\n📊 发震时刻差异分析:")
    origin_time_diffs = []
    
    for evid in orig_catalog['evid'].unique():
        if evid in reloc_catalog['evid'].values:
            orig_time = orig_catalog[orig_catalog['evid'] == evid]['time'].iloc[0]
            reloc_time = reloc_catalog[reloc_catalog['evid'] == evid]['time'].iloc[0]
            time_diff = (reloc_time - orig_time).total_seconds()
            origin_time_diffs.append(time_diff)
    
    if origin_time_diffs:
        print(f"   - 平均发震时刻差异: {np.mean(origin_time_diffs):.3f} ± {np.std(origin_time_diffs):.3f} 秒")
        print(f"   - 差异范围: {np.min(origin_time_diffs):.3f} 到 {np.max(origin_time_diffs):.3f} 秒")
    
    # 分析震相到时差异
    print("\n📊 震相到时差异分析:")
    phase_time_diffs = []
    
    # 合并原始和重定位震相数据
    orig_phases_key = orig_phases.set_index(['evid', 'station', 'phase'])
    reloc_phases_key = reloc_phases.set_index(['evid', 'station', 'phase'])
    
    common_keys = orig_phases_key.index.intersection(reloc_phases_key.index)
    
    for key in common_keys:
        orig_time = orig_phases_key.loc[key, 'time']
        reloc_time = reloc_phases_key.loc[key, 'time']
        time_diff = (reloc_time - orig_time).total_seconds()
        phase_time_diffs.append(time_diff)
    
    if phase_time_diffs:
        print(f"   - 平均震相到时差异: {np.mean(phase_time_diffs):.3f} ± {np.std(phase_time_diffs):.3f} 秒")
        print(f"   - 差异范围: {np.min(phase_time_diffs):.3f} 到 {np.max(phase_time_diffs):.3f} 秒")
        print(f"   - 分析的震相数量: {len(phase_time_diffs)}")
    
    return {
        'origin_time_diffs': origin_time_diffs,
        'phase_time_diffs': phase_time_diffs,
        'common_phases': len(common_keys)
    }

def fix_relocated_timing(original_catalog_file, original_phases_file,
                        relocated_catalog_file, relocated_phases_file,
                        output_dir):
    """修复重定位后的时间问题"""
    
    print("🔧 修复重定位后的时间问题...")
    
    # 读取数据
    orig_catalog = pd.read_csv(original_catalog_file)
    orig_phases = pd.read_csv(original_phases_file)
    reloc_catalog = pd.read_csv(relocated_catalog_file)
    
    # 转换时间格式
    orig_catalog['time'] = pd.to_datetime(orig_catalog['time'])
    orig_phases['time'] = pd.to_datetime(orig_phases['time'])
    reloc_catalog['time'] = pd.to_datetime(reloc_catalog['time'])
    
    # 创建修复后的数据
    fixed_catalog = reloc_catalog.copy()
    fixed_phases = []
    
    print("   🔄 处理每个事件的时间修复...")
    
    for _, event in reloc_catalog.iterrows():
        evid = event['evid']
        
        # 获取原始震相数据
        orig_event_phases = orig_phases[orig_phases['evid'] == evid].copy()
        
        if len(orig_event_phases) == 0:
            continue
        
        # 按照Julia实现的方式处理时间
        # 1. 找到最早的震相到时作为参考时间
        orig_times = pd.to_datetime(orig_event_phases['time'])
        T_ref = orig_times.min()
        
        # 2. 计算相对时间偏移
        relative_times = [(t - T_ref).total_seconds() for t in orig_times]
        
        # 3. 使用原始发震时刻（如果有的话）
        if evid in orig_catalog['evid'].values:
            orig_origin_time = orig_catalog[orig_catalog['evid'] == evid]['time'].iloc[0]
        else:
            # 如果没有原始发震时刻，使用保守估计
            orig_origin_time = T_ref - pd.Timedelta(seconds=15)
        
        # 4. 修复地震目录中的发震时刻
        fixed_catalog.loc[fixed_catalog['evid'] == evid, 'time'] = orig_origin_time
        
        # 5. 修复震相到时：保持原始的相对时间关系
        for i, (_, phase_row) in enumerate(orig_event_phases.iterrows()):
            if i < len(relative_times):
                # 新震相到时 = 原始发震时刻 + 原始相对时间偏移
                new_arrival_time = orig_origin_time + pd.Timedelta(seconds=relative_times[i])
                
                fixed_phase = {
                    'time': new_arrival_time.strftime('%Y-%m-%dT%H:%M:%S.%f'),
                    'evid': evid,
                    'arid': phase_row['arid'],
                    'phase': phase_row['phase'],
                    'network': phase_row['network'],
                    'station': phase_row['station']
                }
                fixed_phases.append(fixed_phase)
    
    # 保存修复后的数据
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    # 保存修复后的地震目录
    fixed_catalog_file = output_path / "fixed_relocated_catalog.csv"
    fixed_catalog.to_csv(fixed_catalog_file, index=False)
    print(f"   ✅ 修复后的地震目录: {fixed_catalog_file}")
    
    # 保存修复后的震相文件
    if fixed_phases:
        fixed_phases_df = pd.DataFrame(fixed_phases)
        fixed_phases_df = fixed_phases_df.sort_values(['evid', 'arid'])
        
        fixed_phases_file = output_path / "fixed_relocated_phases.csv"
        fixed_phases_df.to_csv(fixed_phases_file, index=False)
        print(f"   ✅ 修复后的震相文件: {fixed_phases_file}")
        
        return fixed_catalog_file, fixed_phases_file
    else:
        print("   ⚠️  没有生成修复后的震相数据")
        return fixed_catalog_file, None

def validate_timing_fix(original_phases_file, fixed_phases_file):
    """验证时间修复的效果"""
    
    print("✅ 验证时间修复效果...")
    
    orig_phases = pd.read_csv(original_phases_file)
    fixed_phases = pd.read_csv(fixed_phases_file)
    
    orig_phases['time'] = pd.to_datetime(orig_phases['time'])
    fixed_phases['time'] = pd.to_datetime(fixed_phases['time'])
    
    # 检查相对时间关系是否保持
    validation_results = []
    
    for evid in orig_phases['evid'].unique():
        if evid in fixed_phases['evid'].values:
            orig_event = orig_phases[orig_phases['evid'] == evid].sort_values('time')
            fixed_event = fixed_phases[fixed_phases['evid'] == evid].sort_values('time')
            
            if len(orig_event) > 1 and len(fixed_event) > 1:
                # 计算原始相对时间差
                orig_rel_times = [(t - orig_event['time'].iloc[0]).total_seconds() 
                                for t in orig_event['time']]
                
                # 计算修复后相对时间差
                fixed_rel_times = [(t - fixed_event['time'].iloc[0]).total_seconds() 
                                 for t in fixed_event['time']]
                
                # 比较相对时间差的一致性
                if len(orig_rel_times) == len(fixed_rel_times):
                    time_consistency = np.allclose(orig_rel_times, fixed_rel_times, atol=0.001)
                    validation_results.append(time_consistency)
    
    if validation_results:
        success_rate = np.mean(validation_results) * 100
        print(f"   📊 相对时间关系保持率: {success_rate:.1f}%")
        
        if success_rate > 95:
            print("   ✅ 时间修复成功！相对时间关系得到很好保持")
        elif success_rate > 80:
            print("   ⚠️  时间修复基本成功，但有少量不一致")
        else:
            print("   ❌ 时间修复可能存在问题")
    
    return validation_results

def main():
    """主函数"""
    print("🔧 地震重定位时间问题修复工具")
    print("=" * 60)
    
    # 文件路径
    script_dir = Path(__file__).parent
    data_dir = script_dir.parent / "tvf_hyposvi_data"
    results_dir = script_dir / "tvf_map_ultra_fast_results"
    
    original_catalog_file = data_dir / "2011_pre_cat.csv"
    original_phases_file = data_dir / "2011_phase.csv"
    relocated_catalog_file = results_dir / "relocated_catalog.csv"
    relocated_phases_file = results_dir / "relocated_phases.csv"
    
    # 检查文件存在性
    required_files = [original_catalog_file, original_phases_file, 
                     relocated_catalog_file, relocated_phases_file]
    
    missing_files = [f for f in required_files if not f.exists()]
    if missing_files:
        print("❌ 以下必需文件不存在:")
        for f in missing_files:
            print(f"   - {f}")
        return
    
    print("✅ 所有必需文件都存在")
    
    # 1. 分析时间差异
    timing_analysis = analyze_timing_differences(
        original_catalog_file, original_phases_file,
        relocated_catalog_file, relocated_phases_file
    )
    
    # 2. 修复时间问题
    output_dir = results_dir / "timing_fixed"
    fixed_catalog_file, fixed_phases_file = fix_relocated_timing(
        original_catalog_file, original_phases_file,
        relocated_catalog_file, relocated_phases_file,
        output_dir
    )
    
    # 3. 验证修复效果
    if fixed_phases_file:
        validation_results = validate_timing_fix(original_phases_file, fixed_phases_file)
        
        # 4. 重新分析修复后的时间差异
        print("\n🔍 分析修复后的时间差异...")
        fixed_timing_analysis = analyze_timing_differences(
            original_catalog_file, original_phases_file,
            fixed_catalog_file, fixed_phases_file
        )
    
    print("\n" + "=" * 60)
    print("🎉 时间问题修复完成！")
    print(f"📁 修复后的文件保存在: {output_dir}")

if __name__ == "__main__":
    main()
