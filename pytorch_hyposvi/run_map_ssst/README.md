# MAP-SSST 地震重定位

基于EikoNet神经网络的MAP (Maximum A Posteriori) 和 SSST (Station-Specific Static Time corrections) 地震重定位实现。

## 功能特点

- **EikoNet神经网络**: 使用训练好的EikoNet模型预测地震走时
- **MAP定位方法**: 基于最大后验概率的地震定位
- **SSST台站校正**: 计算和应用台站特定的静态时间校正
- **迭代优化**: 通过多轮迭代优化定位精度
- **完整输出**: 生成重定位后的地震目录和震相文件

## 文件结构

```
pytorch_hyposvi/run_map_ssst/
├── map_ssst_config.json          # 配置文件
├── map_ssst_locator.py           # 主要定位器实现
├── run_map_ssst_relocation.py    # 主运行脚本
├── test_map_ssst.py              # 测试脚本
└── README.md                     # 说明文档
```

## 算法流程

### 1. 初始化
- 加载EikoNet模型和配置参数
- 读取震相数据、台站信息和原始地震目录
- 设置坐标变换和设备配置

### 2. MAP定位
- 使用原始地震位置作为初始猜测和先验信息
- 通过梯度优化求解最大后验概率位置
- 计算位置不确定性

### 3. 静态迭代
- 进行多轮MAP定位以稳定结果
- 每轮使用前一轮结果作为初始猜测

### 4. SSST校正迭代
- 计算当前定位的走时残差
- 使用k-NN或收缩半径方法计算台站校正
- 应用校正到震相数据
- 使用校正后数据重新定位
- 重复直到收敛

### 5. 结果输出
- 生成重定位后的地震目录
- 重新计算震相到达时间
- 保存所有中间结果和统计信息

## 配置参数

### 数据配置
```json
"data": {
    "coordinate_bounds": {
        "lon_min": -118.20,
        "lat_min": 35.3,
        "z_min": 0.0,
        "z_max": 25.0
    },
    "scale": 200.0
}
```

### MAP定位参数
```json
"inversion": {
    "method": "MAP",
    "n_epochs": 200,
    "learning_rate": 1e-3,
    "iter_tol": 1e-2,
    "optimizer": "adam"
}
```

### SSST参数
```json
"ssst": {
    "n_static_iter": 2,
    "n_ssst_iter": 2,
    "ssst_mode": "knn",
    "k-NN": 30,
    "min_ssst_radius": 0.5,
    "max_ssst_radius": 8.0
}
```

## 使用方法

### 1. 快速测试
```bash
cd /home/<USER>/run_eikonet_hyposvi/pytorch_hyposvi/run_map_ssst
python test_map_ssst.py
```

这将运行基本功能测试和迷你迭代测试，验证代码正确性。

### 2. 完整重定位
```bash
python run_map_ssst_relocation.py
```

这将运行完整的MAP-SSST迭代重定位流程。

### 3. 自定义使用
```python
from map_ssst_locator import MAPSSTLocator

# 创建定位器
locator = MAPSSTLocator("map_ssst_config.json", "eikonet_model.pth")

# 加载数据
locator.load_model_and_data("phase.csv", "station.csv", "catalog.csv")

# 运行迭代定位
stats = locator.run_map_ssst_iteration(event_ids, "output_dir")
```

## 输出文件

### 定位结果
- `initial_map_locations.csv`: 初始MAP定位结果
- `static_iter_*.csv`: 静态迭代结果
- `ssst_iter_*.csv`: SSST迭代结果
- `final_map_ssst_locations.csv`: 最终定位结果

### SSST校正
- `ssst_iter_*_corrections.csv`: 各迭代的SSST校正
- `final_ssst_corrections.csv`: 最终SSST校正

### 残差分析
- `ssst_iter_*_residuals.csv`: 各迭代的残差数据
- `iteration_statistics.csv`: 迭代统计信息

### 重定位数据
- `relocated_phases_map_ssst_full.csv`: 完整重定位震相
- `relocated_phases_map_ssst_simple.csv`: 简化重定位震相
- `relocated_catalog_map_ssst_full.csv`: 完整重定位目录
- `relocated_catalog_map_ssst_simple.csv`: 简化重定位目录

## 技术细节

### EikoNet走时计算
- 输入: 震源坐标、台站坐标、震相类型
- 坐标缩放: 转换为km并按scale参数缩放
- 震相编码: P波=0, S波=1
- 输出: 理论走时(秒)

### MAP优化
- 目标函数: 负对数后验概率
- 优化器: Adam
- 收敛判据: 损失变化小于容差
- 约束: 防止空中地震等物理约束

### SSST校正方法
1. **k-NN方法**: 使用k个最近邻地震的加权平均
2. **收缩半径方法**: 在指定半径范围内的距离加权平均
3. **简单平均**: 该台站-震相组合的残差中位数

### 坐标系统
- 参考点: 配置文件中的lat_min, lon_min
- 坐标变换: LLA ↔ ENU
- 缩放因子: scale参数用于数值稳定性

## 注意事项

1. **内存管理**: 大量事件处理时注意GPU内存使用
2. **收敛性**: 监控迭代过程中的残差变化
3. **参数调优**: 根据数据特点调整SSST参数
4. **质量控制**: 检查定位结果的合理性

## 依赖要求

- PyTorch
- NumPy
- Pandas
- scikit-learn
- 自定义模块: eikonet, hyposvi, data

## 参考

基于Julia版本的HypoSVI实现，适配PyTorch框架并优化了性能和易用性。
