{"data": {"station_file": "../tvf_hyposvi_data/hyposvi_station.csv", "velmod_file": "../tvf_hyposvi_data/hyposvi_vel.csv", "coordinate_bounds": {"lon_min": 97.5, "lon_max": 100.0, "lat_min": 23.0, "lat_max": 26.0, "z_min": 0.0, "z_max": 80.0}, "scale": 200.0}, "training": {"batch_size": 256, "n_train": 50000, "n_test": 10000, "n_epochs": 100, "lr": 0.001, "lr_scheduler": {"type": "StepLR", "step_size": 25, "gamma": 0.5}, "early_stopping": {"patience": 20, "min_delta": 1e-06}, "checkpoint_interval": 10}, "model": {"architecture": {"hidden_dims": [16, 32, 16, 32, 16, 32, 16, 32, 16, 32, 16, 32, 16], "activation": "elu", "use_skip_connections": true}, "initialization": {"method": "xavier_uniform", "output_bias": 1.0}}, "loss": {"type": "eikonal", "l1_weight": 0.95, "l2_weight": 0.05, "epsilon": 1e-08}, "optimizer": {"type": "<PERSON>", "lr": 0.001, "weight_decay": 1e-05, "betas": [0.9, 0.999]}, "device": {"use_cuda": true, "cuda_device": 0}, "logging": {"verbose": true, "log_interval": 10, "plot_interval": 10, "save_plots": true}, "output": {"model_save_path": "tvf_eikonet_model.pth", "results_save_path": "tvf_training_results.json", "plot_dir": "plots"}}