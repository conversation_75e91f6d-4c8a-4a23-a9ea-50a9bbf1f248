#!/usr/bin/env python3
"""
安装TauP依赖包的脚本

这个脚本会尝试安装obspy库，其中包含TauP模块
"""

import subprocess
import sys

def install_obspy():
    """安装obspy库"""
    try:
        print("🔧 检查obspy是否已安装...")
        import obspy
        print("✅ obspy已安装")
        
        # 检查TauP模型
        try:
            from obspy.taup import TauPyModel
            model = TauPyModel(model='iasp91')
            print("✅ TauP模型可用")
            return True
        except Exception as e:
            print(f"⚠️ TauP模型加载失败: {e}")
            return False
            
    except ImportError:
        print("📦 obspy未安装，开始安装...")
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'obspy'])
            print("✅ obspy安装成功")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ obspy安装失败: {e}")
            return False

def main():
    """主函数"""
    print("🚀 TauP依赖安装脚本")
    print("=" * 50)
    
    success = install_obspy()
    
    if success:
        print("\n✅ 所有依赖安装完成！")
        print("现在可以运行带TauP验证的MAP定位程序了。")
    else:
        print("\n❌ 依赖安装失败")
        print("请手动安装obspy: pip install obspy")
    
    # 简单测试
    try:
        from obspy.taup import TauPyModel
        model = TauPyModel(model='iasp91')
        arrivals = model.get_travel_times(source_depth_in_km=10, distance_in_degree=1, phase_list=['P'])
        if arrivals:
            print(f"🧪 测试成功: P波走时 = {arrivals[0].time:.3f} 秒")
        else:
            print("⚠️ 测试失败: 未获得走时结果")
    except Exception as e:
        print(f"🧪 测试失败: {e}")

if __name__ == "__main__":
    main()
