"""
Stein Variational Inference (SVI) solver for earthquake location.

This module implements the SVI method for earthquake hypocenter location,
using particle-based variational inference with kernel methods.
"""

import torch
import torch.optim as optim
import numpy as np
from typing import Dict, Any, Optional, Tuple, Union
import warnings

from .utils import get_origin_time, assign_likelihood
from .kernels import RBFKernel, IMQKernel, median_bandwidth_heuristic
from .distributions import PGeneralizedGaussian


class SVISolver:
    """
    Stein Variational Inference solver for earthquake location.
    
    This class implements the SVI method using particle-based variational
    inference to estimate the posterior distribution of earthquake hypocenters.
    
    Args:
        eikonet_model: Trained EikoNet model for travel time prediction
        config: Configuration dictionary
        device: Computation device
    """
    
    def __init__(self, eikonet_model, config: Dict[str, Any], device: torch.device):
        self.eikonet_model = eikonet_model
        self.device = device
        self.config = config
        
        # SVI parameters
        self.n_particles = config.get('n_particles', 101)
        self.n_epochs = config.get('n_epochs', 2000)
        self.learning_rate = config.get('learning_rate', 1e-5)
        self.iter_tol = config.get('svi_iter_tol', 1e-3)
        self.lr_decay_interval = config.get('lr_decay_interval', 100)
        self.verbose = config.get('svi_verbose', 1)
        
        # Kernel parameters
        self.kernel_fn = config.get('kernel_fn', 'RBF').upper()
        
        # Prior parameters
        self.prior_z_mean = config.get('prior_z_mean', 5.5)
        self.prior_z_std = config.get('prior_z_std', 3.0)
        self.prior_scale_param = config.get('prior_scale_param', 2.0)
        
        # Data parameters
        self.scale = config.get('scale', 200.0)
        self.coordinate_bounds = config.get('coordinate_bounds', {})
        
        # Uncertainty parameters
        self.pick_unc_p = config.get('pick_unc_p', 0.10)
        self.pick_unc_s = config.get('pick_unc_s', 0.10)
        self.likelihood_fn = config.get('likelihood_fn', 'gaussian')
        
        # Initialize likelihood function
        self.log_likelihood_fn = assign_likelihood(config)
        
        # Initialize prior distribution
        self.prior = PGeneralizedGaussian(
            loc=0.0, 
            scale=1.0, 
            shape=self.prior_scale_param
        )
        
        # Prior parameters (scaled)
        self.prior_mu = torch.tensor([0.5, 0.5, self.prior_z_mean / self.scale], 
                                   device=device, dtype=torch.float32)
        self.prior_sigma = torch.tensor([0.5, 0.5, self.prior_z_std / self.scale], 
                                      device=device, dtype=torch.float32)
        
    def locate_event(
        self,
        X_obs: torch.Tensor,
        T_obs: torch.Tensor,
        initial_guess: Optional[torch.Tensor] = None,
        prior_info: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Locate earthquake using SVI method.

        Args:
            X_obs: Observation coordinates [4, n_obs] (east, north, depth, phase_type)
            T_obs: Observed arrival times [n_obs]
            initial_guess: Initial location guess [3] (east, north, depth)
            prior_info: Prior information from original catalog

        Returns:
            Dictionary with location results including particle ensemble
        """
        X_obs = X_obs.to(self.device)
        T_obs = T_obs.to(self.device)
        n_obs = X_obs.size(1)
        
        # Set adaptive priors if provided
        if prior_info is not None:
            # Update prior parameters based on original catalog
            self.prior_mu = torch.tensor([
                prior_info['east_scaled'],
                prior_info['north_scaled'],
                prior_info['depth_scaled']
            ], device=self.device, dtype=torch.float32)
            # Use tighter priors around original location
            self.prior_sigma = torch.tensor([
                5.0 / self.scale / 1000.0,  # ~5km std for horizontal
                5.0 / self.scale / 1000.0,  # ~5km std for horizontal
                2.0 / self.scale            # ~2km std for depth
            ], device=self.device, dtype=torch.float32)
            print(f"      🎯 SVI设置自适应先验: 深度 {prior_info['depth_km']:.2f} km")

        # Get MAP solution as initialization
        map_solution = self._get_map_initialization(X_obs, T_obs, initial_guess, prior_info)

        # Initialize particle ensemble
        particles = self._initialize_particles(map_solution, X_obs)
        
        # Setup optimizer
        particles.requires_grad_(True)
        optimizer = optim.Adam([particles], lr=self.learning_rate)
        
        # Track optimization
        losses = []
        convergence_history = []
        
        # SVI optimization loop
        for epoch in range(self.n_epochs):
            optimizer.zero_grad()
            
            # Compute Stein variational gradient
            stein_grad = self._compute_stein_gradient(particles, X_obs, T_obs)
            
            # Manual gradient update (negative because we want to maximize)
            particles.grad = -stein_grad
            
            # Optimizer step
            optimizer.step()
            
            # Learning rate decay
            if (epoch + 1) % self.lr_decay_interval == 0:
                for param_group in optimizer.param_groups:
                    param_group['lr'] /= 1.1
                    
            # Check convergence
            if epoch > 0:
                param_change = torch.norm(stein_grad).item()
                convergence_history.append(param_change)
                
                if param_change < self.iter_tol / self.scale:
                    if self.verbose >= 1:
                        print(f"SVI converged at epoch {epoch + 1}, change: {param_change * 1000 * self.scale:.6f}")
                    break
                    
                if self.verbose == 2:
                    print(f"SVI Epoch {epoch + 1}, change: {param_change * 1000 * self.scale:.6f}")
                    
        if epoch == self.n_epochs - 1 and self.verbose >= 1:
            print(f"SVI failed to converge, final change: {param_change * 1000 * self.scale:.6f}")
            
        # Compute final statistics
        results = self._compute_final_results(particles, X_obs, T_obs)
        results.update({
            'n_iterations': epoch + 1,
            'convergence_history': convergence_history,
            'n_observations': n_obs,
            'n_particles': self.n_particles,
            'particles': particles.detach().cpu().numpy()
        })
        
        return results
        
    def _get_map_initialization(
        self,
        X_obs: torch.Tensor,
        T_obs: torch.Tensor,
        initial_guess: Optional[torch.Tensor],
        prior_info: Optional[Dict[str, Any]] = None
    ) -> torch.Tensor:
        """Get MAP solution for particle initialization."""
        # Import here to avoid circular imports
        from .map_solver import MAPSolver

        # Create MAP solver with same configuration
        map_config = self.config.copy()
        map_solver = MAPSolver(self.eikonet_model, map_config, self.device)

        # Get MAP solution with prior info
        map_result = map_solver.locate_event(X_obs, T_obs, initial_guess, prior_info)
        map_location = torch.tensor(map_result['hypocenter'],
                                  device=self.device, dtype=torch.float32)

        return map_location
        
    def _initialize_particles(self, map_solution: torch.Tensor, X_obs: torch.Tensor) -> torch.Tensor:
        """Initialize particle ensemble around MAP solution."""
        # Create particle ensemble
        particles = map_solution.unsqueeze(1).repeat(1, self.n_particles)
        
        # Add random perturbations
        noise = torch.randn(3, self.n_particles, device=self.device, dtype=torch.float32)
        particles += noise * 5.0 / self.scale
        
        return particles
        
    def _compute_stein_gradient(
        self, 
        particles: torch.Tensor, 
        X_obs: torch.Tensor, 
        T_obs: torch.Tensor
    ) -> torch.Tensor:
        """
        Compute Stein variational gradient.
        
        Args:
            particles: Current particle positions [3, n_particles]
            X_obs: Observation coordinates [4, n_obs]
            T_obs: Observed arrival times [n_obs]
            
        Returns:
            Stein gradient [3, n_particles]
        """
        n_particles = particles.size(1)
        
        # Compute log posterior gradients for each particle
        log_post_grads = []

        for i in range(n_particles):
            particle = particles[:, i:i+1]  # [3, 1]

            # Enable gradient computation
            particle_var = particle.clone().detach().requires_grad_(True)

            # Compute log posterior
            log_post = self._compute_log_posterior(particle_var, X_obs, T_obs)

            # Compute gradient
            if log_post.requires_grad:
                grad = torch.autograd.grad(log_post, particle_var, retain_graph=False)[0]
            else:
                # If log_post doesn't require grad, create zero gradient
                grad = torch.zeros_like(particle_var)
            log_post_grads.append(grad)
            
        # Stack gradients
        log_post_grads = torch.cat(log_post_grads, dim=1)  # [3, n_particles]
        
        # Compute kernel matrix and its gradient
        if self.kernel_fn == 'RBF':
            kernel = RBFKernel()
            K = kernel(particles)  # [n_particles, n_particles]
            K_grad = kernel.gradient(particles)  # [3, n_particles]
        else:
            raise NotImplementedError(f"Kernel {self.kernel_fn} not implemented")
            
        # Compute Stein gradient: (K * ∇log p - ∇K) / n_particles
        stein_grad = (K @ log_post_grads.T).T - K_grad
        stein_grad = stein_grad / n_particles
        
        return stein_grad
        
    def _compute_log_posterior(
        self, 
        particle: torch.Tensor, 
        X_obs: torch.Tensor, 
        T_obs: torch.Tensor
    ) -> torch.Tensor:
        """
        Compute log posterior for a single particle.
        
        Args:
            particle: Particle position [3, 1]
            X_obs: Observation coordinates [4, n_obs]
            T_obs: Observed arrival times [n_obs]
            
        Returns:
            Log posterior value (scalar)
        """
        # Construct input for EikoNet
        X_input = self._construct_input(particle, X_obs)
        
        # Get predicted travel times and compute residuals
        origin_time, residuals = get_origin_time(X_input, self.eikonet_model, T_obs)
        
        # Compute log likelihood
        phase_types = X_obs[3]  # P=0, S=1
        log_likelihood = self._compute_log_likelihood(residuals, phase_types)
        
        # Compute log prior
        log_prior = self._compute_log_prior(particle)
        
        return log_likelihood + log_prior
        
    def _construct_input(self, X_src: torch.Tensor, X_obs: torch.Tensor) -> torch.Tensor:
        """
        Construct input tensor for EikoNet.

        Args:
            X_src: Source coordinates [3, 1] or [3]
            X_obs: Observation coordinates [4, n_obs] (east, north, depth, phase_type)

        Returns:
            Input tensor [7, n_obs] (src_east, src_north, src_depth, obs_east, obs_north, obs_depth, phase_type)
        """
        n_obs = X_obs.size(1)
        X_input = torch.zeros(7, n_obs, device=self.device)

        # Source coordinates (repeated for each observation)
        if X_src.dim() == 1:
            X_src = X_src.unsqueeze(1)
        X_input[:3] = X_src.expand(-1, n_obs)

        # Receiver coordinates and phases
        X_input[3:] = X_obs

        return X_input
        
    def _compute_log_likelihood(self, residuals: torch.Tensor, phase_types: torch.Tensor) -> torch.Tensor:
        """Compute log likelihood from residuals."""
        # Apply phase-specific uncertainties
        uncertainties = torch.where(phase_types == 0, self.pick_unc_p, self.pick_unc_s)
        normalized_residuals = residuals / uncertainties
        
        # Compute log likelihood using specified function
        if self.likelihood_fn == 'gaussian':
            log_likelihood = -0.5 * torch.sum(normalized_residuals**2)
        else:
            # Use the assigned likelihood function
            log_likelihood = torch.sum(self.log_likelihood_fn(normalized_residuals))
            
        return log_likelihood
        
    def _compute_log_prior(self, particle: torch.Tensor) -> torch.Tensor:
        """Compute log prior for particle."""
        # Standardize particle coordinates
        standardized = (particle.squeeze() - self.prior_mu) / self.prior_sigma

        # Compute log prior using P-Generalized Gaussian
        log_prior = torch.tensor(0.0, device=self.device, dtype=torch.float32, requires_grad=True)

        for i in range(3):
            # Use a simple Gaussian prior for now to avoid gradient issues
            log_prior = log_prior - 0.5 * standardized[i]**2

        return log_prior
        
    def _compute_final_results(
        self, 
        particles: torch.Tensor, 
        X_obs: torch.Tensor, 
        T_obs: torch.Tensor
    ) -> Dict[str, Any]:
        """Compute final statistics from particle ensemble."""
        with torch.no_grad():
            # Compute ensemble statistics
            mean_location = torch.mean(particles, dim=1)
            std_location = torch.std(particles, dim=1)
            
            # Compute final residuals using mean location
            X_input_final = self._construct_input(mean_location.unsqueeze(1), X_obs)
            T_pred_final = self.eikonet_model(X_input_final).squeeze(0)
            origin_time_final, residuals_final = get_origin_time(
                X_input_final, self.eikonet_model, T_obs
            )
            
            # Compute uncertainties from particle spread
            uncertainties = std_location.cpu().numpy()
            
            results = {
                'hypocenter': mean_location.cpu().numpy(),
                'hypocenter_std': std_location.cpu().numpy(),
                'origin_time_offset': origin_time_final,
                'residuals': residuals_final.cpu().numpy(),
                'predicted_times': T_pred_final.cpu().numpy(),
                'uncertainties': uncertainties,
                'method': 'SVI'
            }
            
        return results
