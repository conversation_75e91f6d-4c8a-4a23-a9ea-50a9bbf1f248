"""
Data loading utilities for earthquake and station data.

This module provides classes for loading and managing seismic data,
including earthquake phase picks and station information.
"""

import pandas as pd
import numpy as np
import torch
from datetime import datetime
from typing import Union, Tuple, Optional, Dict, List
from pathlib import Path


class PhaseDataLoader:
    """
    Loader for earthquake phase pick data.
    
    Handles loading and preprocessing of phase arrival times,
    matching the format expected by HypoSVI.
    
    Expected CSV format:
    time,evid,arid,phase,network,station
    2000-12-13T15:01:11.330000,1,1,P,X,ST0
    """
    
    def __init__(self, filepath: Union[str, Path]):
        self.filepath = Path(filepath)
        self.data = None
        self.load_data()
        
    def load_data(self) -> pd.DataFrame:
        """Load phase data from CSV file."""
        try:
            self.data = pd.read_csv(self.filepath)
            
            # Validate required columns
            required_cols = ['time', 'evid', 'phase', 'network', 'station']
            missing_cols = [col for col in required_cols if col not in self.data.columns]
            if missing_cols:
                raise ValueError(f"Missing required columns: {missing_cols}")
                
            # Convert time column to datetime
            self.data['time'] = pd.to_datetime(self.data['time'])
            
            # Ensure phase column contains only P and S
            valid_phases = self.data['phase'].isin(['P', 'S'])
            if not valid_phases.all():
                invalid_count = (~valid_phases).sum()
                print(f"Warning: Removing {invalid_count} rows with invalid phases")
                self.data = self.data[valid_phases]
                
            print(f"Loaded {len(self.data)} phase picks for {self.data['evid'].nunique()} events")
            
        except Exception as e:
            raise RuntimeError(f"Failed to load phase data from {self.filepath}: {e}")
            
        return self.data
        
    def get_event_phases(self, evid: int) -> pd.DataFrame:
        """
        Get all phase picks for a specific event.
        
        Args:
            evid: Event ID
            
        Returns:
            DataFrame with phase picks for the event
        """
        if self.data is None:
            raise RuntimeError("Data not loaded")
            
        event_data = self.data[self.data['evid'] == evid].copy()
        
        if len(event_data) == 0:
            raise ValueError(f"No data found for event {evid}")
            
        return event_data
        
    def get_event_ids(self) -> List[int]:
        """Get list of all event IDs."""
        if self.data is None:
            raise RuntimeError("Data not loaded")
            
        return sorted(self.data['evid'].unique())
        
    def filter_by_event_count(self, min_phases: int = 4) -> 'PhaseDataLoader':
        """
        Filter events by minimum number of phase picks.
        
        Args:
            min_phases: Minimum number of phases required per event
            
        Returns:
            New PhaseDataLoader with filtered data
        """
        if self.data is None:
            raise RuntimeError("Data not loaded")
            
        # Count phases per event
        event_counts = self.data.groupby('evid').size()
        valid_events = event_counts[event_counts >= min_phases].index
        
        # Filter data
        filtered_data = self.data[self.data['evid'].isin(valid_events)].copy()
        
        # Create new loader instance
        new_loader = PhaseDataLoader.__new__(PhaseDataLoader)
        new_loader.filepath = self.filepath
        new_loader.data = filtered_data
        
        print(f"Filtered to {len(valid_events)} events with >= {min_phases} phases")
        
        return new_loader


class StationDataLoader:
    """
    Loader for seismic station information.
    
    Handles loading and preprocessing of station coordinates and metadata.
    
    Expected CSV format:
    network,station,latitude,longitude,elevation
    X,ST0,35.5249,-117.3646,0.67
    """
    
    def __init__(self, filepath: Union[str, Path]):
        self.filepath = Path(filepath)
        self.data = None
        self.load_data()
        
    def load_data(self) -> pd.DataFrame:
        """Load station data from CSV file."""
        try:
            self.data = pd.read_csv(self.filepath)
            
            # Validate required columns
            required_cols = ['network', 'station', 'latitude', 'longitude']
            missing_cols = [col for col in required_cols if col not in self.data.columns]
            if missing_cols:
                raise ValueError(f"Missing required columns: {missing_cols}")
                
            # Add elevation column if missing
            if 'elevation' not in self.data.columns:
                self.data['elevation'] = 0.0
                print("Warning: No elevation data found, setting to 0.0")
                
            # Remove duplicate stations
            initial_count = len(self.data)
            self.data = self.data.drop_duplicates(subset=['network', 'station'])
            final_count = len(self.data)
            
            if initial_count != final_count:
                print(f"Removed {initial_count - final_count} duplicate stations")
                
            print(f"Loaded {len(self.data)} stations")
            
        except Exception as e:
            raise RuntimeError(f"Failed to load station data from {self.filepath}: {e}")
            
        return self.data
        
    def get_station_info(self, network: str, station: str) -> pd.Series:
        """
        Get information for a specific station.
        
        Args:
            network: Network code
            station: Station code
            
        Returns:
            Series with station information
        """
        if self.data is None:
            raise RuntimeError("Data not loaded")
            
        mask = (self.data['network'] == network) & (self.data['station'] == station)
        station_data = self.data[mask]
        
        if len(station_data) == 0:
            raise ValueError(f"Station {network}.{station} not found")
        elif len(station_data) > 1:
            print(f"Warning: Multiple entries for {network}.{station}, using first")
            
        return station_data.iloc[0]
        
    def get_station_coordinates(self) -> np.ndarray:
        """
        Get station coordinates as numpy array.
        
        Returns:
            Array of shape (n_stations, 3) with [lat, lon, elevation]
        """
        if self.data is None:
            raise RuntimeError("Data not loaded")
            
        coords = self.data[['latitude', 'longitude', 'elevation']].values
        return coords


class DataLoader:
    """
    Combined data loader for phases and stations.
    
    Provides unified interface for loading and preprocessing
    earthquake and station data for HypoSVI.
    """
    
    def __init__(
        self,
        phase_file: Union[str, Path],
        station_file: Union[str, Path]
    ):
        self.phase_loader = PhaseDataLoader(phase_file)
        self.station_loader = StationDataLoader(station_file)
        
    def format_arrivals(
        self,
        evid: int,
        coordinate_transform: Optional[object] = None
    ) -> Tuple[torch.Tensor, torch.Tensor, datetime, pd.DataFrame]:
        """
        Format arrival data for a specific event.
        
        This function matches the Julia format_arrivals function,
        preparing data for earthquake location.
        
        Args:
            evid: Event ID
            coordinate_transform: Coordinate transformation object
            
        Returns:
            Tuple of (X_obs, T_obs, T_ref, phase_station_data)
            - X_obs: Observation coordinates (4, n_obs) [X, Y, Z, phase]
            - T_obs: Observation times relative to reference
            - T_ref: Reference time (earliest arrival)
            - phase_station_data: Combined phase and station data
        """
        # Get phase data for event
        phases = self.phase_loader.get_event_phases(evid)
        
        # Join with station data
        phase_station = phases.merge(
            self.station_loader.data,
            on=['network', 'station'],
            how='inner'
        )
        
        if len(phase_station) == 0:
            raise ValueError(f"No matching station data for event {evid}")
            
        n_obs = len(phase_station)
        
        # Initialize observation arrays
        X_obs = torch.zeros(4, n_obs, dtype=torch.float32)
        
        # Set coordinates (will be transformed later if transform provided)
        if coordinate_transform is not None:
            # Transform coordinates using provided transform
            for i, row in phase_station.iterrows():
                x, y, z = coordinate_transform.transform(
                    row['latitude'], row['longitude'], row['elevation']
                )
                X_obs[0, i] = x
                X_obs[1, i] = y
                X_obs[2, i] = z
        else:
            # Use raw coordinates
            X_obs[0] = torch.tensor(phase_station['longitude'].values, dtype=torch.float32)
            X_obs[1] = torch.tensor(phase_station['latitude'].values, dtype=torch.float32)
            X_obs[2] = torch.tensor(phase_station['elevation'].values, dtype=torch.float32)
            
        # Set phase indicators (0 for P, 1 for S)
        phase_indicators = (phase_station['phase'] == 'S').astype(float)
        X_obs[3] = torch.tensor(phase_indicators.values, dtype=torch.float32)
        
        # Calculate relative arrival times
        arrival_times = phase_station['time'].values
        reference_time = arrival_times.min()
        
        # Convert to seconds relative to reference time
        T_obs = torch.zeros(n_obs, dtype=torch.float32)
        for i, arrival_time in enumerate(arrival_times):
            dt = (arrival_time - reference_time).total_seconds()
            T_obs[i] = dt
            
        return X_obs, T_obs, reference_time, phase_station
        
    def get_event_summary(self) -> pd.DataFrame:
        """
        Get summary statistics for all events.
        
        Returns:
            DataFrame with event statistics
        """
        if self.phase_loader.data is None:
            raise RuntimeError("Phase data not loaded")
            
        summary = self.phase_loader.data.groupby('evid').agg({
            'time': ['min', 'max', 'count'],
            'phase': lambda x: (x == 'P').sum(),
            'station': 'nunique'
        }).round(3)
        
        # Flatten column names
        summary.columns = ['earliest_time', 'latest_time', 'total_phases', 'p_phases', 'n_stations']
        summary['s_phases'] = summary['total_phases'] - summary['p_phases']
        
        return summary
