#!/usr/bin/env python3
"""
Plot travel time curves for P and S waves
横轴为震中距，纵轴为P波和S波到时的走时曲线图
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from geopy.distance import geodesic
import os

def calculate_epicentral_distance(event_lat, event_lon, station_lat, station_lon):
    """
    Calculate epicentral distance between event and station using geodesic distance
    """
    event_coords = (event_lat, event_lon)
    station_coords = (station_lat, station_lon)
    distance_km = geodesic(event_coords, station_coords).kilometers
    return distance_km

def load_station_coordinates():
    """
    Load station coordinates from the station.csv file
    """
    station_file = '/home/<USER>/run_eikonet_hyposvi/hyposvi/station.csv'

    if not os.path.exists(station_file):
        print(f"Error: Station file {station_file} not found!")
        return {}

    stations = {}
    station_df = pd.read_csv(station_file)

    for idx, row in station_df.iterrows():
        station_name = row['station']
        stations[station_name] = {
            'latitude': row['latitude'],
            'longitude': row['longitude']
        }

    print(f"Loaded {len(stations)} stations from {station_file}")
    return stations

def main():
    # File paths
    catalog_file = 'relocated_catalog.csv'
    phases_file = 'relocated_phases.csv'
    
    # Check if files exist
    if not os.path.exists(catalog_file):
        print(f"Error: {catalog_file} not found!")
        return
    if not os.path.exists(phases_file):
        print(f"Error: {phases_file} not found!")
        return
    
    # Load data
    print("Loading catalog data...")
    catalog_df = pd.read_csv(catalog_file)
    
    print("Loading phases data...")
    phases_df = pd.read_csv(phases_file)
    
    # Load station coordinates
    stations = load_station_coordinates()
    
    # Prepare data for plotting
    p_distances = []
    p_travel_times = []
    s_distances = []
    s_travel_times = []
    
    print("Processing phase data...")
    
    # Process each phase record
    for idx, phase_row in phases_df.iterrows():
        evid = phase_row['evid']
        station = phase_row['station']
        phase_type = phase_row['phase']
        travel_time = phase_row['theoretical_travel_time']
        
        # Get event information
        event_info = catalog_df[catalog_df['evid'] == evid]
        if event_info.empty:
            continue
            
        event_lat = event_info.iloc[0]['latitude']
        event_lon = event_info.iloc[0]['longitude']
        
        # Get station coordinates
        if station not in stations:
            continue
            
        station_lat = stations[station]['latitude']
        station_lon = stations[station]['longitude']
        
        # Calculate epicentral distance
        distance = calculate_epicentral_distance(event_lat, event_lon, station_lat, station_lon)
        
        # Store data based on phase type
        if phase_type == 'P':
            p_distances.append(distance)
            p_travel_times.append(travel_time)
        elif phase_type == 'S':
            s_distances.append(distance)
            s_travel_times.append(travel_time)
    
    print(f"Found {len(p_distances)} P-wave arrivals and {len(s_distances)} S-wave arrivals")
    
    # Create the plot
    plt.figure(figsize=(12, 8))
    
    # Plot P waves
    if p_distances:
        plt.scatter(p_distances, p_travel_times, c='blue', alpha=0.6, s=20, label='P waves')
    
    # Plot S waves
    if s_distances:
        plt.scatter(s_distances, s_travel_times, c='red', alpha=0.6, s=20, label='S waves')
    
    # Set labels and title
    plt.xlabel('Epicentral Distance (km)', fontsize=12)
    plt.ylabel('Travel Time (s)', fontsize=12)
    plt.title('Travel Time Curves for P and S Waves', fontsize=14, fontweight='bold')
    plt.legend(fontsize=11)
    plt.grid(True, alpha=0.3)
    
    # Set axis limits for better visualization
    if p_distances or s_distances:
        all_distances = p_distances + s_distances
        all_times = p_travel_times + s_travel_times
        
        plt.xlim(0, max(all_distances) * 1.05)
        plt.ylim(0, max(all_times) * 1.05)
    
    # Adjust layout
    plt.tight_layout()
    
    # Save the plot
    output_file = 'travel_time_curves.png'
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    print(f"Plot saved as {output_file}")
    
    # Don't display the plot
    plt.close()
    
    # Print some statistics
    if p_distances:
        print(f"\nP-wave statistics:")
        print(f"  Distance range: {min(p_distances):.1f} - {max(p_distances):.1f} km")
        print(f"  Travel time range: {min(p_travel_times):.1f} - {max(p_travel_times):.1f} s")
    
    if s_distances:
        print(f"\nS-wave statistics:")
        print(f"  Distance range: {min(s_distances):.1f} - {max(s_distances):.1f} km")
        print(f"  Travel time range: {min(s_travel_times):.1f} - {max(s_travel_times):.1f} s")

if __name__ == "__main__":
    main()
