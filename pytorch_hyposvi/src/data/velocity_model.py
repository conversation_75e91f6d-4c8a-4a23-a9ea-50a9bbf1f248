"""
Velocity model handling for seismic travel time calculations.

This module implements 1D velocity models with interpolation capabilities,
matching the functionality of the Julia VelMod1D implementation.
"""

import pandas as pd
import numpy as np
import torch
from scipy.interpolate import interp1d
from typing import Union, Tuple, Optional
from pathlib import Path


class VelocityModel1D:
    """
    1D velocity model with P and S wave velocities.
    
    Provides interpolation of velocity values at arbitrary depths,
    matching the Julia VelMod1D implementation.
    
    Args:
        depth: Array of depth values (km)
        vp: Array of P-wave velocities (km/s)
        vs: Array of S-wave velocities (km/s)
        extrapolate: Whether to extrapolate beyond data bounds
    """
    
    def __init__(
        self,
        depth: np.ndarray,
        vp: np.ndarray, 
        vs: np.ndarray,
        extrapolate: bool = True
    ):
        self.depth = np.asarray(depth)
        self.vp = np.asarray(vp)
        self.vs = np.asarray(vs)
        
        # Create interpolation functions
        fill_value = "extrapolate" if extrapolate else (np.nan, np.nan)
        bounds_error = not extrapolate
        
        self.vp_interp = interp1d(
            self.depth, self.vp, 
            kind='linear',
            fill_value=fill_value,
            bounds_error=bounds_error
        )
        
        self.vs_interp = interp1d(
            self.depth, self.vs,
            kind='linear', 
            fill_value=fill_value,
            bounds_error=bounds_error
        )
        
    @classmethod
    def from_csv(cls, filepath: Union[str, Path]) -> 'VelocityModel1D':
        """
        Load velocity model from CSV file.
        
        Expected CSV format:
        depth,vp,vs
        0.0,5.0,2.9
        1.0,5.1,3.0
        ...
        
        Args:
            filepath: Path to CSV file
            
        Returns:
            VelocityModel1D instance
        """
        df = pd.read_csv(filepath)
        
        required_cols = ['depth', 'vp', 'vs']
        if not all(col in df.columns for col in required_cols):
            raise ValueError(f"CSV must contain columns: {required_cols}")
            
        return cls(
            depth=df['depth'].values,
            vp=df['vp'].values,
            vs=df['vs'].values
        )
        
    def get_velocity(
        self, 
        depth: Union[float, np.ndarray, torch.Tensor],
        phase: Union[str, int, np.ndarray, torch.Tensor] = 'P'
    ) -> Union[float, np.ndarray, torch.Tensor]:
        """
        Get velocity at specified depth(s) for given phase(s).
        
        Args:
            depth: Depth value(s) in km
            phase: Phase type ('P', 'S', 0, 1) or array of phases
                  P-wave: 'P' or 0
                  S-wave: 'S' or 1
                  
        Returns:
            Velocity value(s) in km/s
        """
        # Convert torch tensors to numpy for interpolation
        is_torch = isinstance(depth, torch.Tensor)
        if is_torch:
            depth_np = depth.detach().cpu().numpy()
        else:
            depth_np = np.asarray(depth)
            
        # Handle phase specification
        if isinstance(phase, str):
            if phase.upper() == 'P':
                velocities = self.vp_interp(depth_np)
            elif phase.upper() == 'S':
                velocities = self.vs_interp(depth_np)
            else:
                raise ValueError(f"Unknown phase: {phase}")
                
        else:
            # Handle array of phases
            phase_array = np.asarray(phase)
            velocities = np.zeros_like(depth_np)
            
            # P-wave indices (phase == 0 or phase <= 0.5)
            p_mask = phase_array <= 0.5
            velocities[p_mask] = self.vp_interp(depth_np[p_mask])
            
            # S-wave indices (phase == 1 or phase > 0.5)
            s_mask = phase_array > 0.5
            velocities[s_mask] = self.vs_interp(depth_np[s_mask])
            
        # Convert back to torch tensor if input was torch
        if is_torch:
            return torch.from_numpy(velocities).to(depth.device).type(depth.dtype)
        else:
            return velocities
            
    def get_slowness(
        self,
        depth: Union[float, np.ndarray, torch.Tensor],
        phase: Union[str, int, np.ndarray, torch.Tensor] = 'P'
    ) -> Union[float, np.ndarray, torch.Tensor]:
        """
        Get slowness (1/velocity) at specified depth(s).
        
        Args:
            depth: Depth value(s) in km
            phase: Phase type
            
        Returns:
            Slowness value(s) in s/km
        """
        velocity = self.get_velocity(depth, phase)
        return 1.0 / velocity
        
    def plot_model(self, save_path: Optional[str] = None, max_depth: float = 50.0):
        """
        Plot the velocity model.
        
        Args:
            save_path: Path to save the plot (optional)
            max_depth: Maximum depth to plot
        """
        try:
            import matplotlib.pyplot as plt
        except ImportError:
            print("Matplotlib not available for plotting")
            return
            
        # Create depth array for smooth plotting
        plot_depths = np.linspace(self.depth.min(), min(self.depth.max(), max_depth), 1000)
        plot_vp = self.get_velocity(plot_depths, 'P')
        plot_vs = self.get_velocity(plot_depths, 'S')
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 8))
        
        # P-wave velocity
        ax1.plot(plot_vp, plot_depths, 'b-', linewidth=2, label='P-wave')
        ax1.scatter(self.vp, self.depth, c='blue', s=50, alpha=0.7)
        ax1.set_xlabel('P-wave Velocity (km/s)')
        ax1.set_ylabel('Depth (km)')
        ax1.invert_yaxis()
        ax1.grid(True, alpha=0.3)
        ax1.set_title('P-wave Velocity Model')
        
        # S-wave velocity  
        ax2.plot(plot_vs, plot_depths, 'r-', linewidth=2, label='S-wave')
        ax2.scatter(self.vs, self.depth, c='red', s=50, alpha=0.7)
        ax2.set_xlabel('S-wave Velocity (km/s)')
        ax2.set_ylabel('Depth (km)')
        ax2.invert_yaxis()
        ax2.grid(True, alpha=0.3)
        ax2.set_title('S-wave Velocity Model')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Velocity model plot saved to {save_path}")
        else:
            plt.show()
            
        plt.close()
        
    def __repr__(self) -> str:
        return (f"VelocityModel1D(depth_range=[{self.depth.min():.1f}, {self.depth.max():.1f}] km, "
                f"vp_range=[{self.vp.min():.1f}, {self.vp.max():.1f}] km/s, "
                f"vs_range=[{self.vs.min():.1f}, {self.vs.max():.1f}] km/s)")


def build_1d_training_data(
    velocity_model: VelocityModel1D,
    n_train: int,
    n_test: int,
    coordinate_bounds: dict,
    scale: float = 200.0,
    device: torch.device = torch.device('cpu')
) -> Tuple[torch.utils.data.DataLoader, torch.utils.data.DataLoader]:
    """
    Build training and test data for EikoNet from velocity model.
    
    This function generates synthetic training data by sampling random
    source-receiver pairs and computing true slowness values from the
    velocity model.
    
    Args:
        velocity_model: 1D velocity model
        n_train: Number of training samples
        n_test: Number of test samples  
        coordinate_bounds: Dictionary with coordinate bounds
        scale: Coordinate scaling factor
        device: PyTorch device
        
    Returns:
        Tuple of (train_loader, test_loader)
    """
    n_total = n_train + n_test
    
    # Extract coordinate bounds
    z_min = coordinate_bounds['z_min'] / scale
    z_max = coordinate_bounds['z_max'] / scale
    
    # Generate random source coordinates (normalized to [0,1])
    x_src = torch.rand(3, n_total, device=device)
    x_src[2] = torch.rand(n_total, device=device) * (z_max - z_min) + z_min
    
    # Generate random receiver coordinates
    x_rec = torch.zeros(3, n_total, device=device)
    
    # Use rejection sampling to ensure receivers are within bounds
    max_dist = np.sqrt(3)  # Maximum distance in unit cube
    
    for i in range(n_total):
        valid = False
        attempts = 0
        max_attempts = 1000
        
        while not valid and attempts < max_attempts:
            # Random direction vector
            direction = torch.randn(3, device=device)
            direction = direction / torch.norm(direction)
            
            # Random distance
            distance = torch.rand(1, device=device) * max_dist
            distance = torch.clamp(distance, 1e-5, max_dist)
            
            # Receiver position
            rec_pos = x_src[:, i] + distance * direction
            
            # Check bounds
            if (rec_pos[0] >= 0 and rec_pos[0] <= 1 and
                rec_pos[1] >= 0 and rec_pos[1] <= 1 and
                rec_pos[2] >= z_min and rec_pos[2] <= z_max):
                x_rec[:, i] = rec_pos
                valid = True
                
            attempts += 1
            
        if not valid:
            # Fallback: clamp to bounds
            x_rec[:, i] = torch.clamp(x_src[:, i], 0, 1)
            x_rec[2, i] = torch.clamp(x_rec[2, i], z_min, z_max)
    
    # Generate random phase labels (0 for P, 1 for S)
    phases = torch.randint(0, 2, (1, n_total), device=device, dtype=torch.float32)
    
    # Combine coordinates and phases
    x_data = torch.cat([x_src, x_rec, phases], dim=0)  # Shape: (7, n_total)
    
    # Calculate true slowness values
    depths_scaled = x_rec[2] * scale  # Convert back to km
    slowness_values = torch.zeros(1, n_total, device=device)
    
    for i in range(n_total):
        depth = depths_scaled[i].item()
        phase = phases[0, i].item()
        
        # Get slowness from velocity model
        velocity = velocity_model.get_velocity(depth, int(phase))
        slowness_values[0, i] = 1.0 / velocity
        
    # Split into train and test
    x_train = x_data[:, :n_train]
    x_test = x_data[:, n_train:]
    s_train = slowness_values[:, :n_train]
    s_test = slowness_values[:, n_train:]
    
    # Create data loaders
    train_dataset = torch.utils.data.TensorDataset(x_train.T, s_train.T)
    test_dataset = torch.utils.data.TensorDataset(x_test.T, s_test.T)
    
    train_loader = torch.utils.data.DataLoader(train_dataset, batch_size=64, shuffle=True)
    test_loader = torch.utils.data.DataLoader(test_dataset, batch_size=1024, shuffle=False)
    
    return train_loader, test_loader
