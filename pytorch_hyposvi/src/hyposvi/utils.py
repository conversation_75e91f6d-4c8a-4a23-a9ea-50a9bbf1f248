"""
Utility functions for HypoSVI earthquake location.

This module provides utility functions for earthquake location,
including origin time calculation, likelihood assignment, and
coordinate transformations.
"""

import torch
import numpy as np
from typing import Union, Tuple, Dict, Any, Callable
from datetime import datetime, timedelta

from .distributions import (
    HuberDensity, LaplaceDensity, CauchyDensity, GaussianDensity, barron_loss
)
from .kernels import RBFKernel, IMQKernel


def get_origin_time(
    X: torch.Tensor,
    eikonet_model: torch.nn.Module,
    T_obs: torch.Tensor,
    device: torch.device = None
) -> Tuple[float, torch.Tensor]:
    """
    Calculate origin time given hypocenter location.
    
    This function determines the earthquake origin time by finding
    the time offset that minimizes the residuals between observed
    and predicted travel times.
    
    Args:
        X: Input coordinates tensor of shape (7, n_obs) or (7, n_obs, n_particles)
        eikonet_model: Trained EikoNet model
        T_obs: Observed arrival times tensor
        device: Computation device
        
    Returns:
        Tuple of (origin_time_offset, residuals)
    """
    if device is None:
        device = X.device
        
    eikonet_model.eval()
    with torch.no_grad():
        # Get predicted travel times
        T_pred = eikonet_model(X)
        
        if T_pred.dim() == 3:
            # Multiple particles: (1, n_obs, n_particles)
            T_obs_expanded = T_obs.unsqueeze(-1).expand_as(T_pred[0])
            residuals = T_obs_expanded - T_pred[0]  # (n_obs, n_particles)
            
            # Calculate mean residual for each particle
            origin_offsets = torch.mean(residuals, dim=0)  # (n_particles,)
            
            # Find best particle (minimum absolute offset)
            best_idx = torch.argmin(torch.abs(origin_offsets))
            best_offset = origin_offsets[best_idx].item()
            best_residuals = residuals[:, best_idx] - best_offset
            
            return best_offset, best_residuals
            
        else:
            # Single solution: (1, n_obs)
            T_pred = T_pred.squeeze(0)  # (n_obs,)
            residuals = T_obs - T_pred
            origin_offset = torch.mean(residuals).item()
            final_residuals = residuals - origin_offset
            
            return origin_offset, final_residuals


def assign_likelihood(params: Dict[str, Any]) -> Callable:
    """
    Assign likelihood function based on configuration.
    
    Args:
        params: Configuration parameters
        
    Returns:
        Likelihood function
    """
    likelihood_fn = params.get("likelihood_fn", "gaussian").lower()
    
    if likelihood_fn == "laplace":
        dist = LaplaceDensity(0.0, 1.0)
        return dist.log_pdf
    elif likelihood_fn == "cauchy":
        dist = CauchyDensity(0.0, 1.0)
        return dist.log_pdf
    elif likelihood_fn == "gaussian":
        dist = GaussianDensity(0.0, 1.0)
        return dist.log_pdf
    elif likelihood_fn == "huber":
        dist = HuberDensity(1.0)
        return dist.log_pdf
    elif likelihood_fn == "barron":
        alpha = params.get("barron_alpha", 1.5)
        return lambda x: -barron_loss(x, 1.0, alpha)
    else:
        raise ValueError(f"Unknown likelihood function: {likelihood_fn}")


def assign_kernel(params: Dict[str, Any]) -> str:
    """
    Assign kernel type based on configuration.
    
    Args:
        params: Configuration parameters
        
    Returns:
        Kernel type string
    """
    kernel_fn = params.get("kernel_fn", "rbf").upper()
    
    if kernel_fn == "RBF":
        return "rbf"
    elif kernel_fn == "IMQ":
        return "imq"
    else:
        raise ValueError(f"Unknown kernel function: {kernel_fn}")


def timedelta_seconds(t1: datetime, t2: datetime) -> float:
    """
    Calculate time difference in seconds.
    
    Args:
        t1: First datetime
        t2: Second datetime
        
    Returns:
        Time difference in seconds (t1 - t2)
    """
    return (t1 - t2).total_seconds()


def sec_to_datetime(seconds: float) -> timedelta:
    """
    Convert seconds to datetime timedelta.
    
    Args:
        seconds: Seconds value
        
    Returns:
        Timedelta object
    """
    # Handle sign
    sign = 1 if seconds >= 0 else -1
    abs_seconds = abs(seconds)
    
    # Extract integer seconds and milliseconds
    int_seconds = int(abs_seconds)
    milliseconds = int((abs_seconds - int_seconds) * 1000)
    
    return timedelta(seconds=sign * int_seconds, milliseconds=sign * milliseconds)


def logit(p: float) -> float:
    """
    Logit function: log(p / (1 - p))
    
    Args:
        p: Probability value in [0, 1]
        
    Returns:
        Logit value
    """
    if p <= 0 or p >= 1:
        raise ValueError("Probability must be in (0, 1)")
    return np.log(p / (1.0 - p))


def sigmoid(x: float) -> float:
    """
    Sigmoid function: 1 / (1 + exp(-x))
    
    Args:
        x: Input value
        
    Returns:
        Sigmoid value in [0, 1]
    """
    return 1.0 / (1.0 + np.exp(-x))


def huber_loss(x: Union[float, np.ndarray, torch.Tensor], delta: float = 1.0) -> Union[float, np.ndarray, torch.Tensor]:
    """
    Huber loss function.
    
    Args:
        x: Input values
        delta: Threshold parameter
        
    Returns:
        Huber loss values
    """
    is_torch = isinstance(x, torch.Tensor)
    if is_torch:
        abs_x = torch.abs(x)
        return torch.where(
            abs_x < delta,
            0.5 * x**2,
            delta * abs_x - 0.5 * delta**2
        )
    else:
        x = np.asarray(x)
        abs_x = np.abs(x)
        return np.where(
            abs_x < delta,
            0.5 * x**2,
            delta * abs_x - 0.5 * delta**2
        )


def tukey_loss(x: Union[float, np.ndarray, torch.Tensor], c: float = 4.685) -> Union[float, np.ndarray, torch.Tensor]:
    """
    Tukey biweight loss function.
    
    Args:
        x: Input values
        c: Tuning parameter
        
    Returns:
        Tukey loss values
    """
    is_torch = isinstance(x, torch.Tensor)
    if is_torch:
        abs_x = torch.abs(x)
        return torch.where(
            abs_x <= c,
            (c**2 / 6.0) * (1.0 - (1.0 - (x/c)**2)**3),
            c**2 / 6.0
        )
    else:
        x = np.asarray(x)
        abs_x = np.abs(x)
        return np.where(
            abs_x <= c,
            (c**2 / 6.0) * (1.0 - (1.0 - (x/c)**2)**3),
            c**2 / 6.0
        )


def cauchy_loss(x: Union[float, np.ndarray, torch.Tensor], c: float = 1.0) -> Union[float, np.ndarray, torch.Tensor]:
    """
    Cauchy loss function.
    
    Args:
        x: Input values
        c: Scale parameter
        
    Returns:
        Cauchy loss values
    """
    is_torch = isinstance(x, torch.Tensor)
    if is_torch:
        return 0.5 * c**2 * torch.log(1.0 + (x/c)**2)
    else:
        x = np.asarray(x)
        return 0.5 * c**2 * np.log(1.0 + (x/c)**2)


def estimate_pick_uncertainty(
    phases: torch.Tensor,
    default_p: float = 0.1,
    default_s: float = 0.1
) -> torch.Tensor:
    """
    Estimate pick uncertainties based on phase type.
    
    Args:
        phases: Phase indicators (0 for P, 1 for S)
        default_p: Default P-wave uncertainty
        default_s: Default S-wave uncertainty
        
    Returns:
        Uncertainty values for each pick
    """
    uncertainties = torch.where(
        phases <= 0.5,
        torch.tensor(default_p, dtype=phases.dtype, device=phases.device),
        torch.tensor(default_s, dtype=phases.dtype, device=phases.device)
    )
    
    return uncertainties


def compute_differential_times(
    T_obs: torch.Tensor,
    max_pairs: int = 500
) -> Tuple[torch.Tensor, torch.Tensor]:
    """
    Compute differential times between phase pairs.
    
    Args:
        T_obs: Observed arrival times
        max_pairs: Maximum number of pairs to compute
        
    Returns:
        Tuple of (pair_indices, differential_times)
    """
    n_obs = T_obs.size(0)
    
    # Generate all possible pairs
    if n_obs * (n_obs - 1) // 2 <= max_pairs:
        # Use all pairs
        i_indices = []
        j_indices = []
        for i in range(n_obs):
            for j in range(i + 1, n_obs):
                i_indices.append(i)
                j_indices.append(j)
    else:
        # Randomly sample pairs
        import random
        all_pairs = [(i, j) for i in range(n_obs) for j in range(i + 1, n_obs)]
        selected_pairs = random.sample(all_pairs, max_pairs)
        i_indices, j_indices = zip(*selected_pairs)
    
    pair_indices = torch.tensor([list(i_indices), list(j_indices)], dtype=torch.long)
    differential_times = T_obs[pair_indices[0]] - T_obs[pair_indices[1]]
    
    return pair_indices, differential_times


def validate_coordinates(
    X: torch.Tensor,
    coordinate_bounds: Dict[str, float],
    scale: float = 200.0
) -> bool:
    """
    Validate coordinate bounds.
    
    Args:
        X: Coordinate tensor
        coordinate_bounds: Dictionary with coordinate bounds
        scale: Coordinate scaling factor
        
    Returns:
        True if coordinates are valid
    """
    if torch.any(torch.isnan(X)) or torch.any(torch.isinf(X)):
        return False
        
    # Check normalized coordinate bounds (assuming X is normalized)
    if torch.any(X[:3] < 0) or torch.any(X[:3] > 1):
        return False
        
    # Check depth bounds
    z_min_norm = coordinate_bounds['z_min'] / scale
    z_max_norm = coordinate_bounds['z_max'] / scale
    
    if torch.any(X[2] < z_min_norm) or torch.any(X[2] > z_max_norm):
        return False
        
    return True
