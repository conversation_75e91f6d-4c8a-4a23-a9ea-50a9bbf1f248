"""
Kernel functions for Stein Variational Inference.

This module implements various kernel functions used in SVI,
including RBF and IMQ kernels with automatic bandwidth selection.
"""

import torch
import numpy as np
from typing import Union, Tuple, Optional


class RBFKernel:
    """
    Radial Basis Function (RBF) kernel.
    
    Also known as Gaussian kernel. Computes:
    K(x, y) = exp(-γ * ||x - y||²)
    
    Args:
        bandwidth: Kernel bandwidth (if None, uses median heuristic)
        gamma: Kernel parameter (if None, computed from bandwidth)
    """
    
    def __init__(self, bandwidth: Optional[float] = None, gamma: Optional[float] = None):
        self.bandwidth = bandwidth
        self.gamma = gamma
        
    def __call__(
        self, 
        X: torch.Tensor, 
        Y: Optional[torch.Tensor] = None,
        bandwidth: Optional[float] = None
    ) -> torch.Tensor:
        """
        Compute RBF kernel matrix.
        
        Args:
            X: Input tensor of shape (n_features, n_samples_x)
            Y: Optional second tensor of shape (n_features, n_samples_y)
            bandwidth: Optional bandwidth override
            
        Returns:
            Kernel matrix of shape (n_samples_x, n_samples_y)
        """
        if Y is None:
            Y = X
            
        # Use provided bandwidth or compute using median heuristic
        if bandwidth is not None:
            h = bandwidth
        elif self.bandwidth is not None:
            h = self.bandwidth
        else:
            h = self.median_bandwidth_heuristic(X)
            
        # Compute pairwise squared distances efficiently
        # ||x - y||² = ||x||² + ||y||² - 2<x, y>
        X_norm_sq = torch.sum(X**2, dim=0, keepdim=True)  # (1, n_x)
        Y_norm_sq = torch.sum(Y**2, dim=0, keepdim=True)  # (1, n_y)
        
        # Gram matrix: <X, Y>
        XY = torch.mm(X.T, Y)  # (n_x, n_y)
        
        # Squared distances
        dist_sq = X_norm_sq.T + Y_norm_sq - 2.0 * XY  # (n_x, n_y)
        
        # RBF kernel
        gamma = 1.0 / (1e-8 + 2.0 * h**2)
        K = torch.exp(-gamma * dist_sq)
        
        return K
        
    def median_bandwidth_heuristic(self, X: torch.Tensor) -> float:
        """
        Compute bandwidth using median heuristic.
        
        Args:
            X: Input tensor of shape (n_features, n_samples)
            
        Returns:
            Bandwidth value
        """
        n_samples = X.size(1)
        
        if n_samples <= 1:
            return 1.0
            
        # Compute pairwise squared distances
        X_norm_sq = torch.sum(X**2, dim=0, keepdim=True)
        XX = torch.mm(X.T, X)
        dist_sq = X_norm_sq.T + X_norm_sq - 2.0 * XX
        
        # Get upper triangular part (excluding diagonal)
        triu_indices = torch.triu_indices(n_samples, n_samples, offset=1)
        distances_sq = dist_sq[triu_indices[0], triu_indices[1]]
        
        # Median heuristic: h² = median(distances²) / (2 * log(n + 1))
        median_dist_sq = torch.median(distances_sq)
        h_sq = median_dist_sq / (2.0 * np.log(n_samples + 1.0))
        
        return torch.sqrt(h_sq).item()
        
    def gradient(self, X: torch.Tensor, bandwidth: Optional[float] = None) -> torch.Tensor:
        """
        Compute gradient of kernel matrix with respect to X.
        
        Args:
            X: Input tensor of shape (n_features, n_samples)
            bandwidth: Optional bandwidth override
            
        Returns:
            Gradient tensor of shape (n_features, n_samples)
        """
        # Use provided bandwidth or compute using median heuristic
        if bandwidth is not None:
            h = bandwidth
        elif self.bandwidth is not None:
            h = self.bandwidth
        else:
            h = self.median_bandwidth_heuristic(X)
            
        n_features, n_samples = X.shape
        gamma = 1.0 / (1e-8 + 2.0 * h**2)
        
        # Compute kernel matrix
        K = self(X, bandwidth=h)
        
        # Compute gradient: ∇_x K(x, y) = -2γ * K(x, y) * (x - y)
        # For sum over all pairs: ∇_x Σ_y K(x, y) = -2γ * Σ_y K(x, y) * (x - y)
        
        grad = torch.zeros_like(X)
        
        for i in range(n_samples):
            x_i = X[:, i:i+1]  # (n_features, 1)
            
            # Differences: x_i - X
            diff = x_i - X  # (n_features, n_samples)
            
            # Weighted sum: Σ_j K(x_i, x_j) * (x_i - x_j)
            weights = K[i, :].unsqueeze(0)  # (1, n_samples)
            grad[:, i] = -2.0 * gamma * torch.sum(weights * diff, dim=1)
            
        return grad


class IMQKernel:
    """
    Inverse Multi-Quadratic (IMQ) kernel.
    
    Computes: K(x, y) = (h² + ||x - y||²)^β
    where β is typically negative (e.g., -0.5).
    
    Args:
        bandwidth: Kernel bandwidth
        beta: Power parameter (typically negative)
    """
    
    def __init__(self, bandwidth: float = 1.0, beta: float = -0.5):
        self.bandwidth = bandwidth
        self.beta = beta
        
    def __call__(
        self, 
        X: torch.Tensor, 
        Y: Optional[torch.Tensor] = None,
        bandwidth: Optional[float] = None
    ) -> torch.Tensor:
        """
        Compute IMQ kernel matrix.
        
        Args:
            X: Input tensor of shape (n_features, n_samples_x)
            Y: Optional second tensor of shape (n_features, n_samples_y)
            bandwidth: Optional bandwidth override
            
        Returns:
            Kernel matrix of shape (n_samples_x, n_samples_y)
        """
        if Y is None:
            Y = X
            
        # Use provided bandwidth or default
        h = bandwidth if bandwidth is not None else self.bandwidth
        
        # Compute pairwise squared distances
        X_norm_sq = torch.sum(X**2, dim=0, keepdim=True)
        Y_norm_sq = torch.sum(Y**2, dim=0, keepdim=True)
        XY = torch.mm(X.T, Y)
        dist_sq = X_norm_sq.T + Y_norm_sq - 2.0 * XY
        
        # IMQ kernel
        K = (h**2 + dist_sq)**self.beta
        
        return K
        
    def gradient(self, X: torch.Tensor, bandwidth: Optional[float] = None) -> torch.Tensor:
        """
        Compute gradient of kernel matrix with respect to X.
        
        Args:
            X: Input tensor of shape (n_features, n_samples)
            bandwidth: Optional bandwidth override
            
        Returns:
            Gradient tensor of shape (n_features, n_samples)
        """
        h = bandwidth if bandwidth is not None else self.bandwidth
        n_features, n_samples = X.shape
        
        # Compute kernel matrix and distances
        X_norm_sq = torch.sum(X**2, dim=0, keepdim=True)
        XX = torch.mm(X.T, X)
        dist_sq = X_norm_sq.T + X_norm_sq - 2.0 * XX
        
        # IMQ kernel values
        K = (h**2 + dist_sq)**self.beta
        
        # Gradient: ∇_x K(x, y) = 2β * (h² + ||x - y||²)^(β-1) * (x - y)
        grad = torch.zeros_like(X)
        
        for i in range(n_samples):
            x_i = X[:, i:i+1]
            diff = x_i - X  # (n_features, n_samples)
            
            # Kernel derivatives
            dist_sq_i = dist_sq[i, :].unsqueeze(0)  # (1, n_samples)
            K_deriv = 2.0 * self.beta * (h**2 + dist_sq_i)**(self.beta - 1.0)
            
            # Gradient
            grad[:, i] = torch.sum(K_deriv * diff, dim=1)
            
        return grad


def median_bandwidth_heuristic(X: torch.Tensor) -> float:
    """
    Compute bandwidth using median heuristic.
    
    This is a standalone function that can be used with any kernel.
    
    Args:
        X: Input tensor of shape (n_features, n_samples)
        
    Returns:
        Bandwidth value
    """
    kernel = RBFKernel()
    return kernel.median_bandwidth_heuristic(X)


def compute_kernel_matrix(
    X: torch.Tensor,
    kernel_type: str = "rbf",
    bandwidth: Optional[float] = None,
    **kwargs
) -> torch.Tensor:
    """
    Compute kernel matrix using specified kernel type.
    
    Args:
        X: Input tensor of shape (n_features, n_samples)
        kernel_type: Type of kernel ("rbf" or "imq")
        bandwidth: Kernel bandwidth (if None, uses median heuristic for RBF)
        **kwargs: Additional kernel parameters
        
    Returns:
        Kernel matrix of shape (n_samples, n_samples)
    """
    if kernel_type.lower() == "rbf":
        kernel = RBFKernel(bandwidth=bandwidth)
        return kernel(X)
    elif kernel_type.lower() == "imq":
        beta = kwargs.get("beta", -0.5)
        h = bandwidth if bandwidth is not None else 1.0
        kernel = IMQKernel(bandwidth=h, beta=beta)
        return kernel(X)
    else:
        raise ValueError(f"Unknown kernel type: {kernel_type}")


def compute_kernel_gradient(
    X: torch.Tensor,
    kernel_type: str = "rbf", 
    bandwidth: Optional[float] = None,
    **kwargs
) -> torch.Tensor:
    """
    Compute gradient of kernel matrix with respect to X.
    
    Args:
        X: Input tensor of shape (n_features, n_samples)
        kernel_type: Type of kernel ("rbf" or "imq")
        bandwidth: Kernel bandwidth
        **kwargs: Additional kernel parameters
        
    Returns:
        Gradient tensor of shape (n_features, n_samples)
    """
    if kernel_type.lower() == "rbf":
        kernel = RBFKernel(bandwidth=bandwidth)
        return kernel.gradient(X)
    elif kernel_type.lower() == "imq":
        beta = kwargs.get("beta", -0.5)
        h = bandwidth if bandwidth is not None else 1.0
        kernel = IMQKernel(bandwidth=h, beta=beta)
        return kernel.gradient(X)
    else:
        raise ValueError(f"Unknown kernel type: {kernel_type}")
