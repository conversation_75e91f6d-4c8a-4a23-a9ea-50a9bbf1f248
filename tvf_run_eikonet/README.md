# TVF EikoNet 训练脚本

本目录包含使用TVF数据训练EikoNet神经网络的脚本和配置文件。

## 文件说明

### 配置文件
- `tvf_eikonet_config.json` - 主要配置文件，包含数据路径、训练参数、模型架构等设置

### 训练脚本
- `train_tvf_eikonet.py` - 完整版训练脚本，包含每个epoch的速度对比图绘制
- `train_tvf_eikonet_simple.py` - 简化版训练脚本，用于快速测试

### 数据文件（位于 ../tvf_hyposvi_data/）
- `hyposvi_station.csv` - 台站坐标数据
- `hyposvi_vel.csv` - 1D速度模型数据
- `2011_pre_cat.csv` - 地震事件目录（训练中不直接使用）
- `2011_phase.csv` - 地震相位数据（训练中不直接使用）

## 配置说明

### 数据配置
```json
"data": {
    "station_file": "../tvf_hyposvi_data/hyposvi_station.csv",
    "velmod_file": "../tvf_hyposvi_data/hyposvi_vel.csv",
    "coordinate_bounds": {
        "lon_min": 97.5,
        "lon_max": 100.0,
        "lat_min": 23.0,
        "lat_max": 26.0,
        "z_min": 0.0,
        "z_max": 80.0
    },
    "scale": 200.0
}
```

### 训练配置
```json
"training": {
    "batch_size": 256,
    "n_train": 50000,
    "n_test": 10000,
    "n_epochs": 100,
    "lr": 0.001
}
```

## 使用方法

### 1. 快速测试（推荐首次使用）
```bash
cd /home/<USER>/run_eikonet_hyposvi/tvf_run_eikonet
python train_tvf_eikonet_simple.py
```

简化版特点：
- 训练样本：5000（原50000）
- 测试样本：1000（原10000）
- 训练轮次：20（原100）
- 训练时间：约5-10分钟

### 2. 完整训练
```bash
cd /home/<USER>/run_eikonet_hyposvi/tvf_run_eikonet
python train_tvf_eikonet.py
```

完整版特点：
- 每10个epoch绘制速度对比图
- 完整的50000训练样本
- 100个训练轮次
- 训练时间：约30-60分钟（取决于GPU）

## 输出文件

### 训练完成后会生成以下文件：

#### 简化版输出
- `tvf_eikonet_model_simple.pth` - 训练好的模型
- `tvf_training_results_simple.json` - 训练结果和配置
- `tvf_training_history_simple.png` - 训练损失曲线
- `tvf_velocity_model_simple.png` - 输入速度模型图

#### 完整版输出
- `tvf_eikonet_model.pth` - 训练好的模型
- `tvf_training_results.json` - 训练结果和配置
- `tvf_training_history.png` - 训练损失曲线
- `tvf_velocity_comparison.pdf` - 最终速度对比图
- `tvf_velocity_model.png` - 输入速度模型图
- `plots/` - 每个epoch的速度对比图目录
  - `velocity_comparison_epoch_001.png`
  - `velocity_comparison_epoch_010.png`
  - ...

## 数据格式说明

### 台站数据格式 (hyposvi_station.csv)
```csv
network,station,latitude,longitude,elevation
YN,BAS,25.118,99.1466,1.77
YN,CZS,24.904,98.667,1.34
...
```

### 速度模型格式 (hyposvi_vel.csv)
```csv
depth,vp,vs
0.000,5.000,2.890
10.000,5.720,3.300
...
```

## 训练原理

EikoNet训练使用1D速度模型生成合成训练数据：
1. 随机采样源-接收器对
2. 根据速度模型计算真实走时
3. 训练神经网络预测走时
4. 使用Eikonal方程作为物理约束

## 注意事项

1. **GPU使用**：建议使用GPU训练，可显著加速
2. **内存需求**：完整训练需要约2-4GB GPU内存
3. **坐标系统**：使用归一化坐标系统，scale=200.0
4. **深度范围**：当前设置为0-80km，适合区域地震研究

## 训练结果示例

### 简化训练结果
- 训练时间：约24秒
- 最佳损失：0.0134
- 训练样本：5,000
- 训练轮次：20

### 完整训练结果
- 训练时间：约19分钟
- 最佳损失：0.0028
- 训练样本：50,000
- 训练轮次：100
- 生成11个速度对比图（每10个epoch一个）

## 使用训练好的模型

训练完成后，可以使用以下代码加载和使用模型：

```python
import torch
import sys
from pathlib import Path

# 添加src路径
src_dir = Path("../pytorch_hyposvi/src")
sys.path.insert(0, str(src_dir))

from eikonet.models import EikoNet

# 加载模型
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
model = EikoNet(scale=200.0)
model.load_state_dict(torch.load('tvf_eikonet_model.pth', map_location=device))
model.to(device)
model.eval()

# 使用模型预测走时
# x: [x_src, y_src, z_src, x_rec, y_rec, z_rec, phase] (7, n_samples)
# 其中坐标已归一化到[0,1]，phase: 0=P波, 1=S波
with torch.no_grad():
    travel_times = model(x)
```

## 故障排除

### 常见问题
1. **CUDA内存不足**：减少batch_size或训练样本数
2. **导入错误**：确保pytorch_hyposvi/src在Python路径中
3. **数据文件未找到**：检查相对路径是否正确
4. **训练损失不下降**：检查学习率设置，可能需要调整

### 调试建议
1. 首先运行简化版确保环境正确
2. 检查GPU可用性：`torch.cuda.is_available()`
3. 监控训练损失是否正常下降
4. 查看速度对比图确认模型学习效果

### 性能优化
1. 使用GPU训练可显著加速（约10-20倍）
2. 增加batch_size可提高GPU利用率
3. 使用混合精度训练可进一步加速
