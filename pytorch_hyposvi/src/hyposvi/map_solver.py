"""
MAP (Maximum A Posteriori) solver for earthquake location.

This module implements the MAP optimization approach for earthquake
hypocenter location using gradient-based optimization.
"""

import torch
import torch.optim as optim
import numpy as np
from typing import Dict, Any, Tuple, Optional, Callable
from datetime import datetime

from .utils import get_origin_time, assign_likelihood
from .distributions import GaussianDensity


class MAPSolver:
    """
    MAP solver for earthquake location.
    
    Uses gradient-based optimization to find the maximum a posteriori
    estimate of earthquake hypocenter parameters.
    
    Args:
        eikonet_model: Trained EikoNet model for travel time prediction
        config: Configuration dictionary
        device: Computation device
    """
    
    def __init__(
        self,
        eikonet_model: torch.nn.Module,
        config: Dict[str, Any],
        device: torch.device = None
    ):
        self.eikonet_model = eikonet_model
        self.config = config
        
        if device is None:
            self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        else:
            self.device = device
            
        # Move model to device
        self.eikonet_model.to(self.device)
        self.eikonet_model.eval()
        
        # Extract configuration parameters
        self.n_epochs = config.get('n_epochs', 2000)
        self.learning_rate = config.get('learning_rate', 1e-5)
        self.iter_tol = config.get('iter_tol', 1e-2)
        self.scale = config.get('scale', 200.0)
        
        # Prior parameters (keep in km, will be scaled when used)
        self.prior_z_mean_km = config.get('prior_z_mean', 10.0)
        self.prior_z_std_km = config.get('prior_z_std', 15.0)
        # Convert to scaled coordinates for internal use
        self.prior_z_mean = self.prior_z_mean_km / self.scale
        self.prior_z_std = self.prior_z_std_km / self.scale
        self.prior_scale_param = config.get('prior_scale_param', 2.0)
        
        # Uncertainty parameters
        self.pick_unc_p = config.get('pick_unc_p', 0.10)
        self.pick_unc_s = config.get('pick_unc_s', 0.10)
        
        # Likelihood function
        self.likelihood_fn = assign_likelihood(config)
        
        # Coordinate bounds
        coord_bounds = config.get('coordinate_bounds', {})
        self.z_min = coord_bounds.get('z_min', -3.0) / self.scale
        self.z_max = coord_bounds.get('z_max', 25.0) / self.scale  # Match Julia version
        
        # Prevent airquakes
        self.prevent_airquakes = config.get('prevent_airquakes', True)
        
    def locate_event(
        self,
        X_obs: torch.Tensor,
        T_obs: torch.Tensor,
        initial_guess: Optional[torch.Tensor] = None,
        prior_info: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Locate earthquake using MAP optimization.

        Args:
            X_obs: Observation coordinates (4, n_obs) [X, Y, Z, phase]
            T_obs: Observed arrival times (n_obs,)
            initial_guess: Initial hypocenter guess (3,) [X, Y, Z]
            prior_info: Prior information from original catalog

        Returns:
            Dictionary with location results
        """
        X_obs = X_obs.to(self.device)
        T_obs = T_obs.to(self.device)
        n_obs = X_obs.size(1)
        
        # Initialize hypocenter parameters and set adaptive priors
        if initial_guess is not None:
            X_src = initial_guess.clone().to(self.device)
            # Use initial guess depth as adaptive prior center
            self.adaptive_prior_z_mean = X_src[2].item()
        else:
            # Julia-style initialization: [0.5, 0.5, prior_z_mean/scale]
            X_src = torch.tensor([0.5, 0.5, self.prior_z_mean],
                               device=self.device, dtype=torch.float32)
            self.adaptive_prior_z_mean = self.prior_z_mean

        # Set adaptive priors from original catalog if provided
        if prior_info is not None:
            # Use original event location as adaptive prior centers
            self.adaptive_prior_east = prior_info['east_scaled']
            self.adaptive_prior_north = prior_info['north_scaled']
            self.adaptive_prior_z_mean = prior_info['depth_scaled']
            self.adaptive_prior_depth_km = prior_info['depth_km']
            print(f"      🎯 设置自适应先验: 深度 {self.adaptive_prior_depth_km:.2f} km")
        else:
            # Use default priors
            self.adaptive_prior_east = None
            self.adaptive_prior_north = None
            self.adaptive_prior_depth_km = None

        X_src.requires_grad_(True)
        
        n_obs = X_obs.size(1)

        # Setup optimizer (Julia uses NewtonTrustRegion, we use LBFGS as closest equivalent)
        optimizer = optim.LBFGS([X_src], lr=self.learning_rate, max_iter=20)

        # Track optimization
        losses = []
        best_loss = float('inf')
        best_params = X_src.clone()
        patience_counter = 0
        
        # LBFGS optimization (Julia-style)
        def closure():
            optimizer.zero_grad()

            # Construct input for EikoNet
            X_input = self._construct_input(X_src, X_obs)

            # Calculate origin time and residuals
            origin_time, residuals = get_origin_time(X_input, self.eikonet_model, T_obs)

            # Calculate likelihood
            likelihood_loss = self._calculate_likelihood(residuals, X_obs[3])

            # Calculate prior
            prior_loss = self._calculate_prior(X_src)

            # Total loss (negative log posterior)
            total_loss = -likelihood_loss - prior_loss

            # Apply constraints
            constraint_loss = self._apply_constraints(X_src)
            total_loss += constraint_loss

            # Backward pass
            total_loss.backward()

            return total_loss

        prev_params = X_src.clone()
        for epoch in range(self.n_epochs):
            # LBFGS step
            loss = optimizer.step(closure)
            current_loss = loss.item()
            losses.append(current_loss)

            if current_loss < best_loss:
                best_loss = current_loss
                best_params = X_src.clone().detach()
                patience_counter = 0
            else:
                patience_counter += 1

            # Julia-style convergence check
            param_change = torch.norm(X_src - prev_params).item()
            if param_change < self.iter_tol:
                break

            prev_params = X_src.clone()

            # Early stopping
            if patience_counter > 50:  # Reduced for LBFGS
                break
                
        # Final evaluation with best parameters
        X_src_final = best_params
        X_input_final = self._construct_input(X_src_final, X_obs)
        
        with torch.no_grad():
            T_pred_final = self.eikonet_model(X_input_final).squeeze(0)
            origin_time_final, residuals_final = get_origin_time(
                X_input_final, self.eikonet_model, T_obs
            )
            
        # Calculate uncertainties (simplified)
        uncertainties = self._estimate_uncertainties(X_src_final, X_obs, T_obs)
        
        # Prepare results - convert scaled coordinates back to km
        hypocenter_scaled = X_src_final.cpu().numpy()
        # East and North: scaled_coord * scale * 1000 / 1000 = scaled_coord * scale (already in km)
        # Depth: scaled_coord * scale (already in km)
        # The coordinates are already properly scaled for km units
        results = {
            'hypocenter': hypocenter_scaled,  # Keep as scaled coordinates for consistency
            'origin_time_offset': origin_time_final,
            'residuals': residuals_final.cpu().numpy(),
            'predicted_times': T_pred_final.cpu().numpy(),
            'uncertainties': uncertainties,
            'n_iterations': epoch + 1,
            'final_loss': best_loss,
            'convergence_history': losses,
            'n_observations': n_obs
        }
        
        return results
        
    def _construct_input(self, X_src: torch.Tensor, X_obs: torch.Tensor) -> torch.Tensor:
        """
        Construct input tensor for EikoNet.
        
        Args:
            X_src: Source coordinates (3,)
            X_obs: Observation coordinates (4, n_obs)
            
        Returns:
            Input tensor (7, n_obs)
        """
        n_obs = X_obs.size(1)
        X_input = torch.zeros(7, n_obs, device=self.device)
        
        # Source coordinates (repeated for each observation)
        X_input[:3] = X_src.unsqueeze(1).expand(-1, n_obs)
        
        # Receiver coordinates and phases
        X_input[3:] = X_obs
        
        return X_input
        
    def _calculate_likelihood(self, residuals: torch.Tensor, phases: torch.Tensor) -> torch.Tensor:
        """
        Calculate likelihood contribution.
        
        Args:
            residuals: Travel time residuals
            phases: Phase indicators
            
        Returns:
            Log likelihood
        """
        # Estimate uncertainties based on phase type
        uncertainties = torch.where(
            phases <= 0.5,
            torch.tensor(self.pick_unc_p, device=self.device),
            torch.tensor(self.pick_unc_s, device=self.device)
        )
        
        # Standardized residuals
        standardized_residuals = residuals / uncertainties
        
        # Calculate log likelihood
        log_likelihood = torch.sum(self.likelihood_fn(standardized_residuals))
        
        return log_likelihood
        
    def _calculate_prior(self, X_src: torch.Tensor) -> torch.Tensor:
        """
        Calculate prior contribution using adaptive priors from original catalog.

        Args:
            X_src: Source coordinates

        Returns:
            Log prior
        """
        total_prior = torch.tensor(0.0, device=self.device)

        # Adaptive depth prior based on original event depth
        if hasattr(self, 'adaptive_prior_z_mean') and self.adaptive_prior_z_mean is not None:
            # Use individual event's original depth as prior center (in scaled coordinates)
            adaptive_prior_center = self.adaptive_prior_z_mean
            # Use a weak prior that allows reasonable variation around original depth
            # Standard deviation of ~2km in real coordinates (tighter than before)
            prior_std_scaled = 2.0 / self.scale
            z_prior = -0.5 * ((X_src[2] - adaptive_prior_center) / prior_std_scaled)**2
            total_prior += z_prior
        else:
            # Fallback to fixed depth prior
            z_prior = -0.5 * ((X_src[2] - self.prior_z_mean) / (self.prior_z_std / self.scale))**2
            total_prior += z_prior

        # Adaptive horizontal position priors based on original event location
        if hasattr(self, 'adaptive_prior_east') and self.adaptive_prior_east is not None:
            # Use original event location as prior center for horizontal position
            # Standard deviation of ~5km in real coordinates (allows reasonable relocation)
            xy_prior_std_scaled = 5.0 / self.scale / 1000.0  # Convert km to scaled coordinates

            # East prior
            east_prior = -0.5 * ((X_src[0] - self.adaptive_prior_east) / xy_prior_std_scaled)**2
            total_prior += east_prior

            # North prior
            north_prior = -0.5 * ((X_src[1] - self.adaptive_prior_north) / xy_prior_std_scaled)**2
            total_prior += north_prior
        else:
            # Weak uniform-like horizontal prior (default behavior)
            xy_prior = torch.tensor(0.0, device=self.device)
            total_prior += xy_prior

        return total_prior
        
    def _apply_constraints(self, X_src: torch.Tensor) -> torch.Tensor:
        """
        Apply coordinate constraints.
        
        Args:
            X_src: Source coordinates
            
        Returns:
            Constraint penalty
        """
        penalty = torch.tensor(0.0, device=self.device)
        
        # Softer depth constraints
        if X_src[2] < self.z_min:
            penalty += 100.0 * (self.z_min - X_src[2])**2
        if X_src[2] > self.z_max:
            penalty += 100.0 * (X_src[2] - self.z_max)**2

        # Prevent airquakes (softer constraint)
        if self.prevent_airquakes and X_src[2] < 0:
            penalty += 1000.0 * X_src[2]**2
            
        # Horizontal bounds (assuming normalized coordinates [0, 1])
        for i in range(2):
            if X_src[i] < 0:
                penalty += 1000.0 * X_src[i]**2
            if X_src[i] > 1:
                penalty += 1000.0 * (X_src[i] - 1.0)**2
                
        return penalty
        
    def _estimate_uncertainties(
        self,
        X_src: torch.Tensor,
        X_obs: torch.Tensor,
        T_obs: torch.Tensor
    ) -> Dict[str, float]:
        """
        Estimate parameter uncertainties using finite differences.
        
        Args:
            X_src: Source coordinates
            X_obs: Observation coordinates
            T_obs: Observed times
            
        Returns:
            Dictionary with uncertainty estimates
        """
        # This is a simplified uncertainty estimation
        # In practice, you might want to use the Hessian matrix
        
        eps = 1e-4
        uncertainties = {}
        
        for i, param_name in enumerate(['x', 'y', 'z']):
            # Finite difference approximation of second derivative
            X_plus = X_src.clone()
            X_minus = X_src.clone()
            X_plus[i] += eps
            X_minus[i] -= eps
            
            # Calculate losses
            loss_plus = self._evaluate_loss(X_plus, X_obs, T_obs)
            loss_minus = self._evaluate_loss(X_minus, X_obs, T_obs)
            loss_center = self._evaluate_loss(X_src, X_obs, T_obs)
            
            # Second derivative approximation
            second_deriv = (loss_plus - 2*loss_center + loss_minus) / (eps**2)
            
            # Uncertainty estimate (inverse of curvature)
            if second_deriv > 1e-8:
                uncertainties[param_name] = (1.0 / np.sqrt(second_deriv)) * self.scale
            else:
                uncertainties[param_name] = float('inf')
                
        return uncertainties
        
    def _evaluate_loss(
        self,
        X_src: torch.Tensor,
        X_obs: torch.Tensor,
        T_obs: torch.Tensor
    ) -> float:
        """
        Evaluate loss function for given parameters.
        
        Args:
            X_src: Source coordinates
            X_obs: Observation coordinates
            T_obs: Observed times
            
        Returns:
            Loss value
        """
        with torch.no_grad():
            X_input = self._construct_input(X_src, X_obs)
            origin_time, residuals = get_origin_time(X_input, self.eikonet_model, T_obs)
            
            likelihood_loss = self._calculate_likelihood(residuals, X_obs[3])
            prior_loss = self._calculate_prior(X_src)
            constraint_loss = self._apply_constraints(X_src)
            
            total_loss = -likelihood_loss - prior_loss + constraint_loss
            
        return total_loss.item()
