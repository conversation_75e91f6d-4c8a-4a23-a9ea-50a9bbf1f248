"""
EikoNet neural network models.

This module implements the main EikoNet architecture for solving the Eikonal
equation and predicting seismic travel times.
"""

import torch
import torch.nn as nn
from typing import Optional, Dict, Any
from .layers import CylindricalSymmetry, SkipConnection


class EikoNet(nn.Module):
    """
    EikoNet neural network for seismic travel time prediction.
    
    The network consists of two components:
    1. τ0: Straight-line travel time (analytical)
    2. τ1: Neural network correction term
    
    The final travel time is: τ = τ0 * τ1
    
    Args:
        scale: Scaling factor for coordinates
        hidden_dims: List of hidden layer dimensions
        activation: Activation function name
        use_skip_connections: Whether to use skip connections
    """
    
    def __init__(
        self,
        scale: float = 200.0,
        hidden_dims: Optional[list] = None,
        activation: str = "elu",
        use_skip_connections: bool = True
    ):
        super().__init__()
        
        self.scale = scale
        
        if hidden_dims is None:
            hidden_dims = [16, 32, 16, 32, 16, 32, 16, 32, 16, 32, 16, 32, 16]
            
        # Get activation function
        if activation.lower() == "elu":
            act_fn = nn.ELU()
        elif activation.lower() == "relu":
            act_fn = nn.ReLU()
        elif activation.lower() == "tanh":
            act_fn = nn.Tanh()
        else:
            raise ValueError(f"Unsupported activation: {activation}")
            
        # Build τ1 network
        layers = []
        
        # Cylindrical symmetry transformation
        layers.append(CylindricalSymmetry(in_dims=7, out_dims=4))
        
        # First dense layer
        layers.append(nn.Linear(4, 16))
        layers.append(act_fn)
        
        # Skip connection blocks (matching Julia implementation)
        if use_skip_connections:
            # 6 skip connection blocks
            for _ in range(6):
                skip_layers = nn.Sequential(
                    nn.Linear(16, 32),
                    nn.ELU(),
                    nn.Linear(32, 16),
                    nn.ELU()
                )
                layers.append(SkipConnection(skip_layers))
        else:
            # Alternative: regular dense layers
            current_dim = 16
            for dim in hidden_dims[1:-1]:
                layers.append(nn.Linear(current_dim, dim))
                layers.append(act_fn)
                current_dim = dim
                
        # Output layer
        layers.append(nn.Linear(16, 1))
        layers.append(nn.ReLU())  # Ensure positive output

        self.tau1_network = nn.Sequential(*layers)

        # Initialize weights to ensure reasonable initial output
        self._initialize_weights()

    def _initialize_weights(self):
        """Initialize network weights for reasonable initial output."""
        for module in self.tau1_network.modules():
            if isinstance(module, nn.Linear):
                # Xavier/Glorot initialization
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.constant_(module.bias, 0.1)  # Small positive bias

        # Special initialization for output layer to ensure positive initial output
        for module in reversed(list(self.tau1_network.modules())):
            if isinstance(module, nn.Linear):
                # Last linear layer - initialize to output around 1.0
                nn.init.constant_(module.weight, 0.1)
                if module.bias is not None:
                    nn.init.constant_(module.bias, 1.0)
                break

    def tau0(self, x: torch.Tensor) -> torch.Tensor:
        """
        Calculate straight-line travel time τ0.
        
        Args:
            x: Input coordinates tensor
            
        Returns:
            Straight-line travel time
        """
        if x.dim() == 2 and x.size(0) == 7:
            # Julia-style format: (7, n_samples)
            src_coords = x[0:3]  # [x_src, y_src, z_src]
            rec_coords = x[3:6]  # [x_rec, y_rec, z_rec]
            
        elif x.dim() == 2 and x.size(1) == 7:
            # PyTorch format: (batch_size, 7)
            src_coords = x[:, 0:3].T  # Transpose for consistency
            rec_coords = x[:, 3:6].T
            
        elif x.dim() == 3:
            # 3D format: (7, n_samples, n_particles) or similar
            src_coords = x[0:3]
            rec_coords = x[3:6]
            
        else:
            raise ValueError(f"Unexpected input shape: {x.shape}")
            
        # Calculate Euclidean distance
        distance = torch.sqrt(torch.sum((rec_coords - src_coords)**2, dim=0, keepdim=True))
        
        return distance
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Forward pass of EikoNet.

        Args:
            x: Input tensor of shape (7, n_samples) or (batch_size, 7)
               [x_src, y_src, z_src, x_rec, y_rec, z_rec, phase]

        Returns:
            Predicted travel times: τ = τ0 * τ1
        """
        # Calculate τ0 (straight-line travel time)
        tau0_val = self.tau0(x)

        # Calculate τ1 (neural network correction)
        if x.dim() == 2 and x.size(0) == 7:
            # Julia-style input: transpose for PyTorch layers
            x_input = x.T
        else:
            # Already in PyTorch format
            x_input = x

        tau1_val = self.tau1_network(x_input)

        # Ensure dimensions match for multiplication
        if tau1_val.dim() == 2 and tau1_val.size(1) == 1:
            tau1_val = tau1_val.T  # Convert to (1, n_samples)
        elif tau1_val.dim() == 2 and tau1_val.size(0) == x_input.size(0):
            tau1_val = tau1_val.T  # Convert from (batch_size, 1) to (1, batch_size)

        # Final travel time: τ = τ0 * τ1
        # τ1 should be positive due to ReLU output layer
        tau = tau0_val * tau1_val

        return tau


def build_eikonet_from_config(config: Dict[str, Any]) -> EikoNet:
    """
    Build EikoNet model from configuration dictionary.
    
    Args:
        config: Configuration dictionary
        
    Returns:
        Configured EikoNet model
    """
    model_config = config.get("model", {})
    
    scale = model_config.get("scale", 200.0)
    
    # Extract architecture parameters
    arch_config = model_config.get("architecture", {})
    
    model = EikoNet(
        scale=scale,
        activation="elu",  # Default from Julia implementation
        use_skip_connections=True
    )
    
    return model
