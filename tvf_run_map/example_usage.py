#!/usr/bin/env python3
"""
完整的MAP定位与走时验证示例

这个脚本展示了如何运行完整的地震重定位程序，
包括EikoNet定位和TauP验证功能
"""

import sys
import time
from pathlib import Path

# 添加路径
script_dir = Path(__file__).parent
sys.path.insert(0, str(script_dir))

from run_tvf_map_ultra_fast import UltraFastMAPProcessor, main
import pandas as pd

def run_example_with_validation():
    """运行带验证的完整示例"""
    
    print("🌟 TVF地震重定位完整示例")
    print("=" * 70)
    print("此示例将运行MAP地震重定位，并使用TauP进行走时验证")
    print()
    
    # 文件路径检查
    config_file = script_dir / "tvf_map_config.json"
    model_file = script_dir.parent / "tvf_run_eikonet" / "tvf_eikonet_model.pth"
    phase_file = script_dir.parent / "tvf_hyposvi_data" / "2011_phase.csv"
    
    print("🔍 检查必需文件...")
    files_check = {
        "配置文件": config_file,
        "EikoNet模型": model_file,
        "震相数据": phase_file
    }
    
    for name, path in files_check.items():
        if path.exists():
            print(f"   ✅ {name}: {path}")
        else:
            print(f"   ❌ {name}: {path} (不存在)")
            return False
    
    # 数据统计
    phases_df = pd.read_csv(phase_file)
    event_ids = sorted(phases_df['evid'].unique())
    
    print(f"\n📊 数据统计:")
    print(f"   - 总事件数: {len(event_ids)}")
    print(f"   - 总震相数: {len(phases_df)}")
    print(f"   - 台站数: {phases_df['station'].nunique()}")
    
    # 选择处理数量
    print(f"\n🎯 选择处理模式:")
    options = {
        1: ("快速演示", 5),
        2: ("小规模测试", 20), 
        3: ("中等规模", 50),
        4: ("大规模测试", 100),
        5: ("全部事件", len(event_ids))
    }
    
    for key, (desc, count) in options.items():
        print(f"   {key}) {desc} ({count}个事件)")
    
    try:
        choice = input("\n请选择 (1-5): ").strip()
        choice = int(choice) if choice.isdigit() else 1
        
        if choice in options:
            desc, count = options[choice]
            test_events = event_ids[:count]
        else:
            print("⚠️ 无效选择，使用默认快速演示模式")
            test_events = event_ids[:5]
            desc = "快速演示"
    except:
        test_events = event_ids[:5]
        desc = "快速演示"
    
    print(f"\n🚀 开始 {desc} (处理 {len(test_events)} 个事件)")
    print("=" * 50)
    
    # 输出目录
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    output_dir = script_dir / f"map_validation_results_{timestamp}"
    
    try:
        start_time = time.time()
        
        # 创建处理器
        print("🔧 初始化MAP处理器...")
        processor = UltraFastMAPProcessor(str(config_file), str(model_file))
        
        # 运行定位
        print("\n📍 开始MAP地震重定位...")
        results = processor.run_ultra_fast_map(
            event_ids=test_events,
            output_dir=str(output_dir)
        )
        
        total_time = time.time() - start_time
        
        print(f"\n🎉 处理完成！")
        print(f"📊 最终统计:")
        print(f"   - 处理事件数: {results.get('total_events', 0)}")
        print(f"   - 最终RMS残差: {results.get('final_rms_residual', 0):.3f} 秒")
        print(f"   - 最终MAD残差: {results.get('final_mad_residual', 0):.3f} 秒")
        print(f"   - 总处理时间: {total_time:.1f} 秒")
        print(f"   - 平均每事件: {total_time/len(test_events):.1f} 秒")
        
        # 检查输出文件
        print(f"\n📁 输出文件 (保存在: {output_dir}):")
        output_files = [
            ("地震目录", "relocated_catalog.csv"),
            ("震相文件", "relocated_phases.csv"),
            ("定位残差", "map_residuals.csv"),
            ("走时验证", "travel_time_validation.csv")
        ]
        
        for desc, filename in output_files:
            filepath = output_dir / filename
            if filepath.exists():
                size_mb = filepath.stat().st_size / 1024 / 1024
                print(f"   ✅ {desc}: {filename} ({size_mb:.2f} MB)")
            else:
                print(f"   ❌ {desc}: {filename} (未生成)")
        
        # 验证结果分析
        validation_file = output_dir / "travel_time_validation.csv"
        if validation_file.exists():
            print(f"\n🔬 走时验证分析:")
            validation_df = pd.read_csv(validation_file)
            
            if len(validation_df) > 0:
                successful = validation_df[validation_df['validation_status'] == 'success']
                success_rate = len(successful) / len(validation_df) * 100
                
                print(f"   - 验证记录数: {len(validation_df)}")
                print(f"   - 验证成功率: {success_rate:.1f}%")
                
                if len(successful) > 0:
                    time_diffs = successful['time_difference']
                    rel_errors = successful['relative_error']
                    
                    print(f"   - 平均时差: {time_diffs.mean():.3f} ± {time_diffs.std():.3f} 秒")
                    print(f"   - 时差范围: [{time_diffs.min():.3f}, {time_diffs.max():.3f}] 秒")
                    print(f"   - 平均相对误差: {rel_errors.mean():.1f} ± {rel_errors.std():.1f}%")
                    
                    # 按震相类型统计
                    for phase_type in ['P', 'S']:
                        phase_data = successful[successful['phase_type'] == phase_type]
                        if len(phase_data) > 0:
                            phase_time_diff = phase_data['time_difference'].mean()
                            phase_rel_error = phase_data['relative_error'].mean()
                            print(f"   - {phase_type}波: {len(phase_data)}个, "
                                  f"时差={phase_time_diff:.3f}s, "
                                  f"相对误差={phase_rel_error:.1f}%")
        
        print(f"\n✨ 示例运行成功完成！")
        print(f"📂 详细结果请查看: {output_dir}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 运行出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_help():
    """显示帮助信息"""
    print("📖 TVF MAP地震重定位程序使用指南")
    print("=" * 50)
    print()
    print("🎯 程序功能:")
    print("   - 基于EikoNet神经网络的快速地震重定位")
    print("   - TauP理论走时验证")
    print("   - 详细的统计分析报告")
    print()
    print("📁 必需文件:")
    print("   - tvf_map_config.json (配置文件)")
    print("   - tvf_eikonet_model.pth (神经网络模型)")
    print("   - 2011_phase.csv (震相数据)")
    print("   - hyposvi_station.csv (台站信息)")
    print("   - 2011_pre_cat.csv (地震目录)")
    print()
    print("🚀 运行方式:")
    print("   python example_usage.py")
    print()
    print("📊 输出文件:")
    print("   - relocated_catalog.csv (重定位地震目录)")
    print("   - relocated_phases.csv (重新计算的震相)")
    print("   - map_residuals.csv (定位残差)")
    print("   - travel_time_validation.csv (走时验证结果)")
    print()
    print("🔬 验证指标:")
    print("   - 时差 < 1秒: 验证通过")
    print("   - 相对误差 < 10%: 良好")
    print("   - 相对误差 < 5%: 优秀")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] in ['-h', '--help', 'help']:
        show_help()
    else:
        run_example_with_validation()
