"""
Configuration management utilities.

This module provides utilities for loading, validating, and managing
configuration files for the PyTorch HypoSVI system.
"""

import json
import yaml
from pathlib import Path
from typing import Dict, Any, Union, Optional
import copy


class Config:
    """
    Configuration manager for PyTorch HypoSVI.
    
    Handles loading, validation, and access to configuration parameters
    from JSON or YAML files.
    
    Args:
        config_file: Path to configuration file
        config_dict: Configuration dictionary (alternative to file)
    """
    
    def __init__(
        self, 
        config_file: Optional[Union[str, Path]] = None,
        config_dict: Optional[Dict[str, Any]] = None
    ):
        if config_file is not None:
            self.config = load_config(config_file)
            self.config_file = Path(config_file)
        elif config_dict is not None:
            self.config = copy.deepcopy(config_dict)
            self.config_file = None
        else:
            raise ValueError("Either config_file or config_dict must be provided")
            
        # Validate configuration
        self._validate_config()
        
    def _validate_config(self):
        """Validate configuration parameters."""
        required_sections = ['data', 'inversion']
        
        for section in required_sections:
            if section not in self.config:
                raise ValueError(f"Missing required configuration section: {section}")
                
        # Validate data section
        data_config = self.config['data']
        required_data_keys = ['coordinate_bounds', 'scale']
        
        for key in required_data_keys:
            if key not in data_config:
                raise ValueError(f"Missing required data configuration: {key}")
                
        # Validate coordinate bounds
        bounds = data_config['coordinate_bounds']
        required_bounds = ['lon_min', 'lat_min', 'z_min', 'z_max']
        
        for bound in required_bounds:
            if bound not in bounds:
                raise ValueError(f"Missing coordinate bound: {bound}")
                
        # Validate inversion section
        inversion_config = self.config['inversion']
        required_inversion_keys = ['method']
        
        for key in required_inversion_keys:
            if key not in inversion_config:
                raise ValueError(f"Missing required inversion configuration: {key}")
                
        # Validate method
        valid_methods = ['MAP', 'SVI']
        if inversion_config['method'].upper() not in valid_methods:
            raise ValueError(f"Invalid inversion method: {inversion_config['method']}")
            
    def get(self, key: str, default: Any = None) -> Any:
        """
        Get configuration value using dot notation.
        
        Args:
            key: Configuration key (e.g., 'data.scale')
            default: Default value if key not found
            
        Returns:
            Configuration value
        """
        keys = key.split('.')
        value = self.config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
            
    def set(self, key: str, value: Any) -> None:
        """
        Set configuration value using dot notation.
        
        Args:
            key: Configuration key (e.g., 'data.scale')
            value: Value to set
        """
        keys = key.split('.')
        config = self.config
        
        # Navigate to parent dictionary
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
            
        # Set value
        config[keys[-1]] = value
        
    def update(self, updates: Dict[str, Any]) -> None:
        """
        Update configuration with new values.
        
        Args:
            updates: Dictionary of updates
        """
        def deep_update(base_dict, update_dict):
            for key, value in update_dict.items():
                if isinstance(value, dict) and key in base_dict and isinstance(base_dict[key], dict):
                    deep_update(base_dict[key], value)
                else:
                    base_dict[key] = value
                    
        deep_update(self.config, updates)
        
    def save(self, filepath: Optional[Union[str, Path]] = None) -> None:
        """
        Save configuration to file.
        
        Args:
            filepath: Path to save file (uses original path if None)
        """
        if filepath is None:
            if self.config_file is None:
                raise ValueError("No filepath provided and no original config file")
            filepath = self.config_file
        else:
            filepath = Path(filepath)
            
        save_config(self.config, filepath)
        
    def copy(self) -> 'Config':
        """
        Create a copy of the configuration.
        
        Returns:
            New Config instance with copied data
        """
        return Config(config_dict=self.config)
        
    def to_dict(self) -> Dict[str, Any]:
        """
        Get configuration as dictionary.
        
        Returns:
            Configuration dictionary
        """
        return copy.deepcopy(self.config)
        
    def __getitem__(self, key: str) -> Any:
        """Allow dictionary-style access."""
        return self.config[key]
        
    def __setitem__(self, key: str, value: Any) -> None:
        """Allow dictionary-style setting."""
        self.config[key] = value
        
    def __contains__(self, key: str) -> bool:
        """Allow 'in' operator."""
        return key in self.config


def load_config(filepath: Union[str, Path]) -> Dict[str, Any]:
    """
    Load configuration from JSON or YAML file.
    
    Args:
        filepath: Path to configuration file
        
    Returns:
        Configuration dictionary
    """
    filepath = Path(filepath)
    
    if not filepath.exists():
        raise FileNotFoundError(f"Configuration file not found: {filepath}")
        
    with open(filepath, 'r') as f:
        if filepath.suffix.lower() in ['.yaml', '.yml']:
            try:
                import yaml
                config = yaml.safe_load(f)
            except ImportError:
                raise ImportError("PyYAML is required to load YAML configuration files")
        elif filepath.suffix.lower() == '.json':
            config = json.load(f)
        else:
            # Try JSON first, then YAML
            content = f.read()
            try:
                config = json.loads(content)
            except json.JSONDecodeError:
                try:
                    import yaml
                    config = yaml.safe_load(content)
                except ImportError:
                    raise ValueError(f"Unknown configuration file format: {filepath.suffix}")
                    
    return config


def save_config(config: Dict[str, Any], filepath: Union[str, Path]) -> None:
    """
    Save configuration to JSON or YAML file.
    
    Args:
        config: Configuration dictionary
        filepath: Path to save file
    """
    filepath = Path(filepath)
    
    # Create directory if it doesn't exist
    filepath.parent.mkdir(parents=True, exist_ok=True)
    
    with open(filepath, 'w') as f:
        if filepath.suffix.lower() in ['.yaml', '.yml']:
            try:
                import yaml
                yaml.dump(config, f, default_flow_style=False, indent=2)
            except ImportError:
                raise ImportError("PyYAML is required to save YAML configuration files")
        else:
            # Default to JSON
            json.dump(config, f, indent=2)


def create_default_config() -> Dict[str, Any]:
    """
    Create default configuration dictionary.
    
    Returns:
        Default configuration
    """
    return {
        "data": {
            "coordinate_bounds": {
                "lon_min": -117.5,
                "lat_min": 33.0,
                "z_min": -3.0,
                "z_max": 70.0
            },
            "scale": 200.0
        },
        "model": {
            "eikonet_model_file": "./model.pth",
            "prevent_airquakes": True
        },
        "inversion": {
            "method": "MAP",
            "n_epochs": 2000,
            "learning_rate": 1e-5,
            "iter_tol": 1e-2,
            "svi_iter_tol": 1e-3,
            "n_particles": 101,
            "lr_decay_interval": 100,
            "optimizer": "adam"
        },
        "uncertainty": {
            "pick_unc_p": 0.10,
            "pick_unc_s": 0.10,
            "n_phase_min_pick_std": 15,
            "likelihood_fn": "huber",
            "barron_alpha": 1.5
        },
        "prior": {
            "prior_z_mean": 5.5,
            "prior_z_std": 3.0,
            "prior_scale_param": 2.0,
            "max_src_rec_dist": 75.0
        },
        "ssst": {
            "n_ssst_iter": 3,
            "ssst_mode": "knn",
            "outlier_ndev": 3.0,
            "n_det": 8,
            "knn_k": 10,
            "knn_max_dist": 50.0
        },
        "svi": {
            "kernel_fn": "RBF",
            "svi_verbose": 1,
            "plot_particles": False
        },
        "device": {
            "use_cuda": True,
            "cuda_device": 0
        },
        "logging": {
            "log_level": "INFO",
            "log_file": "hyposvi_location.log",
            "verbose": True,
            "show_progress": True
        },
        "output": {
            "save_residuals": True,
            "save_uncertainties": True,
            "output_format": "csv"
        }
    }


def validate_config_file(filepath: Union[str, Path]) -> bool:
    """
    Validate configuration file.
    
    Args:
        filepath: Path to configuration file
        
    Returns:
        True if valid, False otherwise
    """
    try:
        Config(config_file=filepath)
        return True
    except Exception as e:
        print(f"Configuration validation failed: {e}")
        return False
