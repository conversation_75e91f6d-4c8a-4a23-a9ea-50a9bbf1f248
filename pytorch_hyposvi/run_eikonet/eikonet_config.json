{"data": {"station_file": "../../hyposvi/station.csv", "velmod_file": "../../hyposvi/vel.csv", "coordinate_bounds": {"lon_min": -118.2, "lon_max": -117.1, "lat_min": 35.3, "lat_max": 36.4, "z_min": 0.0, "z_max": 25.0}, "scale": 200.0}, "training": {"batch_size": 256, "n_train": 50000, "n_test": 10000, "n_epochs": 100, "lr": 0.001, "lr_scheduler": {"type": "StepLR", "step_size": 25, "gamma": 0.5}, "early_stopping": {"patience": 20, "min_delta": 1e-06}, "checkpoint_interval": 10}, "model": {"architecture": {"hidden_dims": [16, 32, 16, 32, 16, 32, 16, 32, 16, 32, 16, 32, 16], "activation": "elu", "use_skip_connections": true}, "initialization": {"method": "xavier_uniform", "output_bias": 1.0}}, "loss": {"type": "eikonal", "l1_weight": 0.95, "l2_weight": 0.05, "epsilon": 1e-08}, "optimizer": {"type": "<PERSON>", "lr": 0.001, "weight_decay": 1e-05, "betas": [0.9, 0.999]}, "device": {"use_cuda": true, "cuda_device": 0}, "logging": {"verbose": true, "log_interval": 10, "plot_interval": 10, "save_plots": true}, "output": {"model_save_path": "eikonet_model.pth", "results_save_path": "training_results.json", "plot_dir": "plots"}}