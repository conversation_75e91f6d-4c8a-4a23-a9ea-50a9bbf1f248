#!/usr/bin/env python3
"""
TVF EikoNet训练运行脚本

提供简单的命令行接口来运行不同的训练模式。
"""

import argparse
import subprocess
import sys
import os
from pathlib import Path


def run_simple_training():
    """运行简化训练"""
    print("=" * 60)
    print("运行TVF EikoNet简化训练...")
    print("=" * 60)
    
    try:
        result = subprocess.run([sys.executable, "train_tvf_eikonet_simple.py"], 
                              check=True, capture_output=False)
        print("\n简化训练完成!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"\n简化训练失败: {e}")
        return False


def run_full_training():
    """运行完整训练"""
    print("=" * 60)
    print("运行TVF EikoNet完整训练...")
    print("=" * 60)
    
    try:
        result = subprocess.run([sys.executable, "train_tvf_eikonet.py"], 
                              check=True, capture_output=False)
        print("\n完整训练完成!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"\n完整训练失败: {e}")
        return False


def check_environment():
    """检查环境和依赖"""
    print("检查环境...")
    
    # 检查Python版本
    print(f"Python版本: {sys.version}")
    
    # 检查必要的包
    required_packages = ['torch', 'numpy', 'matplotlib', 'pandas', 'scipy']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ {package} 已安装")
        except ImportError:
            missing_packages.append(package)
            print(f"✗ {package} 未安装")
    
    if missing_packages:
        print(f"\n缺少以下包: {', '.join(missing_packages)}")
        print("请使用以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    # 检查CUDA
    try:
        import torch
        if torch.cuda.is_available():
            print(f"✓ CUDA 可用 (设备: {torch.cuda.get_device_name()})")
        else:
            print("⚠ CUDA 不可用，将使用CPU训练")
    except:
        print("⚠ 无法检查CUDA状态")
    
    # 检查数据文件
    data_dir = Path("../tvf_hyposvi_data")
    required_files = ["hyposvi_station.csv", "hyposvi_vel.csv"]
    
    for file in required_files:
        file_path = data_dir / file
        if file_path.exists():
            print(f"✓ 数据文件存在: {file}")
        else:
            print(f"✗ 数据文件缺失: {file}")
            return False
    
    # 检查配置文件
    config_file = Path("tvf_eikonet_config.json")
    if config_file.exists():
        print(f"✓ 配置文件存在: {config_file}")
    else:
        print(f"✗ 配置文件缺失: {config_file}")
        return False
    
    print("\n环境检查完成!")
    return True


def show_status():
    """显示训练状态和结果"""
    print("=" * 60)
    print("TVF EikoNet训练状态")
    print("=" * 60)
    
    # 检查简化训练结果
    simple_model = Path("tvf_eikonet_model_simple.pth")
    simple_results = Path("tvf_training_results_simple.json")
    
    if simple_model.exists() and simple_results.exists():
        print("✓ 简化训练已完成")
        print(f"  模型文件: {simple_model}")
        print(f"  结果文件: {simple_results}")
        
        # 读取结果
        try:
            import json
            with open(simple_results, 'r') as f:
                results = json.load(f)
            
            history = results.get('training_history', {})
            print(f"  最佳损失: {history.get('best_loss', 'N/A')}")
            print(f"  训练时间: {results.get('training_time', 'N/A'):.1f}秒")
            print(f"  训练轮次: {history.get('n_epochs_trained', 'N/A')}")
        except:
            print("  (无法读取训练结果)")
    else:
        print("✗ 简化训练未完成")
    
    print()
    
    # 检查完整训练结果
    full_model = Path("tvf_eikonet_model.pth")
    full_results = Path("tvf_training_results.json")
    plots_dir = Path("plots")
    
    if full_model.exists() and full_results.exists():
        print("✓ 完整训练已完成")
        print(f"  模型文件: {full_model}")
        print(f"  结果文件: {full_results}")
        
        if plots_dir.exists():
            plot_files = list(plots_dir.glob("velocity_comparison_epoch_*.png"))
            print(f"  速度对比图: {len(plot_files)} 个文件")
        
        # 读取结果
        try:
            import json
            with open(full_results, 'r') as f:
                results = json.load(f)
            
            history = results.get('training_history', {})
            print(f"  最佳损失: {history.get('best_loss', 'N/A')}")
            print(f"  训练时间: {history.get('total_time', 'N/A'):.1f}秒")
            print(f"  训练轮次: {history.get('n_epochs_trained', 'N/A')}")
        except:
            print("  (无法读取训练结果)")
    else:
        print("✗ 完整训练未完成")


def main():
    parser = argparse.ArgumentParser(description="TVF EikoNet训练运行脚本")
    parser.add_argument("mode", choices=["simple", "full", "check", "status"], 
                       help="运行模式: simple(简化训练), full(完整训练), check(环境检查), status(查看状态)")
    
    args = parser.parse_args()
    
    # 切换到脚本目录
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    if args.mode == "check":
        success = check_environment()
        sys.exit(0 if success else 1)
    
    elif args.mode == "status":
        show_status()
        sys.exit(0)
    
    elif args.mode == "simple":
        # 先检查环境
        if not check_environment():
            print("\n环境检查失败，请先解决上述问题。")
            sys.exit(1)
        
        print()
        success = run_simple_training()
        sys.exit(0 if success else 1)
    
    elif args.mode == "full":
        # 先检查环境
        if not check_environment():
            print("\n环境检查失败，请先解决上述问题。")
            sys.exit(1)
        
        print()
        success = run_full_training()
        sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
