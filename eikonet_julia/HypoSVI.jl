module HypoSVI
__precompile__

using DataFrames
using CSV
using JSON
using Lux
using Geodesy
using JLD2
using Dates
using StatsBase
using StatsPlots
using Zygote
using ForwardDiff
using Random
using Optimisers
using Combinatorics
using LinearAlgebra
using Distributions
using NearestNeighbors
using Distributed
using ProgressMeter
using Optim
# using Optimization
using LineSearches
# using CovarianceEstimation
using Dates
using KernelDensity
# using Turing

include("./Eikonet.jl")
using .Eikonet
include("./SVIExtras.jl")

abstract type InversionMethod end
abstract type MAP <: InversionMethod end
abstract type SVI <: InversionMethod end

struct SVIParams <: SVI
    plot_π::Bool
    evid::Int
end

struct MAPParams <: MAP end

function init_eikonet(params)

    τ = Lux.Chain(
        CylindricalSymmetry(6+7, 3+1),
        Dense(4, 16, Lux.elu),
    	SkipConnection(Chain(Dense(16, 32, Lux.elu), <PERSON>se(32, 16, Lux.elu)), +),
    	SkipConnection(Chain(Dense(16, 32, Lux.elu), Dense(32, 16, Lux.elu)), +),
    	SkipConnection(Chain(Dense(16, 32, Lux.elu), Dense(32, 16, Lux.elu)), +),
        SkipConnection(Chain(Dense(16, 32, Lux.elu), Dense(32, 16, Lux.elu)), +),
        SkipConnection(Chain(Dense(16, 32, Lux.elu), Dense(32, 16, Lux.elu)), +),
        SkipConnection(Chain(Dense(16, 32, Lux.elu), Dense(32, 16, Lux.elu)), +),
    	Dense(16, 1, Lux.relu))

    return EikoNet(τ, Float32(params["scale"]))
end

function timedelta(t1::DateTime, t2::DateTime)
    # returns total seconds between t1,t2
    (t1-t2) / Millisecond(1000)
end

function format_arrivals(params::Dict, phases::DataFrame, stations::DataFrame)
    phase_sta = innerjoin(phases, stations, on = [:network, :station])
    X_obs = zeros(Float32, 4, size(phase_sta, 1))
    X_obs[1,:] .= phase_sta.X
    X_obs[2,:] .= phase_sta.Y
    X_obs[3,:] .= phase_sta.Z
    arrival_times = DateTime.(phase_sta[!, "time"])
    T_obs = zeros(Float32, length(arrival_times))
    for (i, row) in enumerate(eachrow(phase_sta))
        if row.phase == "P"
            X_obs[4,i] = 0f0
        elseif row.phase == "S"
            X_obs[4,i] = 1f0
        else
            println("Error: unknown Phase label (not P or S). Exiting...")
            println(row)
        end
        T_obs[i] = timedelta(arrival_times[i], minimum(arrival_times))
    end
    T_ref = minimum(arrival_times)
    return X_obs, T_obs, T_ref, phase_sta
end

function logit(p::Float32)
    log(p / (1f0-p))
end

function sigmoid(x::Float32)
    1f0 / (1f0 + exp(-x))
end

function tukey(x::Real, c::T) where {T}
    if abs(x) <= c
        return (c^2 / T(6)) * (T(1) - (T(1) - (x/c)^2)^3)
    else
        return (c^2 / T(6))
    end
end

function tukey_loss(y::AbstractArray, c::Float32)
    return mean(map(x->tukey(x, c), y))
end

function huber_loss(x::Real, δ::Float32)
    if abs(x) < δ
        ρ = 5.0f-1 * x^2
    else
        ρ = δ * abs(x) - 5.0f-1 * δ^2
    end
    return ρ
end

function barron_loss(x::AbstractArray, c::Float32, α::Float32)
    map(y->barron_loss(y, c, α), x)
end

function barron_loss(x::Real, c::Float32, α::Float32)
    return abs(α - 2f0) * (((x/c)^2 / abs(α - 2f0) + 1f0)^(α/2f0) - 1f0) / α
end

function huber_loss(x::AbstractArray, δ::Float32)
    map(y->huber_loss(y, δ), x)
end

function cauchy_loss(x::Real, c::Float32)
    return 5f-1 * c^2 * log(1f0 + (x/c)^2)
end

function cauchy_loss(x::AbstractArray, δ::Float32)
    mean(map(y->cauchy_loss(y, δ), x))
end

function locate(params, X_obs::Array{T}, T_obs::Array{T}, eikonet::EikoNet, ps::NamedTuple, st::NamedTuple, ::MAP; return_input=false, normed_ll=true, σ=nothing) where {T}

    scale = Float32(params["scale"])
    θ̂ = Float32.([0.5, 0.5, Float32(params["prior_z_mean"])/scale])
    iter_tol = Float32(params["iter_tol"]) / scale

    if isnothing(σ)
        σ = Float32.(map(x -> x <= 5f-1 ? Float32(params["pick_unc_p"]) : Float32(params["pick_unc_s"]), X_obs[4,:]))
    else
        σ = Float32.(map(x -> x <= 5f-1 ? σ[1] : σ[2], X_obs[4,:]))
    end
    log_pdf = assign_likelihood(params)

    prior = PGeneralizedGaussian(0f0, 1f0, params["prior_scale_param"])
    prior_μ = Vector{Float32}([0.5, 0.5, params["prior_z_mean"] / scale])
    prior_Σ = Vector{Float32}([0.5, 0.5, params["prior_z_std"] / scale])
    weights = Float32.(map(x -> x <= 5f-1 ? 1.73 : 1, X_obs[4,:]))
    max_src_rec_dist = Float32(params["max_src_rec_dist"]) / scale

    # function ℓπ(θ::AbstractArray)
    #     X_in = cat(repeat(θ, 1, size(X_obs, 2)), X_obs, dims=1)
    #     T_pred = dropdims(eikonet(X_in, ps, st), dims=1)
    #     bias = mean(vec(T_obs) - T_pred)
    #     distances = sqrt.((X_in[1,:]-X_in[4,:]).^2 + (X_in[2,:]-X_in[5,:]).^2 + (X_in[3,:]-X_in[6,:]).^2)
    #     log_L = -sum(log_pdf.((T_obs .- T_pred .- bias) ./ σ))
    #     if normed_ll
    #         log_L /= length(T_obs)
    #     end
    #     log_p = -sum(logpdf.(prior, (θ - prior_μ) ./ prior_Σ))
    #     return log_L + log_p
    # end

    ipairs = collect(combinations(collect(1:size(X_obs, 2)), 2))
    ipairs = permutedims(hcat(ipairs...))
    ΔT_obs = T_obs[ipairs[:,1]] - T_obs[ipairs[:,2]]
    σ = sqrt.(σ[ipairs[:,1]].^2 + σ[ipairs[:,2]].^2)

    function ℓπ(θ::AbstractArray)
        X_in = cat(repeat(θ, 1, size(X_obs, 2)), X_obs, dims=1)
        T_pred = dropdims(eikonet(X_in, ps, st), dims=1)
        ΔT_pred = T_pred[ipairs[:,1]] - T_pred[ipairs[:,2]]
        log_L = -sum(log_pdf.((ΔT_obs - ΔT_pred) ./ σ))
        if normed_ll
            log_L /= length(ΔT_obs)
        end
        log_p = -sum(logpdf.(prior, (θ - prior_μ) ./ prior_Σ))
        return log_L + log_p
    end

    options = Optim.Options(iterations=params["n_epochs"], g_tol=0f0, f_tol=0f0, x_tol=iter_tol, allow_f_increases=false)
    # result = optimize(ℓπ, θ̂, BFGS(linesearch = LineSearches.BackTracking()), options, autodiff = :forward)
    result = optimize(ℓπ, θ̂, NewtonTrustRegion(), options, autodiff = :forward)
    X_best = vec(Optim.minimizer(result))

    # # if ~Optim.converged(result)
    # #     println(result)
    # # end

    X_in = cat(repeat(X_best, 1, size(X_obs, 2)), X_obs, dims=1)
    T_src, resid = get_origin_time(X_in, eikonet, T_obs, ps, st)

    min_lla = LLA(lat=params["lat_min"], lon=params["lon_min"], alt=0.0)
    trans = ENUfromLLA(min_lla, wgs84)
    inv_trans = LLAfromENU(min_lla, wgs84)
    X_input = copy(X_best)
    X_best .*= 1f3 .* scale
    X_best = Float64.(X_best)
    X_lla = inv_trans(ENU(X_best[1], X_best[2]))
    X_src = Dict("longitude" => X_lla.lon, "latitude" => X_lla.lat, "depth" => X_best[3], "time" => sec2date(T_src))
    if ~return_input
        return X_src, resid
    else
        return X_src, resid, X_input
    end
end

struct HuberDensity{T}
    δ::T
    ε::T
end

function HuberDensity(δ::Float32)
    # source: https://stats.stackexchange.com/questions/210413/generating-random-samples-from-huber-density
    y = 2f0 * pdf(Normal(0f0, 1f0), δ) / δ - 2f0 * cdf(Normal(0f0, 1f0), -δ)
    ε = y / (1f0 + y)
    return HuberDensity(δ, ε)
end

function pdf(dist::HuberDensity, x::T) where T
    if abs(x) < dist.δ
        ρ = 5.0f-1 * x^2
    else
        ρ = dist.δ * abs(x) - 5.0f-1 * dist.δ^2
    end
    return (1f0-dist.ε)/sqrt(2f0 * T(π)) * exp(-ρ)
end

function log_prob(dist::HuberDensity, x::T) where T
    if abs(x) < dist.δ
        ρ = 5.0f-1 * x^2
    else
        ρ = dist.δ * abs(x) - 5.0f-1 * dist.δ^2
    end
    return log((1f0-dist.ε)/sqrt(2f0 * T(π))) - ρ
end

function log_prob(dist::HuberDensity, x::AbstractArray)
    map(x->log_prob(dist, x), x)
end

function RBF_kernel(X::AbstractArray{Float32})
    # This is a specific algorithm for computing pairwise distances fast
    n = size(X, 2)
    G = X' * X
    d² = diag(G) .+ diag(G)' .- 2f0 .* G 
    h = median(d²) / (2f0 * log(n+1))
    γ = 1f0 / (1f-8 + 2 * h)
    K = exp.(-γ * d²)
    return K
end

function RBF_kernel(X::AbstractArray, h::Float32; return_dists=false)
    # This is a specific algorithm for fast computation of pairwise distances
    n = size(X, 2)
    G = X' * X
    d² = diag(G) .+ diag(G)' .- 2f0 .* G 
    γ = 1f0 / (1f-8 + 2f0 * h^2)
    K = exp.(-γ * d²)
    if ~return_dists
        return K
    else
        return K, d²
    end
end

function IMQ_kernel(X::AbstractArray{T}, h::T, β::T) where {T}
    G = X * X'
    d² = diag(G) .+ diag(G)' .- T(2.0) .* G
    K = (h^2 .+ d²).^β
    return K
end

# function RBF_kernel(X::AbstractArray{Float32}, C::AbstractArray, h::Float32)
#     # This is a specific algorithm for fast computation of pairwise distances
#     G = X' * X
#     d² = diag(G) .+ diag(G)' .- 2f0 .* G 
#     γ = 1f0 / (1f-8 + 2f0 * h^2)
#     K = exp.(-γ * d²) .* C
#     return K
# end

function median_bw_heuristic(X::AbstractArray)
    n = size(X, 2)
    G = X' * X
    d² = diag(G) .+ diag(G)' .- 2f0 .* G 
    h² = median(d²) / (2f0 * log(n+1f0))
    return sqrt(h²)
end

function get_origin_time(X::AbstractArray{Float32}, eikonet::EikoNet, T_obs::AbstractArray{Float32}, ps::NamedTuple, st::NamedTuple)
    # # Then determine origin time given hypocenter
    T_pred = dropdims(eikonet(X, ps, st), dims=1)
    T_obs = reshape(T_obs, :, 1)
    T_obs = repeat(T_obs, 1, size(X, 3))
    resid = T_obs - T_pred
    origin_offset = dropdims(mean(resid, dims=1), dims=1)
    i_best = argmin(abs.(origin_offset))
    return origin_offset[i_best], resid[:,i_best] .- origin_offset[i_best]
end

function assign_kernel(params)
    if ~haskey(params, "kernel_fn")
        return (x, h) -> RBF_kernel(x, h)
    elseif uppercase.(params["kernel_fn"]) == "RBF"
        return (x, h) -> RBF_kernel(x, h)
    elseif uppercase.(params["kernel_fn"]) == "IMQ"
        return (x, h) -> IMQ_kernel(x, h, Float32(-0.5))
    end
end

function assign_likelihood(params)
    if lowercase.(params["likelihood_fn"]) == "laplace"
        return x -> logpdf(Laplace(0f0, 1f0), x)
    elseif lowercase.(params["likelihood_fn"]) == "cauchy"
        return x -> logpdf(Cauchy(0f0, 1f0), x)
    elseif lowercase.(params["likelihood_fn"]) == "gaussian"
        return x -> logpdf(Normal(0f0, 1f0), x)
    elseif lowercase.(params["likelihood_fn"]) == "huber"
        return x -> -huber_loss(x, 1f0)
    elseif lowercase.(params["likelihood_fn"]) == "barron"
        return x -> -barron_loss(x, 1f0, Float32(params["barron_alpha"]))
    end
end

function locate(params, X_obs::Array{T}, T_obs::Array{T}, eikonet::EikoNet, ps::NamedTuple, st::NamedTuple, other_params::SVI; σ=nothing) where {T}
    # SVI backend for computing the posterior
    # This version of the SVI functions does not use differential times.
    # Note: the origin time is subtracted out beforehand
    X_src, resid, X_input = locate(params, X_obs, T_obs, eikonet, ps, st, MAPParams(), return_input=true)

    scale = Float32(params["scale"])
    N = params["n_particles"]
    M = size(X_obs, 2)
    η = Float32(params["lr"])
    iter_tol = Float32(params["svi_iter_tol"])/scale

    log_pdf = assign_likelihood(params)

    prior = PGeneralizedGaussian(0f0, 1f0, params["prior_scale_param"])
    prior_μ = Vector{Float32}([0.5, 0.5, params["prior_z_mean"] / scale])
    prior_Σ = Vector{Float32}([0.5, 0.5, params["prior_z_std"] / scale])

    θ̂ = repeat(X_input, 1, 1, N)
    θ̂ = θ̂ + randn(Float32, 3, 1, N) .* 5f0 / scale
    # θ̂ = zeros(Float32, 3, 1, N)
    # θ̂[1:2,:,:] .= 5f-1
    # θ̂ = zeros(Float32, 3, 1, N)
    # θ̂ = repeat(prior_μ, 1, 1, N)
    # θ̂[1:2,:,:] = θ̂[1:2,:,:] + randn(Float32, 2, 1, N) .* 5f0 / scale

    if isnothing(σ)
        σ = Float32.(map(x -> x <= 5f-1 ? Float32(params["pick_unc_p"]) : Float32(params["pick_unc_s"]), X_obs[4,:]))
    else
        σ = Float32.(map(x -> x <= 5f-1 ? σ[1] : σ[2], X_obs[4,:]))
    end

    ipairs = collect(combinations(collect(1:size(X_obs, 2)), 2))
    if length(ipairs) >= 500
        ipairs = ipairs[1:500]
    end
    ipairs = permutedims(hcat(ipairs...))

    X_obs = repeat(reshape(X_obs, 4, M, 1), 1, 1, N)
    T_obs = repeat(reshape(T_obs, :, 1), 1, N)

    state = Optimisers.setup(Optimisers.Adam(η), θ̂)
    
    # function ℓπ(θ::AbstractArray)
    #     X_in = cat(repeat(θ, 1, M, 1), X_obs, dims=1)
    #     T_pred = eikonet(X_in, ps, st)[1,:,:]
    #     T_bias = mean(T_obs - T_pred)
    #     log_L = sum(log_pdf.((T_obs - T_pred .- T_bias) ./ σ))
    #     log_p = sum(logpdf.(prior, (θ .- prior_μ) ./ prior_Σ))
    #     return log_L + log_p
    # end

    ΔT_obs = T_obs[ipairs[:,1],:] - T_obs[ipairs[:,2],:]
    σ = sqrt.(σ[ipairs[:,1]].^2 + σ[ipairs[:,2]].^2)

    function ℓπ(θ::AbstractArray)
        X_in = cat(repeat(θ, 1, size(X_obs, 2)), X_obs, dims=1)
        T_pred = dropdims(eikonet(X_in, ps, st), dims=1)
        ΔT_pred = T_pred[ipairs[:,1],:] - T_pred[ipairs[:,2],:]
        # log_L = sum(log_pdf.((ΔT_obs - ΔT_pred) ./ σ))
        ρ = (ΔT_obs - ΔT_pred) ./ σ
        log_L = Float32(length(ΔT_obs)) * log(sum(exp.(-ρ.^2)))
        log_p = sum(logpdf.(prior, (θ .- prior_μ) ./ prior_Σ))
        return log_L + log_p
    end

    σ = repeat(σ, 1, N)

    function ∇_stein_kernel(θ::AbstractArray)
        ∇ℓπ = Zygote.gradient(ℓπ, θ)[1][:,1,:]'
        h = median_bw_heuristic(θ[:,1,:])
        K = RBF_kernel(θ[:,1,:], h)
        ∇K = Zygote.gradient(x -> sum(RBF_kernel(x, h)), θ[:,1,:])[1]
        ϕ = (K * ∇ℓπ - ∇K')' ./ N
        ϕ = Float32.(-1.0 .* reshape(ϕ, size(ϕ,1), 1, size(ϕ,2)))
        return ϕ
    end

    # for i in 1:params["n_epochs"]
    #     ∇ℓπ = Zygote.gradient(ℓπ, θ̂)[1]
    #     state, θ̂_new = Optimisers.update(state, θ̂, -1f0 .* ∇ℓπ)
    #     Δθ = θ̂_new - θ̂
    #     θ̂ = θ̂_new

    #     if (i % params["lr_decay_interval"]) == 0
    #         η /= 1f1
    #         Optimisers.adjust!(state, η)
    #     end

    #     if norm(Δθ, 2) <= iter_tol
    #         if Int(params["svi_verbose"]) >= 1
    #             println("Converged at epoch $i ", 1000 * scale * norm(Δθ, 2))
    #         end
    #         break
    #     end 

    #     if (i == Int(params["n_epochs"])) && (Int(params["svi_verbose"]) >= 1 )
    #         println("Failed to converge; ", 1000 * scale * norm(Δθ, 2))
    #     end

    #     if Int(params["svi_verbose"]) == 2
    #         println("MAP Epoch $i ", 1000 * scale * norm(Δθ, 2))
    #     end
    # end

    for i in 1:params["n_epochs"]
        ϕ = ∇_stein_kernel(θ̂)
        state, θ̂_new = Optimisers.update(state, θ̂, ϕ)
        Δθ = θ̂_new - θ̂
        θ̂ = θ̂_new

        if (i % params["lr_decay_interval"]) == 0
            η /= 1f1
            Optimisers.adjust!(state, η)
        end

        if norm(Δθ, 2) <= iter_tol
            if Int(params["svi_verbose"]) >= 1
                println("Converged at epoch $i ", 1000 * scale * norm(Δθ, 2))
            end
            break
        end 

        if (i == Int(params["n_epochs"])) && (Int(params["svi_verbose"]) >= 1 )
            println("Failed to converge; ", 1000 * scale * norm(Δθ, 2))
        end

        if Int(params["svi_verbose"]) == 2
            println("SVI Epoch $i ", 1000 * scale * norm(Δθ, 2))
        end
    end

    # Convert X back to meters
    # X_best = θ̂ .* scale
    X_best = dropdims(θ̂, dims=2)

    kde_X = kde(X_best[1,:])#, (0f0, 1f0))
    kde_Y = kde(X_best[2,:])#, (0f0, 1f0))
    kde_Z = kde(X_best[3,:])#, (0f0, 1f0))

    X_best = Vector{Float32}([kde_X.x[argmax(kde_X.density)], kde_Y.x[argmax(kde_Y.density)], kde_Z.x[argmax(kde_Z.density)]])
    X_obs = X_obs[:,:,1]
    T_obs = T_obs[:,1]

    X_in = cat(repeat(X_best, 1, size(X_obs, 2)), X_obs, dims=1)
    T_src, resid = get_origin_time(X_in, eikonet, T_obs, ps, st)

    min_lla = LLA(lat=params["lat_min"], lon=params["lon_min"], alt=0.0)
    trans = ENUfromLLA(min_lla, wgs84)
    inv_trans = LLAfromENU(min_lla, wgs84)
    X_input = copy(X_best)
    X_best .*= 1f3 .* scale
    X_best = Float64.(X_best)
    X_lla = inv_trans(ENU(X_best[1], X_best[2]))
    X_src = Dict("longitude" => X_lla.lon, "latitude" => X_lla.lat, "depth" => X_best[3], "time" => sec2date(T_src))

    # Gaussian approx of posterior uncertainty
    # X_cov = cov(BiweightMidcovariance(), X_best')
    X_best = dropdims(θ̂ .* scale, dims=2)
    X_cov = cov(X_best')
    z_unc = sqrt(X_cov[3,3])
    h_unc = sqrt.(eigvals(X_cov[1:2,1:2]))
    sort!(h_unc)

    if other_params.plot_π
        inv_trans = LLAfromENU(LLA(lat=params["lat_min"], lon=params["lon_min"]), wgs84)
        plot_particles(params, X_best .* 1f3, inv_trans, other_params.evid)
    end
    X_src["h_unc_min"] = h_unc[1]
    X_src["h_unc_max"] = h_unc[2]
    X_src["z_unc"] = z_unc

    return X_src, resid
end

function plot_particles(params, X, inv_trans, evid)
    lats = Vector{Float32}()
    lons = Vector{Float32}()    
    for p in eachslice(X, dims=2)
        hypo = inv_trans(ENU(p[1], p[2], 0f0))
        push!(lats, hypo.lat)
        push!(lons, hypo.lon)
    end
    xlims=(minimum(lons)-0.05, maximum(lons)+0.05)
    ylims=(minimum(lats)-0.05, maximum(lats)+0.05)
    zlims=(-1(maximum(X[3,:]/1f3)-1.0), -1*(minimum(X[3,:]/1f3)+1.0))
    # p1 = scatter(lons, lats, xlabel="Longitude", ylabel="Latitude", xlims=xlims, ylims=ylims, markersize=1.0)
    # p2 = scatter(lons, X[3,:]/1f3, xlabel="Longitude", ylabel="Depth", xlims=xlims, ylims=zlims, yflip=true, markersize=1.0)
    # p3 = scatter(lats, X[3,:]/1f3, xlabel="Latitude", ylabel="Depth", xlims=ylims, ylims=zlims, yflip=true, markersize=1.0)
    # savefig(plot(p1, p2, p3, layout=(3,1), size=(400,800), left_margin = 20Plots.mm), "$(params["plot_dir"])/event_$(evid).png")

    kde1 = marginalkde(lons, lats, xlabel="Longitude", ylabel="Latitude", xlims=xlims, ylims=ylims)
    kde2 = marginalkde(lons, -X[3,:]/1f3, xlabel="Longitude", ylabel="Depth", xlims=xlims, ylims=zlims)
    kde3 = marginalkde(lats, -X[3,:]/1f3, xlabel="Latitude", ylabel="Depth", xlims=ylims, ylims=zlims)

    kde4 = marginalkde(X[1,:]/1f3, X[2,:]/1f3, xlabel="X (km)", ylabel="Y (km)", xlims=xlims, ylims=ylims)
    kde5 = marginalkde(X[1,:]/1f3, -X[3,:]/1f3, xlabel="X (km)", ylabel="Depth (km)", xlims=xlims, ylims=zlims)
    kde6 = marginalkde(X[2,:]/1f3, -X[3,:]/1f3, xlabel="Y (km)", ylabel="Depth (km)", xlims=ylims, ylims=zlims)
    savefig(plot(kde1, kde4, kde2, kde5, kde3, kde6, layout=(3,2), size=(1000,800), left_margin = 1Plots.mm), "$(params["plot_dir"])/event_$(evid).png")

end

function update(params, ssst::DataFrame, row::DataFrameRow, max_dist::Float32, kdtree::KDTree,
                resid::DataFrame, origins::DataFrame)   

    idx = inrange(kdtree, [row.X, row.Y, row.depth], max_dist)
    evid_list = setdiff(Set(origins.evid[idx]), row.evid)
    sub_resid = filter(:evid => in(evid_list), resid)

    local_ssst = Dict()
    gdf = groupby(sub_resid, [:network, :station, :phase])
    for (key, subdf) in pairs(gdf)
        network, station, phase = values(key)
        if nrow(subdf) >= params["k-NN"]
            local_ssst[(network, station, phase)] = median(subdf.residual)
        end
    end

    return local_ssst
end

function compute_ssst(params::Dict, ssst::DataFrame, origins::DataFrame, resid::DataFrame)
    resid_origin = innerjoin(resid, origins, on=:evid)
    resid_origin[!, :id] = collect(1:nrow(resid_origin))
    kNN = Int(params["k-NN"])+1
    @time for gdf in groupby(resid_origin, [:network, :station, :phase])
        if nrow(gdf) < kNN
            continue
        end
        kdtree = KDTree(cat(gdf.X, gdf.Y, gdf.depth, dims=2)')
        idx, dists = knn(kdtree, cat(gdf.X, gdf.Y, gdf.depth, dims=2)', kNN, true)
        for i in 1:length(idx)
            resid_origin[gdf.id[i], :residual] = median(gdf.residual[idx[i][2:end]])
        end
    end
    select!(resid_origin, names(ssst))
    return resid_origin
end

function compute_ssst(params::Dict, ssst::DataFrame, origins::DataFrame, resid::DataFrame, max_dist::Float32)
    kdtree = KDTree(cat(origins.X, origins.Y, origins.depth, dims=2)')

    println("Begin updating SSSTs")
    results = @showprogress @distributed (append!) for row in eachrow(origins)
        local_ssst = update(params, ssst, row, max_dist, kdtree, resid, origins)
        [(row.evid, local_ssst)]
    end

    return results
end

function compute_ssst(params::Dict, ssst::DataFrame, origins::DataFrame, resid::DataFrame, max_dist::Float32)
    println("Begin updating SSSTs")
    resid_origin = innerjoin(resid, origins, on=:evid)
    resid_origin[!, :id] = collect(1:nrow(resid_origin))
    kNN = Int(params["k-NN"])+1

    @time for gdf in groupby(resid_origin, [:network, :station, :phase])
        if nrow(gdf) < kNN
            continue
        end

        kdtree = KDTree(cat(gdf.X, gdf.Y, gdf.depth, dims=2)')

        for (i, row) in enumerate(eachrow(gdf))
            idx = inrange(kdtree, [row.X, row.Y, row.depth], max_dist)
            idx = setdiff(idx, i)
            if length(idx) < kNN
                resid_origin[gdf.id[i], :residual] = 0f0
            else
                resid_origin[gdf.id[i], :residual] = median(gdf.residual[idx])
            end
        end

        # idx = inrange(kdtree, cat(gdf.X, gdf.Y, gdf.depth, dims=2)', max_dist)
        # for i in 1:length(idx)
        #     if length(idx[i]) < kNN
        #         continue
        #     end
        #     resid_origin[gdf.id[i], :residual] = median(gdf.residual[idx[i][2:end]])
        # end
    end
    select!(resid_origin, names(ssst))
    return resid_origin
end

function update_ssst(new_ssst::DataFrame, ssst::DataFrame, origins::DataFrame)
    ssst = leftjoin(ssst, new_ssst, on=[:evid, :network, :station, :phase], makeunique=true)
    for row in eachrow(ssst)
        if ismissing(row.residual_1)
            continue
        end
        row.residual += row.residual_1
    end
    select!(ssst, Not(:residual_1))
    return ssst
end

function update_ssst(new_ssst::Vector, ssst::DataFrame, origins::DataFrame)
    @showprogress for (evid, local_ssst) in new_ssst
        for row in eachrow(ssst[ssst.evid .== evid, :])
            key = (row.network, row.station, row.phase)
            if haskey(local_ssst, key)
                ssst.residual[row.idx] += local_ssst[key]
            end
        end
    end
    return ssst
end

function apply_ssst(phases_old::DataFrame, ssst::DataFrame)
    phases = deepcopy(phases_old)
    sort!(phases, [:evid, :network, :station, :phase])
    sort!(ssst, [:evid, :network, :station, :phase])
    for i in 1:nrow(phases)
        sgn = sign(ssst.residual[i])
        resid = abs(ssst.residual[i])
        sec = Second(floor(resid))
        msec = Millisecond(floor((resid - floor(resid)) * 1000.))
        if sgn >= 0
            phases.time[i] = phases.time[i] - (sec + msec)
        else
            phases.time[i] = phases.time[i] + (sec + msec)
        end
    end
    return phases
end

function init_ssst(phases::DataFrame, resid::DataFrame)
    ssst_dict = Dict()
    for group in groupby(resid, [:network, :station, :phase])
        row = group[1,:]
        ssst_dict[(row.network, row.station, row.phase)] = median(group.residual)
    end

    ssst = DataFrame(evid=phases.evid, network=phases.network, station=phases.station, phase=phases.phase,
                     residual=zeros(Float32, nrow(phases)))

    for row in eachrow(ssst)
        if haskey(ssst_dict, (row.network, row.station, row.phase))
            row.residual = ssst_dict[(row.network, row.station, row.phase)]
        end
    end
    return ssst
end

function init_ssst(phases::DataFrame)
    ssst = DataFrame(evid=phases.evid, network=phases.network, station=phases.station, phase=phases.phase,
                     residual=zeros(Float32, nrow(phases)))
    return ssst
end

function plot_events(origins::DataFrame)
    scatter(origins[!,:longitude], origins[!,:latitude], left_margin = 20Plots.mm)
    savefig("events.png")
end

function logrange(x1, x2, n)
    (10^y for y in range(log10(x1), log10(x2), length=n))
end

function filter_catalog!(params::Dict, origins::DataFrame)
    if haskey(params, "lat_min_filter")
        filter!(x -> (x.latitude >= params["lat_min_filter"]) & (x.latitude < params["lat_max_filter"]), origins)
    end
    if haskey(params, "lon_min_filter")
        filter!(x -> (x.longitude >= params["lon_min_filter"]) & (x.longitude < params["lon_max_filter"]), origins)
    end
    if haskey(params, "depth_min_filter")
        filter!(x -> x.depth >= params["depth_min_filter"], origins)
    end
    if haskey(params, "depth_max_filter")
        filter!(x -> x.depth < params["depth_max_filter"], origins)
    end
end

function remove_outlier_picks(params, phases::DataFrame, origins::DataFrame, residuals::DataFrame, max_resid::Real)
    phase_cols = names(phases)
    phases = innerjoin(phases, residuals, on=[:arid, :evid, :network, :station, :phase])
    filter!(x -> abs(x.residual) <= max_resid, phases)
    good_evids = []
    for group in groupby(phases, :evid)
        if nrow(group) >= params["n_det"]
            push!(good_evids, group.evid[1])
        end
    end
    filter!(:evid => in(Set(good_evids)), phases)
    filter!(:evid => in(Set(good_evids)), origins)
    phases = phases[!, phase_cols]
    return phases, origins
end

function remove_outlier_picks(params, phases::DataFrame, origins::DataFrame, residuals::DataFrame)
    phase_cols = names(phases)
    phases = innerjoin(phases, residuals, on=[:arid, :evid, :network, :station, :phase])
    # mads = combine(groupby(phases, :evid), :residual => mad)
    # phases = innerjoin(phases, mads, on=[:evid])
    phases = subset(groupby(phases, :evid), :residual => x -> abs.(x) .<= Float32(params["outlier_ndev"]) * mad(x))
    good_evids = []
    for group in groupby(phases, :evid)
        if nrow(group) >= params["n_det"]
            push!(good_evids, group.evid[1])
        end
    end
    filter!(:evid => in(Set(good_evids)), phases)
    filter!(:evid => in(Set(good_evids)), origins)
    phases = phases[!, phase_cols]
    return phases, origins
end

function remove_duplicate_picks(params, phases, origins, residuals)
    phase_cols = names(phases)
    phases = innerjoin(phases, residuals, on=[:arid, :evid, :network, :station, :phase])
    phases = combine(sdf -> sdf[argmin(abs.(sdf.residual)), :], groupby(phases, [:evid, :network, :station, :phase]))

    # Remove events with fewer than n_det picks
    good_evids = []
    for group in groupby(phases, :evid)
        if nrow(group) >= params["n_det"]
            push!(good_evids, group.evid[1])
        end
    end
    filter!(:evid => in(Set(good_evids)), phases)
    filter!(:evid => in(Set(good_evids)), origins)
    phases = phases[!, phase_cols]
    return phases, origins
end

function prune_events(params, phases, origins)
    # Remove events with fewer than n_det picks
    good_evids = []
    for group in groupby(phases, :evid)
        if nrow(group) >= params["n_det"]
            push!(good_evids, group.evid[1])
        end
    end
    filter!(:evid => in(Set(good_evids)), phases)
    filter!(:evid => in(Set(good_evids)), origins)
    return phases, origins
end

function read_phases(params, origins)
    phases = CSV.read(params["phase_file"], DataFrame)
    phases.phase = uppercase.(phases.phase)
    unique!(phases)
    filter!(:evid => in(Set(origins.evid)), phases)
    return phases
end

function init_resid_df()
    return DataFrame(arid=Int[], evid=Int[], network=String[], station=String[], phase=String[], residual=Float32[])
end

function init_origin_df(params, evid, X_src, T_src, mag, resid)
    min_lla = LLA(lat=params["lat_min"], lon=params["lon_min"], alt=0.0)
    trans = ENUfromLLA(min_lla, wgs84)
    XX, YY, ZZ = trans(LLA(X_src["latitude"], X_src["longitude"]))
    if ~haskey(X_src, "z_unc")
        origin_df = DataFrame(time=T_src, evid=evid, latitude=X_src["latitude"],
                        longitude=X_src["longitude"], depth=X_src["depth"]/1f3, magnitude=mag, X=XX/1f3, Y=YY/1f3,
                        rmse=std(resid), mae=mean(abs.(resid)))
    else
        origin_df = DataFrame(time=T_src, evid=evid, latitude=X_src["latitude"],
                        longitude=X_src["longitude"], depth=X_src["depth"]/1f3, magnitude=mag, X=XX/1f3, Y=YY/1f3,
                        unc_h_max=X_src["h_unc_max"], unc_h_min=X_src["h_unc_min"],
                        unc_z=X_src["z_unc"], rmse=std(resid), mae=mean(abs.(resid)))
    end
    resid_df = DataFrame(arid=Int[], evid=Int[], network=String[], station=String[], phase=String[], residual=Float32[])
    return origin_df, resid_df
end

function init_origin_df()
    origin_df = DataFrame(time=DateTime[], evid=Int[], latitude=Float32[], longitude=Float32[], depth=Float32[],
                            magnitude=Float32[], rmse=Float32[], mae=Float32[], X=Float32[], Y=Float32[])
    resid_df = DataFrame(arid=Int[], evid=Int[], network=String[], station=String[], phase=String[], residual=Float32[])
    return origin_df, resid_df
end

function init_ssst_radius(params)
    ssst_radius = logrange(Float32(params["min_ssst_radius"]), Float32(params["max_ssst_radius"]), params["n_ssst_iter"])
    ssst_radius = reverse(collect(ssst_radius))
    return ssst_radius
end

function locate_events(params, origins::DataFrame, phases::DataFrame, stations::DataFrame; mode="MAP", outfile=nothing, showprogress=false, σ=nothing)
    @load params["model_file"] ps st

    model = init_eikonet(params)
    println("Begin HypoSVI")

    if !("mag" in names(origins))
        origins[!, "mag"] .= NaN
    end
    results = @showprogress @distributed (append!) for phase_sub in groupby(phases, :evid)
    # results = []
    # for phase_sub in groupby(phases, :evid)
        if mode == "MAP"
            method = MAPParams()
        elseif mode == "SVI"
            method = SVIParams(params["plot_svi_results"], phase_sub.evid[1])
        end
        X_obs, T_obs, T_ref, phase_key = format_arrivals(params, DataFrame(phase_sub), stations)
        X_src, resid = locate(params, X_obs, T_obs, model, ps, st, method, σ=σ)

        mag = filter(row -> row.evid == phase_sub.evid[1], origins).mag[1]

        temp_origin_df, temp_resid_df = init_origin_df(params, phase_sub.evid[1], X_src, T_ref + X_src["time"], mag, resid)
        for (i, row) in enumerate(eachrow(phase_key))
            push!(temp_resid_df, (row.arid, phase_sub.evid[1], row.network, row.station, row.phase, resid[i]))
        end
        # push!(results, (temp_origin_df, temp_resid_df))
        [(temp_origin_df, temp_resid_df)]
    end

    origin_df = vcat([x[1] for x in results]...)
    resid_df = vcat([x[2] for x in results]...)

    if params["verbose"]
        println(first(origin_df, 25))
    end
    if isnothing(outfile)
        CSV.write(params["catalog_outfile"], origin_df)
    else
        CSV.write(outfile, origin_df)
    end

    return origin_df, resid_df
end


function locate_events_svi(pfile; stop=nothing, evid=nothing, showprogress=false, mode="SVI")
    # This function is meant to be used only if you want to skip the MAP steps and go straight to SVI
    params = JSON.parsefile(pfile)
    outfile = params["catalog_outfile"]
    resid_file = params["resid_outfile"]

    origins = CSV.read(params["catalog_infile"], DataFrame)
    if ~isnothing(stop)
        origins = origins[1:stop,:]
    end
    if params["verbose"]
        println("Read in ", nrow(origins), " events")
    end
    filter_catalog!(params, origins)
    if params["verbose"]
        println("After filtering ", nrow(origins), " events remaining")
    end
    phases = read_phases(params, origins)
    select!(phases, [:network, :station, :phase, :time, :evid])
    phases[!, :arid] = collect(1:nrow(phases))

    stations = get_stations(params)
    unique!(stations)

    if ~isnothing(evid)
        filter!(:evid => x -> x == evid, origins)
        filter!(:evid => x -> x == evid, phases)
    end

    origins, residuals = locate_events(params, origins, phases, stations, mode=mode, outfile="$(outfile)_svi", showprogress=showprogress)
    return
end

function mad_resid(df::DataFrame, phase)
    df_sub = filter(:phase => x -> x == phase, df)
    return mad(df_sub.residual)
end

function mad_resid(df::DataFrame, phase, n_phase_min)
    gdf = groupby(df, :evid)
    gdf = filter(:nrow => x -> x >= n_phase_min, DataFrames.transform(gdf, nrow))
    df_sub = filter(:phase => x -> x == phase, gdf)
    return mad(df_sub.residual)
end

function estimate_σ_pick(params, residuals::DataFrame)
    σ = Float32.([mad_resid(residuals, "P", params["n_phase_min_pick_std"]), mad_resid(residuals, "S", params["n_phase_min_pick_std"])])
    return σ
end

function locate_events_ssst(pfile; stop=nothing, evid=nothing, showprogress=false, svi_step=false)
    params = JSON.parsefile(pfile)
    outfile = params["catalog_outfile"]
    resid_file = params["resid_outfile"]
    ssst_file = params["ssst_outfile"]

    origins = CSV.read(params["catalog_infile"], DataFrame)
    if ~isnothing(stop)
        origins = origins[1:stop,:]
    end
    if params["verbose"]
        println("Read in ", nrow(origins), " events")
    end
    filter_catalog!(params, origins)
    if params["verbose"]
        println("After filtering ", nrow(origins), " events remaining")
    end

    phases = read_phases(params, origins)
    select!(phases, [:network, :station, :phase, :time, :evid])
    phases[!, :arid] = collect(1:nrow(phases))

    # Select a single event if desired
    if ~isnothing(evid)
        filter!(:evid => x -> x == evid, origins)
        filter!(:evid => x -> x == evid, phases)
    end

    phases_origins = prune_events(params, phases, origins)

    stations = get_stations(params)
    unique!(stations)
    if ~("mag" in names(origins))
        origins[!, :mag] .= NaN32
        # magnitudes = zeros(Float32, nrow(origins))
        # magnitudes .= NaN32
    end
    magnitudes = origins[!,[:evid, :mag]]

    # Initial locations
    origins, residuals = locate_events(params, origins, phases, stations, mode="MAP", outfile="$(outfile)_iter_a", showprogress=showprogress)
    CSV.write("$(resid_file)_iter_a.csv", residuals)
    origins = innerjoin(origins, magnitudes, on=:evid)
    σ = estimate_σ_pick(params, residuals)
    println("MAD residual initial P: ", σ[1], " S: ", σ[2])

    # Remove duplicates and relocate
    # phases, origins = remove_outlier_picks(params, phases, origins, residuals, 5f-1)
    # phases, origins = remove_duplicate_picks(params, phases, origins, residuals)

    # println("Removing duplicate picks and relocate with new estimate of σ")
    # origins, residuals = locate_events(params, origins, phases, stations, mode="MAP", outfile="$(outfile)_iter_b", showprogress=showprogress)
    # CSV.write("$(resid_file)_iter_b.csv", residuals)
    # origins = innerjoin(origins, magnitudes, on=:evid)
    # σ = estimate_σ_pick(params, residuals)
    # println("MAD residual initial P: ", σ[1], " S: ", σ[2])

    phases0 = deepcopy(phases)
    ssst = init_ssst(phases)

    # Static iterations
    for i in 1:params["n_static_iter"]
        new_ssst = init_ssst(phases, residuals)
        ssst = update_ssst(new_ssst, ssst, origins)
        phases = apply_ssst(phases0, ssst)
        CSV.write("$(ssst_file)_iter_static.csv", ssst)
        CSV.write("$(resid_file)_iter_static.csv", residuals)
        origins, residuals = locate_events(params, origins, phases, stations, mode="MAP", outfile="$(outfile)_iter_static", showprogress=showprogress)
        origins = innerjoin(origins, magnitudes, on=:evid)
        σ = estimate_σ_pick(params, residuals)
        println("MAD residual after static correction $i P: ", σ[1], " S: ", σ[2])
    end

    if lowercase(params["ssst_mode"]) == "shrinking"
        ssst_radius = init_ssst_radius(params)
    end

    # SSST iterations
    for k in 1:params["n_ssst_iter"]
        if lowercase(params["ssst_mode"]) == "knn"
            new_ssst = compute_ssst(params, ssst, origins, residuals)
        elseif lowercase(params["ssst_mode"]) == "shrinking"
            new_ssst = compute_ssst(params, ssst, origins, residuals, ssst_radius[k])
        end
        ssst = update_ssst(new_ssst, ssst, origins)
        phases = apply_ssst(phases0, ssst)
        CSV.write("$(ssst_file)_iter_$(k).csv", ssst)
        CSV.write("$(resid_file)_iter_$(k).csv", residuals)
        origins, residuals = locate_events(params, origins, phases, stations, mode="MAP", outfile="$(outfile)_iter_$(k)", showprogress=showprogress)
        origins = innerjoin(origins, magnitudes, on=:evid)
        σ = estimate_σ_pick(params, residuals)
        println("MAD residual for ssst iter $(k) P: ", σ[1], " S: ", σ[2])
    end

    if svi_step != false
        new_ssst = compute_ssst(params, ssst, origins, residuals)
        ssst = update_ssst(new_ssst, ssst, origins)
        phases = apply_ssst(phases0, ssst)
        origins, residuals = locate_events(params, origins, phases, stations, mode=svi_step, outfile="$(outfile)_svi", showprogress=showprogress)
    end
    return
end

end
