#!/usr/bin/env python3
"""
EikoNet简化训练脚本 - PyTorch版本

支持测试模式（10次）和完整训练模式（100次）
"""

import os
import sys
import json
import torch
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
from datetime import datetime
import argparse

# 添加src路径到Python路径
current_dir = Path(__file__).parent
src_dir = current_dir.parent / "src"
sys.path.insert(0, str(src_dir))

from eikonet.models import EikoNet
from eikonet.trainer import EikoNetTrainer
from eikonet.loss import EikonalLoss
from data.velocity_model import VelocityModel1D, build_1d_training_data
from data.loader import StationDataLoader


def build_improved_training_data(
    velocity_model: VelocityModel1D,
    n_train: int,
    n_test: int,
    coordinate_bounds: dict,
    scale: float = 200.0,
    device: torch.device = torch.device('cpu')
):
    """
    改进的训练数据生成，使用更高效的方法
    """
    n_total = n_train + n_test
    print(f"  生成 {n_total} 个训练样本...")

    # 坐标边界
    z_min = coordinate_bounds['z_min'] / scale
    z_max = coordinate_bounds['z_max'] / scale

    # 按照Julia代码生成随机源坐标
    print("  生成源位置...")
    x_src = torch.rand(3, n_total, device=device)
    # 源深度：均匀分布在整个深度范围内
    x_src[2, :] = torch.rand(n_total, device=device) * (z_max - z_min) + z_min

    # 简化接收器生成（为了提高速度，使用简单的随机生成）
    print("  生成接收器位置...")
    x_rec = torch.rand(3, n_total, device=device)
    x_rec[2, :] = torch.rand(n_total, device=device) * (z_max - z_min) + z_min

    # 生成相位标签（与Julia代码一致：交替P和S波）
    phases = torch.arange(n_total, device=device) % 2
    phases = phases.float()

    # 组合坐标和相位
    x_data = torch.cat([x_src, x_rec, phases.unsqueeze(0)], dim=0)

    # 计算真实慢度值（使用接收器深度）
    print("  计算慢度值...")
    slowness_values = torch.zeros(1, n_total, device=device)

    # 向量化计算以提高效率
    rec_depths_km = (x_rec[2, :] * scale).cpu().numpy()  # 转换回km
    phases_np = phases.cpu().numpy()

    # 分别处理P波和S波
    p_mask = phases_np <= 0.5
    s_mask = phases_np > 0.5

    # P波慢度
    if p_mask.sum() > 0:
        p_depths = rec_depths_km[p_mask]
        p_velocities = np.array([velocity_model.get_velocity(d, 'P') for d in p_depths], dtype=np.float32)
        slowness_values[0, p_mask] = torch.from_numpy(1.0 / p_velocities).to(device)

    # S波慢度
    if s_mask.sum() > 0:
        s_depths = rec_depths_km[s_mask]
        s_velocities = np.array([velocity_model.get_velocity(d, 'S') for d in s_depths], dtype=np.float32)
        slowness_values[0, s_mask] = torch.from_numpy(1.0 / s_velocities).to(device)

    # 分割训练和测试数据
    x_train = x_data[:, :n_train]
    x_test = x_data[:, n_train:]
    s_train = slowness_values[:, :n_train]
    s_test = slowness_values[:, n_train:]

    # 创建数据加载器
    train_dataset = torch.utils.data.TensorDataset(x_train.T, s_train.T)
    test_dataset = torch.utils.data.TensorDataset(x_test.T, s_test.T)

    train_loader = torch.utils.data.DataLoader(train_dataset, batch_size=256, shuffle=True)
    test_loader = torch.utils.data.DataLoader(test_dataset, batch_size=1024, shuffle=False)

    return train_loader, test_loader


def load_config(config_path: str, test_mode: bool = False) -> dict:
    """
    加载配置文件并根据模式调整参数
    
    Args:
        config_path: 配置文件路径
        test_mode: 是否为测试模式
        
    Returns:
        配置字典
    """
    with open(config_path, 'r') as f:
        config = json.load(f)
    
    if test_mode:
        # 测试模式：适中的训练参数
        config['training']['n_epochs'] = 10
        config['training']['n_train'] = 10000
        config['training']['n_test'] = 5000
        config['training']['batch_size'] = 256
        config['logging']['plot_interval'] = 1  # 每轮都画图
        print("=== 测试模式 ===")
        print(f"训练轮次: {config['training']['n_epochs']}")
        print(f"训练样本: {config['training']['n_train']}")
        print(f"测试样本: {config['training']['n_test']}")
    else:
        # 完整训练模式
        config['logging']['plot_interval'] = 1  # 每轮都画图
        print("=== 完整训练模式 ===")
        print(f"训练轮次: {config['training']['n_epochs']}")
        print(f"训练样本: {config['training']['n_train']}")
        print(f"测试样本: {config['training']['n_test']}")
    
    return config


def create_coordinate_bounds(config: dict) -> dict:
    """从配置创建坐标边界字典"""
    bounds = config['data']['coordinate_bounds']
    return bounds


def plot_velocity_comparison(velocity_model: VelocityModel1D, model: EikoNet,
                           coordinate_bounds: dict, scale: float,
                           save_path: str, epoch: int):
    """
    绘制速度模型对比图（预测vs真实）
    使用多个随机源-接收器对来全面测试模型

    Args:
        velocity_model: 真实速度模型
        model: 训练好的EikoNet模型
        coordinate_bounds: 坐标边界
        scale: 缩放因子
        save_path: 保存路径
        epoch: 当前轮次
    """
    device = next(model.parameters()).device

    # 创建测试深度
    z_min = coordinate_bounds['z_min']
    z_max = coordinate_bounds['z_max']
    depths = np.linspace(z_min, z_max, 30)

    # 为每个深度创建多个随机源-接收器对
    n_pairs_per_depth = 20  # 每个深度测试20个不同的源-接收器对
    n_total = len(depths) * n_pairs_per_depth

    # 生成随机测试数据
    x_test_all = torch.zeros(7, n_total, device=device)
    depth_indices = []

    idx = 0
    for i, depth in enumerate(depths):
        for j in range(n_pairs_per_depth):
            # 随机源位置
            x_test_all[0, idx] = torch.rand(1, device=device)  # x_src
            x_test_all[1, idx] = torch.rand(1, device=device)  # y_src
            x_test_all[2, idx] = depth / scale  # z_src (normalized)

            # 随机接收器位置（确保与源有一定距离）
            while True:
                x_rec = torch.rand(1, device=device)
                y_rec = torch.rand(1, device=device)
                z_rec = depth / scale

                # 确保源-接收器距离合理（不要太近）
                dist = torch.sqrt((x_rec - x_test_all[0, idx])**2 +
                                (y_rec - x_test_all[1, idx])**2 +
                                (z_rec - x_test_all[2, idx])**2)
                if dist > 0.1:  # 最小距离阈值
                    x_test_all[3, idx] = x_rec
                    x_test_all[4, idx] = y_rec
                    x_test_all[5, idx] = z_rec
                    break

            depth_indices.append(i)
            idx += 1
    
    # 测试P波和S波
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

    model.eval()
    with torch.no_grad():
        for phase_idx, (phase_name, phase_val) in enumerate([('P', 0), ('S', 1)]):
            ax = ax1 if phase_idx == 0 else ax2

            # 设置相位
            x_test_phase = x_test_all.clone()
            x_test_phase[6, :] = phase_val

            # 获取真实速度（对应每个深度）
            true_velocities_all = []
            predicted_velocities_all = []
            depths_all = []

            # 使用Eikonal PDE直接获取预测的慢度
            from eikonet.utils import eikonal_pde
            predicted_slowness = eikonal_pde(x_test_phase, model, scale)

            # 转换为速度 (v = 1/s)
            pred_velocities = (1.0 / (predicted_slowness.squeeze() + 1e-8)).cpu().numpy()

            # 按深度组织数据
            for i, depth_idx in enumerate(depth_indices):
                depth = depths[depth_idx]
                true_vel = velocity_model.get_velocity(depth, phase_name)
                pred_vel = pred_velocities[i]

                true_velocities_all.append(true_vel)
                predicted_velocities_all.append(pred_vel)
                depths_all.append(depth)

            true_velocities_all = np.array(true_velocities_all)
            predicted_velocities_all = np.array(predicted_velocities_all)
            depths_all = np.array(depths_all)

            # 计算差异统计
            mae = np.mean(np.abs(predicted_velocities_all - true_velocities_all))
            rmse = np.sqrt(np.mean((predicted_velocities_all - true_velocities_all)**2))

            # 按照Julia代码的方式绘制：使用接收器深度作为x轴，速度作为y轴
            # 获取接收器深度（第6个维度，索引5）
            rec_depths_all = []
            for i in range(len(depth_indices)):
                rec_depth = x_test_phase[5, i].cpu().item() * scale  # 转换回km
                rec_depths_all.append(rec_depth)
            rec_depths_all = np.array(rec_depths_all)

            # 限制显示点数（与Julia代码一致）
            max_points = min(1000, len(predicted_velocities_all))
            indices = np.random.choice(len(predicted_velocities_all), max_points, replace=False)

            # 绘制预测速度点（Julia代码风格：scatter plot）
            ax.scatter(rec_depths_all[indices], predicted_velocities_all[indices],
                      c='red', s=15, alpha=0.6, label=f'v̂ ({phase_name})', zorder=5)

            # 绘制真实速度点
            true_vels_sampled = [velocity_model.get_velocity(d, phase_name) for d in rec_depths_all[indices]]
            ax.scatter(rec_depths_all[indices], true_vels_sampled,
                      c='blue', s=15, alpha=0.6, label=f'v ({phase_name})', zorder=4)

            ax.set_xlabel('Depth (km)')
            ax.set_ylabel(f'{phase_name}-wave Velocity (km/s)')
            ax.set_title(f'{phase_name}-wave Velocity (Epoch {epoch})\nMAE: {mae:.3f}, RMSE: {rmse:.3f}')
            ax.legend()
            ax.grid(True, alpha=0.3)

            # 设置合理的y轴范围（与Julia代码一致）
            ax.set_ylim(0, 10)
            ax.set_xlim(z_min, z_max)
    
    plt.tight_layout()

    # 确保目录存在
    Path(save_path).parent.mkdir(parents=True, exist_ok=True)

    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()

    print(f"速度对比图已保存到: {save_path}")


def main():
    """主训练函数"""
    parser = argparse.ArgumentParser(description='EikoNet训练脚本')
    parser.add_argument('--test', action='store_true', help='测试模式（10轮训练）')
    parser.add_argument('--config', type=str, default='eikonet_config.json', help='配置文件路径')
    args = parser.parse_args()
    
    # 设置路径
    config_path = args.config
    output_dir = "."
    plot_dir = "plots"
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    os.makedirs(plot_dir, exist_ok=True)
    
    print("=" * 60)
    print("EikoNet 训练脚本 - PyTorch版本")
    print("=" * 60)
    
    # 加载配置
    print("\n1. 加载配置文件...")
    config = load_config(config_path, test_mode=args.test)
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"\n使用设备: {device}")
    if device.type == 'cuda':
        print(f"GPU: {torch.cuda.get_device_name()}")
        print(f"GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
    
    # 加载速度模型
    print("\n2. 加载速度模型...")
    velocity_model = VelocityModel1D.from_csv(config['data']['velmod_file'])
    print(f"速度模型: {velocity_model}")
    
    # 加载台站数据（用于验证）
    print("\n3. 加载台站数据...")
    station_loader = StationDataLoader(config['data']['station_file'])
    print(f"加载了 {len(station_loader.data)} 个台站")
    
    # 创建坐标边界
    coordinate_bounds = create_coordinate_bounds(config)
    scale = config['data']['scale']
    
    # 生成训练数据
    print("\n4. 生成训练数据...")
    train_loader, test_loader = build_improved_training_data(
        velocity_model=velocity_model,
        n_train=config['training']['n_train'],
        n_test=config['training']['n_test'],
        coordinate_bounds=coordinate_bounds,
        scale=scale,
        device=device
    )
    
    print(f"训练样本: {config['training']['n_train']}")
    print(f"测试样本: {config['training']['n_test']}")
    
    # 创建模型
    print("\n5. 创建EikoNet模型...")
    model = EikoNet(scale=scale)
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 创建训练器
    loss_fn = EikonalLoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=config['training']['lr'])
    
    trainer = EikoNetTrainer(
        model=model,
        loss_fn=loss_fn,
        optimizer=optimizer,
        device=device,
        scale=scale
    )
    
    # 开始训练
    print(f"\n6. 开始训练 ({config['training']['n_epochs']} epochs)...")
    model_save_path = os.path.join(output_dir, "eikonet_model.pth")
    
    # 训练过程中每隔指定轮次画图
    plot_interval = config['logging']['plot_interval']
    
    # 自定义训练循环以支持中间画图和时间显示
    best_val_loss = float('inf')
    train_losses = []
    val_losses = []
    epoch_times = []

    import time
    training_start_time = time.time()

    for epoch in range(config['training']['n_epochs']):
        epoch_start_time = time.time()

        # 训练一轮
        train_loss = trainer.train_epoch(train_loader, verbose=False)
        val_loss = trainer.validate(test_loader)

        epoch_end_time = time.time()
        epoch_duration = epoch_end_time - epoch_start_time
        epoch_times.append(epoch_duration)

        train_losses.append(train_loss)
        val_losses.append(val_loss)

        # 更新最佳模型
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            trainer.save_model(model_save_path)

        # 计算时间统计
        total_elapsed = epoch_end_time - training_start_time
        avg_epoch_time = np.mean(epoch_times)
        remaining_epochs = config['training']['n_epochs'] - (epoch + 1)
        estimated_remaining_time = remaining_epochs * avg_epoch_time
        estimated_total_time = total_elapsed + estimated_remaining_time

        # 格式化时间显示
        def format_time(seconds):
            hours = int(seconds // 3600)
            minutes = int((seconds % 3600) // 60)
            secs = int(seconds % 60)
            if hours > 0:
                return f"{hours:02d}h{minutes:02d}m{secs:02d}s"
            else:
                return f"{minutes:02d}m{secs:02d}s"

        # 打印详细进度信息
        print(f"Epoch {epoch+1:4d}/{config['training']['n_epochs']} | "
              f"Train Loss: {train_loss:.6f} | "
              f"Val Loss: {val_loss:.6f} | "
              f"Best: {best_val_loss:.6f} | "
              f"Time: {format_time(epoch_duration)} | "
              f"Elapsed: {format_time(total_elapsed)} | "
              f"ETA: {format_time(estimated_remaining_time)}")

        # 每轮都画速度对比图
        if (epoch + 1) % plot_interval == 0:
            plot_path = os.path.join(plot_dir, f"velocity_comparison_epoch_{epoch+1:03d}.png")
            plot_velocity_comparison(
                velocity_model, model, coordinate_bounds, scale, plot_path, epoch+1
            )
    
    # 保存最终结果
    final_training_time = time.time() - training_start_time
    print(f"\n训练完成!")
    print(f"最佳损失: {best_val_loss:.6f}")
    print(f"总训练时间: {format_time(final_training_time)}")
    print(f"平均每轮时间: {format_time(avg_epoch_time)}")
    print(f"模型已保存到: {model_save_path}")
    print(f"共生成 {len([f for f in os.listdir(plot_dir) if f.endswith('.png')])} 张速度对比图")
    
    # 绘制训练历史
    plt.figure(figsize=(10, 6))
    epochs = range(1, len(train_losses) + 1)
    plt.plot(epochs, train_losses, 'b-', label='Training Loss', linewidth=2)
    plt.plot(epochs, val_losses, 'r-', label='Validation Loss', linewidth=2)
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.title('EikoNet Training History')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.yscale('log')
    plt.tight_layout()
    
    history_path = os.path.join(output_dir, "training_history.png")
    plt.savefig(history_path, dpi=300, bbox_inches='tight')
    plt.close()
    print(f"训练历史图已保存到: {history_path}")
    
    print("=" * 60)


if __name__ == "__main__":
    main()
