#!/usr/bin/env python3
"""
EikoNet训练脚本 - PyTorch版本

使用配置文件训练EikoNet神经网络模型，用于地震走时预测。
包含速度差异记录和可视化功能。
"""

import os
import sys
import json
import torch
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, List, Tuple

# 添加src路径到Python路径
current_dir = Path(__file__).parent
src_dir = current_dir.parent / "src"
sys.path.insert(0, str(src_dir))

from eikonet.models import EikoNet
from eikonet.trainer import EikoNetTrainer
from eikonet.loss import EikonalLoss, get_loss_function
from eikonet.utils import eikonal_pde
from data.velocity_model import VelocityModel1D, build_1d_training_data
from data.loader import StationDataLoader
from utils.config import load_config
from utils.logging import setup_logging


def load_and_adjust_config(config_path: str) -> dict:
    """
    加载配置文件

    Args:
        config_path: 配置文件路径

    Returns:
        配置字典
    """
    with open(config_path, 'r') as f:
        config = json.load(f)

    print(f"配置已加载:")
    print(f"  台站文件: {config['data']['station_file']}")
    print(f"  速度模型文件: {config['data']['velmod_file']}")
    print(f"  训练样本数: {config['training']['n_train']}")
    print(f"  测试样本数: {config['training']['n_test']}")
    print(f"  训练轮次: {config['training']['n_epochs']}")

    return config


def create_coordinate_bounds(config: dict) -> dict:
    """
    从配置创建坐标边界字典

    Args:
        config: 配置字典

    Returns:
        坐标边界字典
    """
    bounds = config['data']['coordinate_bounds']
    return {
        'lon_min': bounds['lon_min'],
        'lon_max': bounds['lon_max'],
        'lat_min': bounds['lat_min'],
        'lat_max': bounds['lat_max'],
        'z_min': bounds['z_min'],
        'z_max': bounds['z_max']
    }


class VelocityDifferenceTracker:
    """
    速度差异跟踪器，用于记录和可视化预测速度与真实速度的差异
    """

    def __init__(self, velocity_model: VelocityModel1D, coordinate_bounds: dict,
                 scale: float, device: torch.device, plot_dir: str):
        self.velocity_model = velocity_model
        self.coordinate_bounds = coordinate_bounds
        self.scale = scale
        self.device = device
        self.plot_dir = Path(plot_dir)
        self.plot_dir.mkdir(exist_ok=True)

        # 创建测试数据点
        self.test_depths = np.linspace(
            coordinate_bounds['z_min'],
            coordinate_bounds['z_max'],
            50
        )
        self.test_data = self._create_test_data()

    def _create_test_data(self) -> torch.Tensor:
        """创建用于测试的固定数据点"""
        n_test = len(self.test_depths)
        x_test = torch.zeros(7, n_test, device=self.device)

        # 固定源位置（中心点）
        x_test[0, :] = 0.5  # x_src
        x_test[1, :] = 0.5  # y_src
        x_test[2, :] = torch.tensor(self.test_depths / self.scale, device=self.device)  # z_src

        # 固定接收器位置（稍微偏移）
        x_test[3, :] = 0.6  # x_rec
        x_test[4, :] = 0.6  # y_rec
        x_test[5, :] = torch.tensor(self.test_depths / self.scale, device=self.device)  # z_rec

        return x_test

    def record_velocity_difference(self, model: torch.nn.Module, epoch: int) -> Dict[str, float]:
        """
        记录当前模型的速度差异

        Args:
            model: 训练中的模型
            epoch: 当前轮次

        Returns:
            速度差异统计信息
        """
        model.eval()
        stats = {}

        with torch.no_grad():
            for phase_name, phase_val in [('P', 0), ('S', 1)]:
                # 设置相位
                x_test = self.test_data.clone()
                x_test[6, :] = phase_val

                # 获取真实速度
                true_velocities = []
                for depth in self.test_depths:
                    vel = self.velocity_model.get_velocity(depth, phase_name)
                    true_velocities.append(vel)
                true_velocities = np.array(true_velocities)

                # 获取模型预测的走时
                predicted_times = model(x_test)

                # 从预测走时反推速度
                distances = torch.sqrt(torch.sum((x_test[3:6] - x_test[0:3])**2, dim=0)) * self.scale
                predicted_velocities = (distances / predicted_times.squeeze()).cpu().numpy()

                # 计算差异统计
                velocity_diff = predicted_velocities - true_velocities
                abs_diff = np.abs(velocity_diff)
                rel_diff = abs_diff / true_velocities * 100  # 相对误差百分比

                stats[f'{phase_name}_mae'] = np.mean(abs_diff)
                stats[f'{phase_name}_rmse'] = np.sqrt(np.mean(velocity_diff**2))
                stats[f'{phase_name}_mape'] = np.mean(rel_diff)
                stats[f'{phase_name}_max_error'] = np.max(abs_diff)

        return stats


def plot_training_history(history: dict, save_path: str):
    """
    绘制训练历史
    
    Args:
        history: 训练历史字典
        save_path: 保存路径
    """
    fig, ax = plt.subplots(1, 1, figsize=(10, 6))
    
    epochs = range(1, len(history['train_losses']) + 1)
    ax.plot(epochs, history['train_losses'], 'b-', label='Training Loss', linewidth=2)
    
    if history['val_losses'] and len(history['val_losses']) > 0:
        ax.plot(epochs, history['val_losses'], 'r-', label='Validation Loss', linewidth=2)
    
    ax.set_xlabel('Epoch')
    ax.set_ylabel('Loss')
    ax.set_title('EikoNet Training History')
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"训练历史图已保存到: {save_path}")


def plot_velocity_comparison(velocity_model: VelocityModel1D, model: EikoNet, 
                           coordinate_bounds: dict, scale: float, save_path: str):
    """
    绘制速度模型对比图（预测vs真实）
    
    Args:
        velocity_model: 真实速度模型
        model: 训练好的EikoNet模型
        coordinate_bounds: 坐标边界
        scale: 缩放因子
        save_path: 保存路径
    """
    device = next(model.parameters()).device
    
    # 创建测试深度
    z_min = coordinate_bounds['z_min']
    z_max = coordinate_bounds['z_max']
    depths = np.linspace(z_min, z_max, 100)
    
    # 创建测试数据点（固定源和接收器位置，变化深度）
    n_test = len(depths)
    x_test = torch.zeros(7, n_test, device=device)
    
    # 固定源位置（中心点）
    x_test[0, :] = 0.5  # x_src
    x_test[1, :] = 0.5  # y_src  
    x_test[2, :] = torch.tensor(depths / scale, device=device)  # z_src (normalized)
    
    # 固定接收器位置（稍微偏移）
    x_test[3, :] = 0.6  # x_rec
    x_test[4, :] = 0.6  # y_rec
    x_test[5, :] = torch.tensor(depths / scale, device=device)  # z_rec (normalized)
    
    # 测试P波和S波
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    for phase_idx, (phase_name, phase_val) in enumerate([('P', 0), ('S', 1)]):
        ax = ax1 if phase_idx == 0 else ax2
        
        # 设置相位
        x_test[6, :] = phase_val
        
        # 获取真实速度
        true_velocities = []
        for depth in depths:
            vel = velocity_model.get_velocity(depth, phase_name)
            true_velocities.append(vel)
        true_velocities = np.array(true_velocities)
        
        # 获取模型预测的走时
        model.eval()
        with torch.no_grad():
            predicted_times = model(x_test)
            
        # 计算直线走时
        tau0_times = model.tau0(x_test)
        
        # 从预测走时反推速度（简化计算）
        distances = torch.sqrt(torch.sum((x_test[3:6] - x_test[0:3])**2, dim=0)) * scale
        predicted_velocities = (distances / predicted_times.squeeze()).cpu().numpy()
        
        # 绘图
        ax.plot(true_velocities, depths, 'b-', linewidth=2, label=f'True {phase_name}-wave')
        ax.plot(predicted_velocities, depths, 'r--', linewidth=2, label=f'Predicted {phase_name}-wave')
        
        ax.set_xlabel(f'{phase_name}-wave Velocity (km/s)')
        ax.set_ylabel('Depth (km)')
        ax.set_title(f'{phase_name}-wave Velocity Comparison')
        ax.invert_yaxis()
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"速度对比图已保存到: {save_path}")


def main():
    """主训练函数"""
    # 设置路径
    config_path = "../../hyposvi/eikonet.json"
    data_dir = "../../hyposvi"
    output_dir = "."
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 设置日志
    log_file = os.path.join(output_dir, f"eikonet_training_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    
    print("=" * 60)
    print("EikoNet 训练脚本 - PyTorch版本")
    print("=" * 60)
    
    # 加载配置
    print("\n1. 加载配置文件...")
    config = load_and_adjust_config(config_path, data_dir)
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"\n使用设备: {device}")
    if device.type == 'cuda':
        print(f"GPU: {torch.cuda.get_device_name()}")
        print(f"GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
    
    # 加载速度模型
    print("\n2. 加载速度模型...")
    velocity_model = VelocityModel1D.from_csv(config['velmod_file'])
    print(f"速度模型: {velocity_model}")
    
    # 绘制速度模型
    vel_plot_path = os.path.join(output_dir, "velocity_model.png")
    velocity_model.plot_model(save_path=vel_plot_path)
    
    # 加载台站数据（用于验证）
    print("\n3. 加载台站数据...")
    station_loader = StationDataLoader(config['station_file'])
    print(f"加载了 {len(station_loader.data)} 个台站")
    
    # 创建坐标边界
    coordinate_bounds = create_coordinate_bounds(config)
    scale = 200.0  # 与Julia版本保持一致
    
    # 生成训练数据
    print("\n4. 生成训练数据...")
    train_loader, test_loader = build_1d_training_data(
        velocity_model=velocity_model,
        n_train=config['n_train'],
        n_test=config['n_test'],
        coordinate_bounds=coordinate_bounds,
        scale=scale,
        device=device
    )
    
    print(f"训练样本: {config['n_train']}")
    print(f"测试样本: {config['n_test']}")
    
    # 创建模型
    print("\n5. 创建EikoNet模型...")
    model = EikoNet(scale=scale)
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 创建训练器
    loss_fn = EikonalLoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=config['lr'])
    
    trainer = EikoNetTrainer(
        model=model,
        loss_fn=loss_fn,
        optimizer=optimizer,
        device=device,
        scale=scale
    )
    
    # 开始训练
    print(f"\n6. 开始训练 ({config['n_epochs']} epochs)...")
    model_save_path = os.path.join(output_dir, "eikonet_model.pth")
    
    history = trainer.train(
        train_loader=train_loader,
        val_loader=test_loader,
        n_epochs=config['n_epochs'],
        save_path=model_save_path,
        checkpoint_interval=20,
        early_stopping_patience=50,
        verbose=True
    )
    
    # 保存训练历史
    print("\n7. 保存结果...")
    
    # 绘制训练历史
    history_plot_path = os.path.join(output_dir, "training_history.png")
    plot_training_history(history, history_plot_path)
    
    # 绘制速度对比图
    comparison_plot_path = os.path.join(output_dir, "velocity_comparison.pdf")
    plot_velocity_comparison(velocity_model, model, coordinate_bounds, scale, comparison_plot_path)
    
    # 保存配置和历史
    results = {
        'config': config,
        'coordinate_bounds': coordinate_bounds,
        'scale': scale,
        'training_history': history,
        'model_path': model_save_path
    }
    
    results_path = os.path.join(output_dir, "training_results.json")
    with open(results_path, 'w') as f:
        # 转换numpy类型为Python原生类型以便JSON序列化
        def convert_numpy(obj):
            if isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            return obj
        
        json.dump(results, f, indent=2, default=convert_numpy)
    
    print(f"\n训练完成!")
    print(f"最佳损失: {history['best_loss']:.6f}")
    print(f"训练时间: {history['total_time']:.1f}秒")
    print(f"模型已保存到: {model_save_path}")
    print(f"结果已保存到: {results_path}")
    print("=" * 60)


if __name__ == "__main__":
    main()
