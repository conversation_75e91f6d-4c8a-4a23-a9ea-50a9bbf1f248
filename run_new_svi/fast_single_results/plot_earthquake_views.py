#!/usr/bin/env python3
"""
Plot earthquake locations in three views: front view, side view, and top view
Compare relocated earthquakes with original locations
"""

import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import os

def plot_earthquake_views(relocated_csv, original_csv, output_dir):
    """
    Plot earthquake locations in three different views, comparing relocated and original positions

    Parameters:
    relocated_csv: path to the relocated_catalog.csv file
    original_csv: path to the pre_cat.csv file (original locations)
    output_dir: directory to save the plots
    """

    # Read the earthquake catalogs
    df_relocated = pd.read_csv(relocated_csv)
    df_original = pd.read_csv(original_csv)

    # Extract coordinates for relocated earthquakes
    lon_relocated = df_relocated['longitude'].values
    lat_relocated = df_relocated['latitude'].values
    depth_relocated = df_relocated['depth'].values

    # Extract coordinates for original earthquakes
    lon_original = df_original['longitude'].values
    lat_original = df_original['latitude'].values
    depth_original = df_original['depth'].values
    
    # Create figure with three subplots
    fig = plt.figure(figsize=(18, 6))

    # Plot 1: Top view (longitude vs latitude)
    ax1 = fig.add_subplot(131)

    # Plot original earthquakes (blue circles)
    scatter1_orig = ax1.scatter(lon_original, lat_original, c='blue',
                               s=15, alpha=0.6, edgecolors='darkblue', linewidth=0.3,
                               label='Original Locations')

    # Plot relocated earthquakes (red circles)
    scatter1_reloc = ax1.scatter(lon_relocated, lat_relocated, c='red',
                                s=15, alpha=0.7, edgecolors='darkred', linewidth=0.3,
                                label='Relocated Locations')

    ax1.set_xlabel('Longitude (degrees)', fontsize=12)
    ax1.set_ylabel('Latitude (degrees)', fontsize=12)
    ax1.set_title('Top View (Map View)', fontsize=14, fontweight='bold')
    ax1.grid(True, alpha=0.3)
    ax1.set_aspect('equal', adjustable='box')
    ax1.legend(loc='upper right', fontsize=10)
    
    # Plot 2: Front view (longitude vs depth)
    ax2 = fig.add_subplot(132)

    # Plot original earthquakes (blue circles)
    scatter2_orig = ax2.scatter(lon_original, depth_original, c='blue',
                               s=15, alpha=0.6, edgecolors='darkblue', linewidth=0.3,
                               label='Original Locations')

    # Plot relocated earthquakes (red circles)
    scatter2_reloc = ax2.scatter(lon_relocated, depth_relocated, c='red',
                                s=15, alpha=0.7, edgecolors='darkred', linewidth=0.3,
                                label='Relocated Locations')

    ax2.set_xlabel('Longitude (degrees)', fontsize=12)
    ax2.set_ylabel('Depth (km)', fontsize=12)
    ax2.set_title('Front View (Longitude-Depth)', fontsize=14, fontweight='bold')
    ax2.grid(True, alpha=0.3)
    ax2.invert_yaxis()  # Invert y-axis so depth increases downward
    ax2.legend(loc='upper right', fontsize=10)
    
    # Plot 3: Side view (latitude vs depth)
    ax3 = fig.add_subplot(133)

    # Plot original earthquakes (blue circles)
    scatter3_orig = ax3.scatter(lat_original, depth_original, c='blue',
                               s=15, alpha=0.6, edgecolors='darkblue', linewidth=0.3,
                               label='Original Locations')

    # Plot relocated earthquakes (red circles)
    scatter3_reloc = ax3.scatter(lat_relocated, depth_relocated, c='red',
                                s=15, alpha=0.7, edgecolors='darkred', linewidth=0.3,
                                label='Relocated Locations')

    ax3.set_xlabel('Latitude (degrees)', fontsize=12)
    ax3.set_ylabel('Depth (km)', fontsize=12)
    ax3.set_title('Side View (Latitude-Depth)', fontsize=14, fontweight='bold')
    ax3.grid(True, alpha=0.3)
    ax3.invert_yaxis()  # Invert y-axis so depth increases downward
    ax3.legend(loc='upper right', fontsize=10)
    
    # Adjust layout to prevent overlap
    plt.tight_layout()

    # Add main title
    fig.suptitle('Earthquake Location Views - Original vs Relocated Comparison',
                 fontsize=16, fontweight='bold', y=1.02)

    # Save the plot
    output_file = os.path.join(output_dir, 'earthquake_comparison_views.png')
    plt.savefig(output_file, dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')

    # Print statistics
    print(f"Earthquake catalog comparison statistics:")
    print(f"Total events: {len(df_relocated)}")
    print(f"\nOriginal locations:")
    print(f"  Longitude range: {lon_original.min():.4f} to {lon_original.max():.4f} degrees")
    print(f"  Latitude range: {lat_original.min():.4f} to {lat_original.max():.4f} degrees")
    print(f"  Depth range: {depth_original.min():.2f} to {depth_original.max():.2f} km")
    print(f"  Mean depth: {depth_original.mean():.2f} km")
    print(f"\nRelocated locations:")
    print(f"  Longitude range: {lon_relocated.min():.4f} to {lon_relocated.max():.4f} degrees")
    print(f"  Latitude range: {lat_relocated.min():.4f} to {lat_relocated.max():.4f} degrees")
    print(f"  Depth range: {depth_relocated.min():.2f} to {depth_relocated.max():.2f} km")
    print(f"  Mean depth: {depth_relocated.mean():.2f} km")

    # Calculate relocation differences
    lon_diff = np.abs(lon_relocated - lon_original)
    lat_diff = np.abs(lat_relocated - lat_original)
    depth_diff = np.abs(depth_relocated - depth_original)

    print(f"\nRelocation differences (absolute values):")
    print(f"  Mean longitude shift: {lon_diff.mean():.4f} degrees ({lon_diff.mean()*111:.1f} km)")
    print(f"  Mean latitude shift: {lat_diff.mean():.4f} degrees ({lat_diff.mean()*111:.1f} km)")
    print(f"  Mean depth shift: {depth_diff.mean():.2f} km")
    print(f"  Max longitude shift: {lon_diff.max():.4f} degrees ({lon_diff.max()*111:.1f} km)")
    print(f"  Max latitude shift: {lat_diff.max():.4f} degrees ({lat_diff.max()*111:.1f} km)")
    print(f"  Max depth shift: {depth_diff.max():.2f} km")

    print(f"\nPlot saved to: {output_file}")

    # Close the figure to free memory
    plt.close()

def main():
    """Main function"""
    # Define file paths
    relocated_csv = 'relocated_catalog.csv'
    original_csv = '/home/<USER>/run_eikonet_hyposvi/hyposvi/pre_cat.csv'
    output_dir = '.'

    # Check if input files exist
    if not os.path.exists(relocated_csv):
        print(f"Error: Relocated catalog file '{relocated_csv}' not found!")
        return

    if not os.path.exists(original_csv):
        print(f"Error: Original catalog file '{original_csv}' not found!")
        return

    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Generate the plots
    plot_earthquake_views(relocated_csv, original_csv, output_dir)

if __name__ == "__main__":
    main()
