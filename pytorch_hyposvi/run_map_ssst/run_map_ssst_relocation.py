#!/usr/bin/env python3
"""
运行MAP和SSST地震重定位

使用EikoNet神经网络进行地震走时预测，结合MAP定位方法和SSST台站校正，
实现高精度的地震重定位。

主要流程:
1. 初始MAP定位
2. 静态迭代优化
3. SSST校正计算和应用
4. 迭代优化定位结果
5. 生成重定位震相和目录文件
"""

import sys
import os
import json
import time
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime

# 添加源代码路径
script_dir = Path(__file__).parent
src_dir = script_dir.parent / "src"
if str(src_dir.resolve()) not in sys.path:
    sys.path.insert(0, str(src_dir.resolve()))

# 导入MAP-SSST定位器
from map_ssst_locator import MAPSSTLocator


def main():
    """主函数"""
    print("🚀 开始MAP-SSST地震重定位")
    print("=" * 80)
    
    # 设置路径
    config_file = script_dir / "map_ssst_config.json"
    model_file = "/home/<USER>/run_eikonet_hyposvi/pytorch_hyposvi/run_eikonet/eikonet_model.pth"
    
    # 数据文件路径
    phase_file = "/home/<USER>/run_eikonet_hyposvi/hyposvi/phase.csv"
    station_file = "/home/<USER>/run_eikonet_hyposvi/hyposvi/station.csv"
    catalog_file = "/home/<USER>/run_eikonet_hyposvi/hyposvi/pre_cat.csv"
    
    # 输出目录
    output_dir = script_dir / "map_ssst_results"
    
    print(f"📁 配置文件: {config_file}")
    print(f"📁 模型文件: {model_file}")
    print(f"📁 震相文件: {phase_file}")
    print(f"📁 台站文件: {station_file}")
    print(f"📁 地震目录: {catalog_file}")
    print(f"📁 输出目录: {output_dir}")
    print()
    
    # 检查文件是否存在
    required_files = [config_file, model_file, phase_file, station_file, catalog_file]
    for file_path in required_files:
        if not Path(file_path).exists():
            print(f"❌ 文件不存在: {file_path}")
            return
    
    print("✅ 所有必需文件都存在")
    print()
    
    try:
        # 创建MAP-SSST定位器
        print("🔧 初始化MAP-SSST定位器...")
        locator = MAPSSTLocator(str(config_file), model_file)
        
        # 加载模型和数据
        print("📂 加载模型和数据...")
        locator.load_model_and_data(phase_file, station_file, catalog_file)
        
        # 获取所有事件ID
        phases_df = pd.read_csv(phase_file)
        event_ids = sorted(phases_df['evid'].unique())
        
        print(f"📊 发现 {len(event_ids)} 个事件")
        print(f"   - 事件ID范围: {min(event_ids)} - {max(event_ids)}")
        
        # 可以选择处理部分事件进行测试
        # 这里处理前100个事件作为示例
        test_event_ids = event_ids  # 可以修改为 event_ids 处理所有事件
        print(f"   - 本次处理: {len(test_event_ids)} 个事件")
        print()
        
        # 开始MAP-SSST迭代定位
        print("🎯 开始MAP-SSST迭代定位...")
        start_time = time.time()
        
        final_stats = locator.run_map_ssst_iteration(
            event_ids=test_event_ids,
            output_dir=str(output_dir)
        )
        
        total_time = time.time() - start_time
        
        # 显示最终结果
        print("\n" + "=" * 80)
        print("🎉 MAP-SSST定位完成!")
        print(f"📊 最终统计:")
        print(f"   - 总事件数: {final_stats.get('total_events', 0)}")
        print(f"   - SSST校正数: {final_stats.get('total_ssst_corrections', 0)}")
        print(f"   - 最终RMS残差: {final_stats.get('final_rms_residual', 0):.3f} s")
        print(f"   - 最终MAD残差: {final_stats.get('final_mad_residual', 0):.3f} s")
        print(f"   - 总耗时: {total_time/3600:.2f} 小时")
        print(f"   - 平均每事件: {total_time/len(test_event_ids):.1f} 秒")
        print(f"📁 结果保存在: {output_dir}")
        
        # 生成重定位后的震相文件
        print("\n" + "=" * 80)
        print("📋 生成重定位后的震相和目录文件...")
        
        # 读取最终定位结果
        final_locations_file = output_dir / "final_map_ssst_locations.csv"
        if final_locations_file.exists():
            final_locations_df = pd.read_csv(final_locations_file)
            
            # 生成重定位震相文件
            relocated_phases_df = locator.generate_relocated_phases(
                final_locations_df, str(output_dir)
            )
            
            # 生成重定位目录文件
            generate_relocated_catalog(final_locations_df, catalog_file, str(output_dir))
            
            print("\n✅ 重定位数据文件生成完成!")
            
            # 显示一些统计信息
            if len(final_locations_df) > 0:
                print(f"📈 最终定位结果统计:")
                print(f"   - 经度范围: {final_locations_df['longitude'].min():.4f} - {final_locations_df['longitude'].max():.4f}")
                print(f"   - 纬度范围: {final_locations_df['latitude'].min():.4f} - {final_locations_df['latitude'].max():.4f}")
                print(f"   - 深度范围: {final_locations_df['depth_km'].min():.2f} - {final_locations_df['depth_km'].max():.2f} km")
                
                if 'rms_residual' in final_locations_df.columns:
                    print(f"   - 平均RMS: {final_locations_df['rms_residual'].mean():.3f} s")
        
        print("\n" + "=" * 80)
        print("🎉 所有处理完成!")
        print(f"📁 输出目录: {output_dir}")
        print("📋 生成的主要文件:")
        print("   - initial_map_locations.csv (初始MAP定位)")
        print("   - static_iter_*.csv (静态迭代结果)")
        print("   - ssst_iter_*.csv (SSST迭代结果)")
        print("   - final_map_ssst_locations.csv (最终定位结果)")
        print("   - final_ssst_corrections.csv (最终SSST校正)")
        print("   - relocated_phases_map_ssst_*.csv (重定位震相)")
        print("   - relocated_catalog_map_ssst.csv (重定位目录)")
        print("   - iteration_statistics.csv (迭代统计)")
        
    except Exception as e:
        print(f"❌ 处理过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return


def generate_relocated_catalog(locations_df: pd.DataFrame, original_catalog_file: str, output_dir: str):
    """
    生成重定位后的地震目录文件
    
    Args:
        locations_df: 重定位结果DataFrame
        original_catalog_file: 原始地震目录文件路径
        output_dir: 输出目录
    """
    print("📋 生成重定位后的地震目录文件...")
    
    # 读取原始目录
    original_catalog = pd.read_csv(original_catalog_file)
    
    # 合并数据
    merged_df = pd.merge(locations_df, original_catalog, on='evid', suffixes=('_relocated', '_original'))
    
    # 创建重定位后的目录
    relocated_catalog = []
    
    for _, row in merged_df.iterrows():
        relocated_event = {
            'evid': row['evid'],
            'time': row.get('time_original', row.get('time', '')),  # 保持原始发震时刻
            'longitude': row['longitude_relocated'],
            'latitude': row['latitude_relocated'],
            'depth': row['depth_km'],
            'magnitude': row.get('magnitude', row.get('mag', 0)),
            # 保留原始信息用于对比
            'original_longitude': row.get('longitude_original', row.get('longitude', 0)),
            'original_latitude': row.get('latitude_original', row.get('latitude', 0)),
            'original_depth': row.get('depth_original', row.get('depth', 0)),
            # 重定位质量信息
            'longitude_std': row.get('longitude_std', 0),
            'latitude_std': row.get('latitude_std', 0),
            'depth_std_km': row.get('depth_std_km', 0),
            'rms_residual': row.get('rms_residual', 0),
            'n_phases': row.get('n_phases', 0),
            'n_p_phases': row.get('n_p_phases', 0),
            'n_s_phases': row.get('n_s_phases', 0),
            'method': 'MAP_SSST',
            'processing_time': row.get('processing_time', 0)
        }
        
        relocated_catalog.append(relocated_event)
    
    # 创建DataFrame并保存
    relocated_catalog_df = pd.DataFrame(relocated_catalog)
    
    if len(relocated_catalog_df) > 0:
        # 保存完整的重定位目录
        full_catalog_file = Path(output_dir) / "relocated_catalog_map_ssst_full.csv"
        relocated_catalog_df.to_csv(full_catalog_file, index=False)
        print(f"   ✅ 完整重定位目录: {full_catalog_file}")
        
        # 保存简化版本
        simple_catalog_df = relocated_catalog_df[['evid', 'time', 'longitude', 'latitude', 'depth', 'magnitude']].copy()
        simple_catalog_file = Path(output_dir) / "relocated_catalog_map_ssst_simple.csv"
        simple_catalog_df.to_csv(simple_catalog_file, index=False)
        print(f"   ✅ 简化重定位目录: {simple_catalog_file}")
        
        print(f"   📊 生成了 {len(relocated_catalog_df)} 个重定位事件记录")
    else:
        print("   ❌ 没有生成重定位目录数据")


if __name__ == "__main__":
    main()
