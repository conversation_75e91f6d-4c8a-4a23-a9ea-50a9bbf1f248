"""
Custom layers for EikoNet neural network.

This module implements custom PyTorch layers used in the EikoNet architecture,
including coordinate transformations and positional encodings.
"""

import torch
import torch.nn as nn
from typing import Tuple


class CylindricalSymmetry(nn.Module):
    """
    Cylindrical symmetry layer for coordinate transformation.
    
    Transforms input coordinates to exploit cylindrical symmetry in the problem.
    Converts (x_src, y_src, z_src, x_rec, y_rec, z_rec, phase) to 
    (radial_offset, z_src, z_rec, phase).
    
    Args:
        in_dims: Input dimension (should be 7)
        out_dims: Output dimension (should be 4)
    """
    
    def __init__(self, in_dims: int = 7, out_dims: int = 4):
        super().__init__()
        self.in_dims = in_dims
        self.out_dims = out_dims
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Forward pass of cylindrical symmetry transformation.
        
        Args:
            x: Input tensor of shape (batch_size, 7) or (7, n_samples)
                [x_src, y_src, z_src, x_rec, y_rec, z_rec, phase]
                
        Returns:
            Transformed tensor of shape (batch_size, 4) or (4, n_samples)
            [radial_offset, z_src, z_rec, phase]
        """
        if x.dim() == 2 and x.size(0) == 7:
            # Julia-style format: (7, n_samples)
            x_src, y_src = x[0:1], x[1:2]  # Source coordinates
            z_src = x[2:3]
            x_rec, y_rec = x[3:4], x[4:5]  # Receiver coordinates  
            z_rec = x[5:6]
            phase = x[6:7]
            
            # Calculate radial offset between source and receiver
            radial_offset = torch.sqrt((x_src - x_rec)**2 + (y_src - y_rec)**2)
            
            # Stack transformed coordinates
            x_new = torch.cat([radial_offset, z_src, z_rec, phase], dim=0)
            
        elif x.dim() == 2 and x.size(1) == 7:
            # Standard PyTorch format: (batch_size, 7)
            x_src, y_src = x[:, 0:1], x[:, 1:2]
            z_src = x[:, 2:3]
            x_rec, y_rec = x[:, 3:4], x[:, 4:5]
            z_rec = x[:, 5:6]
            phase = x[:, 6:7]
            
            radial_offset = torch.sqrt((x_src - x_rec)**2 + (y_src - y_rec)**2)
            x_new = torch.cat([radial_offset, z_src, z_rec, phase], dim=1)
            
        else:
            raise ValueError(f"Unexpected input shape: {x.shape}")
            
        return x_new


class PosEncoding(nn.Module):
    """
    Positional encoding layer using sinusoidal functions.
    
    Applies sinusoidal positional encoding to the first 6 dimensions
    and passes through the 7th dimension unchanged.
    
    Args:
        in_dims: Input dimension (should be 7)
        out_dims: Output dimension (should be 13)
    """
    
    def __init__(self, in_dims: int = 7, out_dims: int = 13):
        super().__init__()
        self.in_dims = in_dims
        self.out_dims = out_dims
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Forward pass of positional encoding.
        
        Args:
            x: Input tensor of shape (batch_size, 7) or (7, n_samples)
                
        Returns:
            Encoded tensor with sinusoidal features
        """
        if x.dim() == 2 and x.size(0) == 7:
            # Julia-style format: (7, n_samples)
            coords = x[0:6]  # First 6 dimensions
            phase = x[6:7]   # 7th dimension
            
            # Apply sinusoidal encoding to coordinates
            cos_coords = torch.cos(2 * torch.pi * coords)
            sin_coords = torch.sin(2 * torch.pi * coords)
            
            # Concatenate: [cos(coords), sin(coords), phase]
            encoded = torch.cat([cos_coords, sin_coords, phase], dim=0)
            
        elif x.dim() == 2 and x.size(1) == 7:
            # Standard PyTorch format: (batch_size, 7)
            coords = x[:, 0:6]
            phase = x[:, 6:7]
            
            cos_coords = torch.cos(2 * torch.pi * coords)
            sin_coords = torch.sin(2 * torch.pi * coords)
            
            encoded = torch.cat([cos_coords, sin_coords, phase], dim=1)
            
        else:
            raise ValueError(f"Unexpected input shape: {x.shape}")
            
        return encoded


class SkipConnection(nn.Module):
    """
    Skip connection (residual connection) layer.
    
    Implements a skip connection that adds the input to the output
    of a sequence of layers.
    
    Args:
        layers: Sequential layers to apply before adding skip connection
    """
    
    def __init__(self, layers: nn.Sequential):
        super().__init__()
        self.layers = layers
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Forward pass with skip connection.
        
        Args:
            x: Input tensor
            
        Returns:
            Output tensor with skip connection: x + layers(x)
        """
        return x + self.layers(x)
