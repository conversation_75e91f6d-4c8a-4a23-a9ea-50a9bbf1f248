{"data": {"station_file": "/home/<USER>/run_eikonet_hyposvi/hyposvi/station.csv", "phase_file": "/home/<USER>/run_eikonet_hyposvi/hyposvi/phase.csv", "catalog_file": "/home/<USER>/run_eikonet_hyposvi/hyposvi/pre_cat.csv", "catalog_outfile": "/home/<USER>/run_eikonet_hyposvi/pytorch_hyposvi/run_map_ssst/map_ssst_catalog.csv", "residual_outfile": "/home/<USER>/run_eikonet_hyposvi/pytorch_hyposvi/run_map_ssst/map_ssst_residuals.csv", "ssst_outfile": "/home/<USER>/run_eikonet_hyposvi/pytorch_hyposvi/run_map_ssst/ssst_corrections.csv", "coordinate_bounds": {"lon_min": -118.2, "lat_min": 35.3, "z_min": 0.0, "z_max": 25.0}, "scale": 200.0}, "model": {"eikonet_model_file": "/home/<USER>/run_eikonet_hyposvi/pytorch_hyposvi/run_eikonet/eikonet_model.pth", "prevent_airquakes": false}, "inversion": {"method": "MAP", "n_epochs": 200, "learning_rate": 0.001, "iter_tol": 0.01, "optimizer": "adam"}, "uncertainty": {"pick_unc_p": 0.1, "pick_unc_s": 0.1, "n_phase_min_pick_std": 15, "likelihood_fn": "huber", "barron_alpha": 1.5}, "prior": {"prior_z_mean": 10.0, "prior_z_std": 10.0, "prior_scale_param": 10.0, "max_src_rec_dist": 75.0}, "ssst": {"n_static_iter": 2, "n_ssst_iter": 2, "ssst_mode": "knn", "k-NN": 30, "min_ssst_radius": 0.5, "max_ssst_radius": 8.0}, "device": {"use_cuda": true, "cuda_device": 0}, "logging": {"log_level": "INFO", "verbose": true, "show_progress": true}, "output": {"save_residuals": true, "save_uncertainties": true, "save_ssst": true, "save_iterations": true, "output_format": "csv"}}