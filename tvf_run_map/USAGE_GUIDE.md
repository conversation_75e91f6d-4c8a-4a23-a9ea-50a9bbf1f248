# TVF MAP地震重定位程序使用指南

## 快速开始

### 1. 安装依赖
```bash
cd /home/<USER>/run_eikonet_hyposvi/tvf_run_map
python install_taup.py
```

### 2. 运行测试
```bash
# 走时验证测试
python test_travel_time_validation.py

# 快速MAP定位测试
python quick_test_map.py

# 完整示例程序
python example_usage.py
```

### 3. 运行主程序
```bash
# 直接运行主程序
python run_tvf_map_ultra_fast.py
```

## 程序文件说明

| 文件名 | 功能描述 |
|--------|----------|
| `run_tvf_map_ultra_fast.py` | 主程序，MAP地震重定位 |
| `install_taup.py` | TauP依赖安装脚本 |
| `test_travel_time_validation.py` | 走时验证测试程序 |
| `quick_test_map.py` | 快速测试脚本 |
| `example_usage.py` | 完整使用示例 |
| `tvf_map_config.json` | 配置文件 |
| `README_ANALYSIS.md` | 详细技术报告 |

## 主要功能

### ✅ EikoNet神经网络定位
- 快速走时计算
- MAP最大后验估计
- 自适应先验约束

### ✅ TauP理论验证
- iasp91地球模型
- 实时走时对比
- 统计误差分析

### ✅ 批量处理能力
- 支持数万事件处理
- 多进程并行优化
- 智能内存管理

## 验证结果摘要

**测试数据**: 云南腾冲-龙陵地区，15,021个地震，274,020个震相

**验证精度**:
- P波平均相对误差: 8.2%
- S波平均相对误差: 8.9%
- 验证成功率: 74-100%

**定位精度**:
- RMS残差: ~0.65秒
- MAD残差: ~0.52秒
- 处理速度: ~5秒/事件

## 使用建议

1. **首次使用**: 运行 `python example_usage.py` 进行完整测试
2. **大批量处理**: 修改主程序中的事件选择逻辑
3. **参数调优**: 根据数据质量调整配置文件中的参数
4. **结果验证**: 重点关注走时验证的相对误差指标

## 技术支持

如有问题，请参考 `README_ANALYSIS.md` 中的详细技术文档。
