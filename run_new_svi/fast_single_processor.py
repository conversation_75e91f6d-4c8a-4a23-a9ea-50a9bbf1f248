#!/usr/bin/env python3
"""
快速单线程处理器 - 专注于减少数据加载开销

主要优化:
1. 数据预加载和复用 - 避免重复加载大文件
2. 定位器复用 - 减少初始化开销
3. 批量I/O操作 - 减少文件操作次数
4. 内存优化 - 高效的内存管理

预期加速: 2-3倍
"""

import sys
import os
import json
import time
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 添加pytorch_hyposvi到Python路径
current_dir = Path(__file__).parent.absolute()
pytorch_hyposvi_path = current_dir.parent / "pytorch_hyposvi"
if pytorch_hyposvi_path.exists():
    sys.path.insert(0, str(pytorch_hyposvi_path))
    print(f"✅ 已添加pytorch_hyposvi路径: {pytorch_hyposvi_path}")
else:
    print(f"❌ 未找到pytorch_hyposvi路径: {pytorch_hyposvi_path}")

# 静态导入
import torch

# 导入HypoSVI相关模块
# 首先尝试直接导入
HypoSVILocator = None
ENUToLLA = None
EikoNet = None

try:
    # 直接导入模块文件
    import sys
    import importlib.util

    # 先导入layers模块
    layers_path = pytorch_hyposvi_path / "src" / "eikonet" / "layers.py"
    spec = importlib.util.spec_from_file_location("eikonet_layers", layers_path)
    eikonet_layers = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(eikonet_layers)

    # 将layers模块添加到全局命名空间，这样EikoNet可以找到它
    globals()['CylindricalSymmetry'] = eikonet_layers.CylindricalSymmetry
    globals()['SkipConnection'] = eikonet_layers.SkipConnection

    # 现在导入EikoNet，但需要修改其导入
    eikonet_path = pytorch_hyposvi_path / "src" / "eikonet" / "models.py"
    with open(eikonet_path, 'r') as f:
        eikonet_code = f.read()

    # 修改相对导入为使用全局变量
    eikonet_code = eikonet_code.replace(
        "from .layers import CylindricalSymmetry, SkipConnection",
        "# from .layers import CylindricalSymmetry, SkipConnection  # 已在全局导入"
    )

    # 执行修改后的代码
    exec(eikonet_code, globals())
    print("✅ 成功导入EikoNet")
except Exception as e:
    print(f"❌ 无法导入EikoNet: {e}")
    print(f"   详细错误: {type(e).__name__}: {e}")

try:
    # 导入ENUToLLA
    transforms_path = pytorch_hyposvi_path / "src" / "data" / "transforms.py"
    spec = importlib.util.spec_from_file_location("transforms", transforms_path)
    transforms = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(transforms)
    ENUToLLA = transforms.ENUToLLA
    print("✅ 成功导入ENUToLLA")
except Exception as e:
    print(f"❌ 无法导入ENUToLLA: {e}")

# HypoSVILocator比较复杂，需要特殊处理
try:
    # 先添加src目录到路径
    src_path = pytorch_hyposvi_path / "src"
    if str(src_path) not in sys.path:
        sys.path.insert(0, str(src_path))

    # 现在尝试导入
    from hyposvi.locator import HypoSVILocator
    print("✅ 成功导入HypoSVILocator")
except Exception as e:
    print(f"❌ 无法导入HypoSVILocator: {e}")
    print(f"   详细错误: {type(e).__name__}: {e}")
    HypoSVILocator = None


def check_imports():
    """检查所有必需的模块是否已正确导入"""
    missing_modules = []

    if HypoSVILocator is None:
        missing_modules.append("HypoSVILocator")
    if ENUToLLA is None:
        missing_modules.append("ENUToLLA")
    if EikoNet is None:
        missing_modules.append("EikoNet")

    if missing_modules:
        raise ImportError(f"以下模块导入失败: {', '.join(missing_modules)}")

    print("✅ 所有必需模块已成功导入")


def convert_to_geographic_coordinates(result: Dict, config: Dict, coord_transform) -> Dict:
    """转换坐标到地理坐标系"""
    try:
        # 从hypocenter字段获取坐标（scaled coordinates）
        hypocenter = result.get('hypocenter', [0, 0, 0])
        if len(hypocenter) < 3:
            return {'longitude': 0, 'latitude': 0, 'depth_km': 0,
                   'longitude_std': 0, 'latitude_std': 0, 'depth_std_km': 0}

        # 获取缩放参数
        scale = config['data']['scale']

        # 转换scaled coordinates到实际坐标（米）
        # 按照Julia版本的转换方式：X_best .*= 1f3 .* scale
        east_m = hypocenter[0] * 1000.0 * scale
        north_m = hypocenter[1] * 1000.0 * scale
        depth_m = hypocenter[2] * 1000.0 * scale

        # 转换ENU坐标到地理坐标
        # coord_transform是LLAToENU对象，使用inverse_transform方法
        lat, lon, alt = coord_transform.inverse_transform(east_m, north_m, -depth_m)
        depth_km = depth_m / 1000.0

        # 处理不确定性
        if 'hypocenter_std' in result:
            # SVI格式
            uncertainties = result['hypocenter_std']
            east_std_m = uncertainties[0] * 1000.0 * scale
            north_std_m = uncertainties[1] * 1000.0 * scale
            depth_std_m = uncertainties[2] * 1000.0 * scale
        elif 'uncertainties' in result and isinstance(result['uncertainties'], dict):
            # MAP格式
            uncertainties = result['uncertainties']
            east_std_m = uncertainties.get('x', 0.0) * 1000.0 * scale
            north_std_m = uncertainties.get('y', 0.0) * 1000.0 * scale
            depth_std_m = uncertainties.get('z', 0.0) * 1000.0 * scale
        else:
            east_std_m = north_std_m = depth_std_m = 0.0

        depth_std_km = depth_std_m / 1000.0

        # 转换不确定性为地理坐标
        lat_rad = np.radians(lat)
        R_earth = 6371000.0

        lat_std_deg = north_std_m / R_earth * 180.0 / np.pi
        lon_std_deg = east_std_m / (R_earth * np.cos(lat_rad)) * 180.0 / np.pi

        return {
            'longitude': lon,
            'latitude': lat,
            'depth_km': depth_km,
            'longitude_std': lon_std_deg,
            'latitude_std': lat_std_deg,
            'depth_std_km': depth_std_km,
            'east_m': east_m,
            'north_m': north_m,
            'east_std_m': east_std_m,
            'north_std_m': north_std_m
        }
    except Exception as e:
        print(f"坐标转换失败: {e}")
        return {'longitude': 0, 'latitude': 0, 'depth_km': 0,
               'longitude_std': 0, 'latitude_std': 0, 'depth_std_km': 0}


class FastSingleProcessor:
    """快速单线程处理器 - 专注于减少开销"""
    
    def __init__(self, config_file: str, model_file: str):
        self.config_file = config_file
        self.model_file = model_file

        # 加载配置
        with open(config_file, 'r') as f:
            self.config = json.load(f)

        # 设置设备
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

        # 加载EikoNet模型
        self.eikonet_model = self._load_eikonet_model()

        # 预创建定位器（复用）
        self.locator = None
        self.data_loaded = False

        # 台站坐标和坐标转换器（将在load_data_once中初始化）
        self.station_coords = {}
        self.coord_transform = None

        print(f"⚡ 快速单线程处理器初始化")
        print(f"   - GPU可用: {torch.cuda.is_available()}")
        print(f"   - EikoNet模型已加载")

    def _load_eikonet_model(self):
        """加载EikoNet模型"""
        if EikoNet is None:
            raise ImportError("EikoNet模块未能成功导入")

        state = torch.load(self.model_file, map_location=self.device)
        scale = state.get('scale', self.config['data']['scale'])

        model = EikoNet(scale=scale)

        if 'model_state_dict' in state:
            model.load_state_dict(state['model_state_dict'])
        else:
            model.load_state_dict(state)

        model.to(self.device)
        model.eval()

        return model
    
    def load_data_once(self, phase_file: str, station_file: str, catalog_file: str):
        """一次性加载所有数据"""
        print("📂 预加载数据...")
        start_time = time.time()
        
        # 创建定位器
        if self.locator is None:
            self.locator = HypoSVILocator(self.config_file, self.model_file)
            self.locator.load_data(phase_file, station_file)

        # 获取坐标转换器
        self.coord_transform = self.locator.preprocessor.coord_transform

        # 加载台站坐标
        stations_df = pd.read_csv(station_file)
        for _, station in stations_df.iterrows():
            east, north, up = self.coord_transform.transform(
                station['latitude'], station['longitude'], station['elevation']
            )
            self.station_coords[station['station']] = {
                'east': east,
                'north': north,
                'depth': -up  # 转换为深度（负的高程）
            }

        # 加载原始目录
        self.catalog_df = pd.read_csv(catalog_file)
        self.original_catalog = {}
        for _, row in self.catalog_df.iterrows():
            self.original_catalog[row['evid']] = {
                'latitude': row['latitude'],
                'longitude': row['longitude'],
                'depth': row['depth']
            }
        
        self.data_loaded = True
        load_time = time.time() - start_time
        print(f"   ✅ 数据加载完成: {load_time:.1f}秒")
        print(f"   - 地震目录: {len(self.catalog_df)} 个事件")
        print(f"   - 台站数量: {len(self.station_coords)} 个")
        print(f"   - 原始目录: {len(self.original_catalog)} 个事件")
    
    def process_single_event_fast(self, evid: int) -> Optional[Dict]:
        """快速处理单个事件"""
        if not self.data_loaded:
            raise RuntimeError("数据未加载，请先调用 load_data_once()")
        
        try:
            start_time = time.time()
            
            # 获取原始信息
            original_info = self.original_catalog.get(evid)
            
            # 设置初始位置和先验
            initial_location = None
            original_prior = None
            
            if original_info is not None:
                initial_location = {
                    'latitude': original_info['latitude'],
                    'longitude': original_info['longitude'],
                    'depth': original_info['depth']
                }
                original_prior = initial_location.copy()
            
            # 执行定位（复用已加载的定位器）
            result = self.locator.locate_single_event(evid, initial_location, original_prior)
            
            if result is not None:
                # 转换为地理坐标
                geo_coords = convert_to_geographic_coordinates(
                    result, self.config, self.locator.preprocessor.coord_transform
                )
                
                # 合并结果
                combined_result = {**result, **geo_coords}
                combined_result['evid'] = evid
                combined_result['processing_time'] = time.time() - start_time
                combined_result['method'] = 'FAST_SINGLE'
                
                return combined_result
            
            return None
            
        except Exception as e:
            print(f"❌ 事件 {evid} 处理失败: {e}")
            return None
        finally:
            # 清理GPU缓存
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
    
    def process_events_batch_fast(self, event_ids: List[int], output_dir: str,
                                 save_interval: int = 50) -> Dict:
        """快速批量处理事件"""
        
        if not self.data_loaded:
            raise RuntimeError("数据未加载，请先调用 load_data_once()")
        
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        results_file = output_path / "fast_single_results.csv"
        
        # 检查已处理事件
        processed_events = set()
        if results_file.exists():
            try:
                existing_df = pd.read_csv(results_file)
                processed_events = set(existing_df['evid'].tolist())
                print(f"📂 发现已处理事件: {len(processed_events)} 个")
            except:
                pass
        
        # 过滤未处理事件
        remaining_events = [evid for evid in event_ids if evid not in processed_events]
        
        if not remaining_events:
            print("✅ 所有事件已处理完成")
            return {'successful_events': len(processed_events), 'failed_events': 0}
        
        print(f"🎯 待处理事件: {len(remaining_events)} 个")
        
        # 开始快速处理
        start_time = time.time()
        successful_results = []
        failed_count = 0
        
        print(f"⚡ 开始快速单线程处理...")
        
        for i, evid in enumerate(remaining_events):
            try:
                result = self.process_single_event_fast(evid)
                
                if result is not None:
                    successful_results.append(result)
                    
                    # 显示进度
                    if (i + 1) % 10 == 0 or i == 0:
                        elapsed_time = time.time() - start_time
                        avg_time = elapsed_time / (i + 1)
                        remaining_time = (len(remaining_events) - i - 1) * avg_time
                        progress = (i + 1) / len(remaining_events) * 100
                        
                        print(f"📊 进度: {i+1}/{len(remaining_events)} ({progress:.1f}%) "
                              f"- 平均: {avg_time:.1f}s/事件 - 剩余: {remaining_time/60:.1f}分钟")
                else:
                    failed_count += 1
                
                # 定期保存结果
                if len(successful_results) % save_interval == 0 and successful_results:
                    self._save_results_batch(successful_results[-save_interval:], results_file)
                
            except Exception as e:
                print(f"❌ 事件 {evid} 处理失败: {e}")
                failed_count += 1
        
        # 保存剩余结果
        if successful_results:
            remaining_to_save = len(successful_results) % save_interval
            if remaining_to_save > 0:
                self._save_results_batch(successful_results[-remaining_to_save:], results_file)
        
        # 最终统计
        total_time = time.time() - start_time
        
        print("⚡ 快速处理完成!")
        print(f"   - 总处理时间: {total_time/60:.1f} 分钟")
        print(f"   - 成功事件: {len(successful_results)}")
        print(f"   - 失败事件: {failed_count}")
        
        if len(successful_results) > 0:
            avg_time = np.mean([r['processing_time'] for r in successful_results])
            print(f"   - 平均每事件时间: {avg_time:.1f}秒")
            print(f"   - 处理速度: {len(successful_results)*60/total_time:.1f} 事件/分钟")
        
        return {
            'successful_events': len(successful_results),
            'failed_events': failed_count,
            'total_time': total_time
        }
    
    def _save_results_batch(self, results: List[Dict], output_file: Path):
        """批量保存结果"""
        rows = []
        for result in results:
            row_data = {
                'evid': result['evid'],
                'longitude': result['longitude'],
                'latitude': result['latitude'],
                'depth_km': result['depth_km'],
                'longitude_std': result.get('longitude_std', 0),
                'latitude_std': result.get('latitude_std', 0),
                'depth_std_km': result.get('depth_std_km', 0),
                'origin_time_offset': result.get('origin_time_offset', 0),
                'n_phases': result.get('n_phases', 0),
                'n_p_phases': result.get('n_p_phases', 0),
                'n_s_phases': result.get('n_s_phases', 0),
                'n_iterations': result.get('n_iterations', 0),
                'n_particles': result.get('n_particles', 0),
                'method': result.get('method', 'FAST_SINGLE'),
                'rms_residual': np.sqrt(np.mean(result.get('residuals', [])**2)) if 'residuals' in result else 0,
                'processing_time': result.get('processing_time', 0)
            }
            rows.append(row_data)
        
        df = pd.DataFrame(rows)
        
        # 批量写入
        if output_file.exists():
            df.to_csv(output_file, mode='a', header=False, index=False)
        else:
            df.to_csv(output_file, index=False)

    def generate_output_files(self, results_file: str, output_dir: str):
        """生成地震目录文件和震相文件"""
        print("\n📝 生成地震目录和震相文件...")

        output_path = Path(output_dir)

        # 输出文件路径
        catalog_file = output_path / "relocated_catalog.csv"
        phase_file = output_path / "relocated_phases.csv"

        try:
            # 读取结果文件
            results_df = pd.read_csv(results_file)
            print(f"   - 读取结果: {len(results_df)} 个事件")

            # 生成地震目录文件
            self._generate_catalog_file(results_df, catalog_file)

            # 生成震相文件
            self._generate_phase_file(results_df, phase_file)

            print(f"✅ 输出文件生成完成!")
            print(f"   - 地震目录: {catalog_file}")
            print(f"   - 震相文件: {phase_file}")

        except Exception as e:
            print(f"❌ 生成输出文件失败: {e}")
            import traceback
            traceback.print_exc()

    def _generate_catalog_file(self, results_df: pd.DataFrame, output_file: Path):
        """生成地震目录文件"""
        catalog_data = []

        for _, row in results_df.iterrows():
            evid = int(row['evid'])

            # 获取重定位后的发震时间（独立计算每个事件）
            if 'origin_time' in row:
                # 如果结果中直接包含重定位后的发震时间
                new_origin_time = datetime.strptime(row['origin_time'], '%Y-%m-%dT%H:%M:%S.%f')
            else:
                # 从原始目录中获取该事件的初始时间，然后应用时间偏移
                original_event = self.catalog_df[self.catalog_df['evid'] == evid]
                if len(original_event) == 0:
                    print(f"     警告: 事件 {evid} 在原始目录中未找到")
                    continue

                original_time = datetime.strptime(original_event.iloc[0]['time'], '%Y-%m-%dT%H:%M:%S.%f')
                origin_time_offset = row['origin_time_offset']
                new_origin_time = original_time + timedelta(seconds=origin_time_offset)

            # 获取原始震级（如果有的话）
            original_event = self.catalog_df[self.catalog_df['evid'] == evid]
            if len(original_event) > 0:
                magnitude = original_event.iloc[0]['mag']
            else:
                magnitude = 1.0  # 默认震级

            catalog_entry = {
                'evid': evid,
                'latitude': round(row['latitude'], 4),
                'longitude': round(row['longitude'], 4),
                'depth': round(row['depth_km'], 2),
                'time': new_origin_time.strftime('%Y-%m-%dT%H:%M:%S.%f'),
                'mag': magnitude
            }
            catalog_data.append(catalog_entry)

        # 创建DataFrame并保存
        catalog_df = pd.DataFrame(catalog_data)
        catalog_df.to_csv(output_file, index=False)
        print(f"   - 地震目录文件已保存: {len(catalog_df)} 个事件")

    def _generate_phase_file(self, results_df: pd.DataFrame, output_file: Path):
        """基于EikoNet重新计算理论震相到时"""
        print("   🧮 使用EikoNet重新计算理论震相到时...")

        # 加载原始震相数据作为模板
        original_phase_file = "/home/<USER>/run_eikonet_hyposvi/hyposvi/phase.csv"
        original_phase_df = pd.read_csv(original_phase_file)

        phase_data = []

        for _, row in results_df.iterrows():
            evid = int(row['evid'])

            print(f"     处理事件 {evid}...")

            # 获取重定位后的震源位置和时间
            src_lat = row['latitude']
            src_lon = row['longitude']
            src_depth_km = row['depth_km']

            # 获取重定位后的发震时间（直接使用重定位结果中的时间）
            if 'origin_time' in row:
                # 如果结果中直接包含重定位后的发震时间
                new_origin_time = datetime.strptime(row['origin_time'], '%Y-%m-%dT%H:%M:%S.%f')
            else:
                # 从原始目录中获取该事件的初始时间，然后应用时间偏移
                original_event = self.catalog_df[self.catalog_df['evid'] == evid]
                if len(original_event) == 0:
                    print(f"       警告: 事件 {evid} 在原始目录中未找到")
                    continue

                original_time = datetime.strptime(original_event.iloc[0]['time'], '%Y-%m-%dT%H:%M:%S.%f')
                origin_time_offset = row['origin_time_offset']
                new_origin_time = original_time + timedelta(seconds=origin_time_offset)

            # 转换震源坐标到ENU（米）
            src_east, src_north, src_up = self.coord_transform.transform(src_lat, src_lon, 0)
            src_depth_m = src_depth_km * 1000.0

            # 获取该事件的原始震相（用于确定哪些台站有观测）
            event_phases = original_phase_df[original_phase_df['evid'] == evid]

            if len(event_phases) == 0:
                print(f"       警告: 事件 {evid} 没有震相数据")
                continue

            # 为每个震相计算理论到时
            phase_count = 0
            for _, phase_row in event_phases.iterrows():
                station_name = phase_row['station'].strip()  # 去除空格
                phase_type = phase_row['phase']

                if station_name not in self.station_coords:
                    continue

                # 获取台站坐标
                rec_coords = self.station_coords[station_name]

                # 使用EikoNet计算理论走时
                travel_time = self._calculate_travel_time_with_eikonet(
                    src_east, src_north, src_depth_m,
                    rec_coords['east'], rec_coords['north'], rec_coords['depth'],
                    phase_type
                )

                # 计算理论到达时间
                theoretical_arrival_time = new_origin_time + timedelta(seconds=travel_time)

                # 创建新的震相记录
                phase_entry = {
                    'time': theoretical_arrival_time.strftime('%Y-%m-%dT%H:%M:%S.%f'),
                    'evid': evid,
                    'arid': int(phase_row['arid']),
                    'phase': phase_type,
                    'network': phase_row['network'],
                    'station': station_name,
                    'theoretical_travel_time': travel_time,
                    'calculation_method': 'EikoNet_theoretical',
                    'origin_time': new_origin_time.strftime('%Y-%m-%dT%H:%M:%S.%f')
                }
                phase_data.append(phase_entry)
                phase_count += 1

            print(f"       完成: {phase_count} 条震相记录")

        # 创建DataFrame并保存
        phase_df = pd.DataFrame(phase_data)
        phase_df.to_csv(output_file, index=False)
        print(f"   - 理论震相文件已保存: {len(phase_df)} 条震相记录")

    def _calculate_travel_time_with_eikonet(
        self,
        src_east: float, src_north: float, src_depth_m: float,
        rec_east: float, rec_north: float, rec_depth_m: float,
        phase_type: str
    ) -> float:
        """使用EikoNet计算理论走时"""

        # 坐标缩放（转换为km并缩放）
        scale = self.config['data']['scale']

        x_src_scaled = (src_east / 1000.0) / scale
        y_src_scaled = (src_north / 1000.0) / scale
        z_src_scaled = (src_depth_m / 1000.0) / scale

        x_rec_scaled = (rec_east / 1000.0) / scale
        y_rec_scaled = (rec_north / 1000.0) / scale
        z_rec_scaled = (rec_depth_m / 1000.0) / scale

        # 震相类型编码（P=0, S=1）
        phase_code = 0.0 if phase_type == 'P' else 1.0

        # 构建输入张量
        x_input = torch.tensor([
            x_src_scaled, y_src_scaled, z_src_scaled,
            x_rec_scaled, y_rec_scaled, z_rec_scaled,
            phase_code
        ], dtype=torch.float32, device=self.device).unsqueeze(0)  # (1, 7)

        # 使用EikoNet计算理论走时
        with torch.no_grad():
            travel_time = self.eikonet_model(x_input.T).item()  # 转置为(7, 1)格式

        return travel_time


def main():
    """主函数"""
    print("🚀 开始地震重定位处理")
    print("="*50)

    # 检查模块导入
    try:
        check_imports()
    except ImportError as e:
        print(f"❌ 模块导入检查失败: {e}")
        return

    # 配置文件路径
    config_file = "/home/<USER>/run_eikonet_hyposvi/run_new_svi/parallel_config.json"
    model_file = "/home/<USER>/run_eikonet_hyposvi/pytorch_hyposvi/run_eikonet/eikonet_model.pth"
    
    # 数据文件路径
    phase_file = "/home/<USER>/run_eikonet_hyposvi/hyposvi/phase.csv"
    station_file = "/home/<USER>/run_eikonet_hyposvi/hyposvi/station.csv"
    catalog_file = "/home/<USER>/run_eikonet_hyposvi/hyposvi/pre_cat.csv"
    
    # 输出目录
    output_dir = "/home/<USER>/run_eikonet_hyposvi/run_new_svi/fast_single_results"
    
    try:
        # 创建快速处理器
        processor = FastSingleProcessor(config_file, model_file)
        
        # 预加载数据
        processor.load_data_once(phase_file, station_file, catalog_file)
        
        # 获取事件列表
        phases_df = pd.read_csv(phase_file)
        event_ids = sorted(phases_df['evid'].unique())

        print(f"📊 发现 {len(event_ids)} 个事件")
        
        # 开始快速处理
        stats = processor.process_events_batch_fast(event_ids, output_dir)

        # 生成地震目录和震相文件
        results_file = os.path.join(output_dir, "fast_single_results.csv")
        if os.path.exists(results_file):
            processor.generate_output_files(results_file, output_dir)
        else:
            print("⚠️ 结果文件不存在，无法生成输出文件")

        print(f"📁 所有结果保存在: {output_dir}")

    except Exception as e:
        print(f"❌ 处理失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
