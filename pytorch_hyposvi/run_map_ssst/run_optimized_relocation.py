#!/usr/bin/env python3
"""
运行优化的MAP-SSST地震重定位

使用优化的参数配置，包括：
1. 更宽松的先验约束
2. 增强的SSST效果
3. 改进的优化算法参数
4. 先验衰减机制
"""

import sys
import os
import json
import time
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime

# 添加源代码路径
script_dir = Path(__file__).parent
src_dir = script_dir.parent / "src"
if str(src_dir.resolve()) not in sys.path:
    sys.path.insert(0, str(src_dir.resolve()))

# 导入MAP-SSST定位器
from map_ssst_locator import MAPSSTLocator


def main():
    """主函数"""
    print("🚀 开始优化的MAP-SSST地震重定位")
    print("=" * 80)
    
    # 使用优化的配置文件
    config_file = script_dir / "map_ssst_config_optimized.json"
    model_file = "/home/<USER>/run_eikonet_hyposvi/pytorch_hyposvi/run_eikonet/eikonet_model.pth"
    
    # 数据文件路径
    phase_file = "/home/<USER>/run_eikonet_hyposvi/hyposvi/phase.csv"
    station_file = "/home/<USER>/run_eikonet_hyposvi/hyposvi/station.csv"
    catalog_file = "/home/<USER>/run_eikonet_hyposvi/hyposvi/pre_cat.csv"
    
    # 输出目录
    output_dir = script_dir / "map_ssst_optimized_results"
    
    print(f"📁 优化配置文件: {config_file}")
    print(f"📁 模型文件: {model_file}")
    print(f"📁 震相文件: {phase_file}")
    print(f"📁 台站文件: {station_file}")
    print(f"📁 地震目录: {catalog_file}")
    print(f"📁 输出目录: {output_dir}")
    print()
    
    # 检查文件是否存在
    required_files = [config_file, model_file, phase_file, station_file, catalog_file]
    for file_path in required_files:
        if not Path(file_path).exists():
            print(f"❌ 文件不存在: {file_path}")
            return
    
    print("✅ 所有必需文件都存在")
    
    # 显示优化配置的关键参数
    with open(config_file, 'r') as f:
        config = json.load(f)
    
    print("\n🔧 优化配置参数:")
    print(f"   - 迭代次数: {config['inversion']['n_epochs']}")
    print(f"   - 学习率: {config['inversion']['learning_rate']}")
    print(f"   - 收敛容差: {config['inversion']['iter_tol']}")
    print(f"   - 深度先验标准差: {config['prior']['depth_prior_std']} km")
    print(f"   - 水平先验标准差: {config['prior']['horizontal_prior_std']} km")
    print(f"   - SSST迭代次数: {config['ssst']['n_ssst_iter']}")
    print(f"   - k-NN参数: {config['ssst']['k-NN']}")
    print(f"   - SSST半径范围: {config['ssst']['min_ssst_radius']}-{config['ssst']['max_ssst_radius']} km")
    print()
    
    try:
        # 创建优化的MAP-SSST定位器
        print("🔧 初始化优化的MAP-SSST定位器...")
        locator = MAPSSTLocator(str(config_file), model_file)
        
        # 加载模型和数据
        print("📂 加载模型和数据...")
        locator.load_model_and_data(phase_file, station_file, catalog_file)
        
        # 获取事件ID
        phases_df = pd.read_csv(phase_file)
        event_ids = sorted(phases_df['evid'].unique())
        
        print(f"📊 发现 {len(event_ids)} 个事件")
        print(f"   - 事件ID范围: {min(event_ids)} - {max(event_ids)}")
        
        # 选择处理事件数量（可以调整）
        # 先处理50个事件测试优化效果
        test_event_ids = event_ids  # 可以修改为 event_ids 处理所有事件
        print(f"   - 本次处理: {len(test_event_ids)} 个事件")
        print()
        
        # 开始优化的MAP-SSST迭代定位
        print("🎯 开始优化的MAP-SSST迭代定位...")
        start_time = time.time()
        
        final_stats = locator.run_map_ssst_iteration(
            event_ids=test_event_ids,
            output_dir=str(output_dir)
        )
        
        total_time = time.time() - start_time
        
        # 显示最终结果
        print("\n" + "=" * 80)
        print("🎉 优化的MAP-SSST定位完成!")
        print(f"📊 最终统计:")
        print(f"   - 总事件数: {final_stats.get('total_events', 0)}")
        print(f"   - SSST校正数: {final_stats.get('total_ssst_corrections', 0)}")
        print(f"   - 最终RMS残差: {final_stats.get('final_rms_residual', 0):.3f} s")
        print(f"   - 最终MAD残差: {final_stats.get('final_mad_residual', 0):.3f} s")
        print(f"   - 总耗时: {total_time/3600:.2f} 小时")
        print(f"   - 平均每事件: {total_time/len(test_event_ids):.1f} 秒")
        print(f"📁 结果保存在: {output_dir}")
        
        # 生成重定位后的震相和目录文件
        print("\n" + "=" * 80)
        print("📋 生成重定位后的震相和目录文件...")
        
        # 读取最终定位结果
        final_locations_file = output_dir / "final_map_ssst_locations.csv"
        if final_locations_file.exists():
            final_locations_df = pd.read_csv(final_locations_file)
            
            # 生成重定位震相文件
            relocated_phases_df = locator.generate_relocated_phases(
                final_locations_df, str(output_dir)
            )
            
            # 生成重定位目录文件
            generate_relocated_catalog(final_locations_df, catalog_file, str(output_dir))
            
            print("\n✅ 重定位数据文件生成完成!")
            
            # 计算和显示改善统计
            if len(final_locations_df) > 0:
                print(f"📈 优化后定位结果统计:")
                print(f"   - 经度范围: {final_locations_df['longitude'].min():.4f} - {final_locations_df['longitude'].max():.4f}")
                print(f"   - 纬度范围: {final_locations_df['latitude'].min():.4f} - {final_locations_df['latitude'].max():.4f}")
                print(f"   - 深度范围: {final_locations_df['depth_km'].min():.2f} - {final_locations_df['depth_km'].max():.2f} km")
                
                if 'rms_residual' in final_locations_df.columns:
                    print(f"   - 平均RMS: {final_locations_df['rms_residual'].mean():.3f} s")
                
                # 计算与原始位置的差异
                original_catalog = pd.read_csv(catalog_file)
                common_evids = set(final_locations_df['evid']).intersection(set(original_catalog['evid']))
                
                if len(common_evids) > 0:
                    original_subset = original_catalog[original_catalog['evid'].isin(common_evids)].sort_values('evid')
                    relocated_subset = final_locations_df[final_locations_df['evid'].isin(common_evids)].sort_values('evid')
                    
                    # 计算位移
                    lon_diff = relocated_subset['longitude'].values - original_subset['longitude'].values
                    lat_diff = relocated_subset['latitude'].values - original_subset['latitude'].values
                    depth_diff = relocated_subset['depth_km'].values - original_subset['depth'].values
                    
                    # 计算水平距离
                    avg_lat = np.mean(original_subset['latitude'])
                    lon_km = lon_diff * 111.0 * np.cos(np.radians(avg_lat))
                    lat_km = lat_diff * 111.0
                    horizontal_dist = np.sqrt(lon_km**2 + lat_km**2)
                    
                    print(f"\n📊 重定位改善统计:")
                    print(f"   - 平均水平位移: {np.mean(horizontal_dist):.2f} ± {np.std(horizontal_dist):.2f} km")
                    print(f"   - 最大水平位移: {np.max(horizontal_dist):.2f} km")
                    print(f"   - 平均深度变化: {np.mean(depth_diff):.2f} ± {np.std(depth_diff):.2f} km")
                    print(f"   - 最大深度变化: {np.max(np.abs(depth_diff)):.2f} km")
        
        print("\n" + "=" * 80)
        print("🎉 所有处理完成!")
        print(f"📁 输出目录: {output_dir}")
        print("📋 生成的主要文件:")
        print("   - initial_map_locations.csv (初始MAP定位)")
        print("   - static_iter_*.csv (静态迭代结果)")
        print("   - ssst_iter_*.csv (SSST迭代结果)")
        print("   - final_map_ssst_locations.csv (最终定位结果)")
        print("   - final_ssst_corrections.csv (最终SSST校正)")
        print("   - relocated_phases_map_ssst_*.csv (重定位震相)")
        print("   - relocated_catalog_map_ssst.csv (重定位目录)")
        print("   - iteration_statistics.csv (迭代统计)")
        
        print("\n💡 建议下一步:")
        print("   1. 运行 plot_relocation_comparison.py 生成对比图")
        print("   2. 分析重定位效果是否有显著改善")
        print("   3. 根据结果进一步调整参数")
        
    except Exception as e:
        print(f"❌ 处理过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return


def generate_relocated_catalog(locations_df: pd.DataFrame, original_catalog_file: str, output_dir: str):
    """
    生成重定位后的地震目录文件
    
    Args:
        locations_df: 重定位结果DataFrame
        original_catalog_file: 原始地震目录文件路径
        output_dir: 输出目录
    """
    print("📋 生成重定位后的地震目录文件...")
    
    # 读取原始目录
    original_catalog = pd.read_csv(original_catalog_file)
    
    # 合并数据
    merged_df = pd.merge(locations_df, original_catalog, on='evid', suffixes=('_relocated', '_original'))
    
    # 创建重定位后的目录
    relocated_catalog = []
    
    for _, row in merged_df.iterrows():
        relocated_event = {
            'evid': row['evid'],
            'time': row.get('time_original', row.get('time', '')),
            'longitude': row['longitude_relocated'],
            'latitude': row['latitude_relocated'],
            'depth': row['depth_km'],
            'magnitude': row.get('magnitude', row.get('mag', 0)),
            # 保留原始信息用于对比
            'original_longitude': row.get('longitude_original', row.get('longitude', 0)),
            'original_latitude': row.get('latitude_original', row.get('latitude', 0)),
            'original_depth': row.get('depth_original', row.get('depth', 0)),
            # 重定位质量信息
            'longitude_std': row.get('longitude_std', 0),
            'latitude_std': row.get('latitude_std', 0),
            'depth_std_km': row.get('depth_std_km', 0),
            'rms_residual': row.get('rms_residual', 0),
            'n_phases': row.get('n_phases', 0),
            'method': 'OPTIMIZED_MAP_SSST',
            'processing_time': row.get('processing_time', 0)
        }
        
        relocated_catalog.append(relocated_event)
    
    # 创建DataFrame并保存
    relocated_catalog_df = pd.DataFrame(relocated_catalog)
    
    if len(relocated_catalog_df) > 0:
        # 保存完整的重定位目录
        full_catalog_file = Path(output_dir) / "relocated_catalog_optimized_full.csv"
        relocated_catalog_df.to_csv(full_catalog_file, index=False)
        print(f"   ✅ 完整重定位目录: {full_catalog_file}")

        # 保存简化版本
        simple_catalog_df = relocated_catalog_df[['evid', 'time', 'longitude', 'latitude', 'depth', 'magnitude']].copy()
        simple_catalog_file = Path(output_dir) / "relocated_catalog_optimized_simple.csv"
        simple_catalog_df.to_csv(simple_catalog_file, index=False)
        print(f"   ✅ 简化重定位目录: {simple_catalog_file}")
        
        print(f"   📊 生成了 {len(relocated_catalog_df)} 个重定位事件记录")
    else:
        print("   ❌ 没有生成重定位目录数据")


if __name__ == "__main__":
    main()
