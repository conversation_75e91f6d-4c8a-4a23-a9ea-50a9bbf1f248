{"model": {"scale": 200.0, "architecture": {"input_dims": 7, "cylindrical_symmetry": {"in_dims": 13, "out_dims": 4}, "hidden_layers": [{"type": "dense", "units": 16, "activation": "elu"}, {"type": "skip_connection", "layers": [{"type": "dense", "units": 32, "activation": "elu"}, {"type": "dense", "units": 16, "activation": "elu"}]}, {"type": "skip_connection", "layers": [{"type": "dense", "units": 32, "activation": "elu"}, {"type": "dense", "units": 16, "activation": "elu"}]}, {"type": "skip_connection", "layers": [{"type": "dense", "units": 32, "activation": "elu"}, {"type": "dense", "units": 16, "activation": "elu"}]}, {"type": "skip_connection", "layers": [{"type": "dense", "units": 32, "activation": "elu"}, {"type": "dense", "units": 16, "activation": "elu"}]}, {"type": "skip_connection", "layers": [{"type": "dense", "units": 32, "activation": "elu"}, {"type": "dense", "units": 16, "activation": "elu"}]}, {"type": "skip_connection", "layers": [{"type": "dense", "units": 32, "activation": "elu"}, {"type": "dense", "units": 16, "activation": "elu"}]}], "output_layer": {"type": "dense", "units": 1, "activation": "relu"}}}, "data": {"velmod_file": "./velmod.csv", "n_train": 1000000, "n_test": 100000, "batch_size": 64, "coordinate_bounds": {"lon_min": -117.5, "lat_min": 33.0, "z_min": -3.0, "z_max": 70.0}}, "training": {"n_epochs": 200, "learning_rate": 0.001, "optimizer": "adam", "loss_weights": {"l1_weight": 0.95, "l2_weight": 0.05}, "finite_diff_epsilon": 0.001, "model_save_path": "./model.pth", "checkpoint_interval": 10, "early_stopping": {"patience": 20, "min_delta": 1e-06}}, "device": {"use_cuda": true, "cuda_device": 0}, "logging": {"log_level": "INFO", "log_file": "eikonet_training.log", "tensorboard_dir": "./runs"}}