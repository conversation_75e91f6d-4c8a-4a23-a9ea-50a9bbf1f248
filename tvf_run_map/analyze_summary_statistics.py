#!/usr/bin/env python3
"""
分析重定位差异结果的统计摘要
"""

import pandas as pd
import numpy as np
import os

def analyze_timing_results():
    """分析时间差异结果的统计摘要"""
    
    results_dir = "timing_analysis_results"
    
    print("="*70)
    print("地震重定位时间差异分析统计报告")
    print("="*70)
    
    # 1. 分析发震时刻差异
    if os.path.exists(f"{results_dir}/origin_time_differences.csv"):
        origin_df = pd.read_csv(f"{results_dir}/origin_time_differences.csv")
        
        print("\n【1. 发震时刻差异统计】")
        print("-" * 40)
        print(f"分析地震数量: {len(origin_df)}")
        print(f"发震时刻差异 (秒):")
        print(f"  平均值: {origin_df['time_diff_seconds'].mean():.3f}")
        print(f"  标准差: {origin_df['time_diff_seconds'].std():.3f}")
        print(f"  最小值: {origin_df['time_diff_seconds'].min():.3f}")
        print(f"  最大值: {origin_df['time_diff_seconds'].max():.3f}")
        print(f"  中位数: {origin_df['time_diff_seconds'].median():.3f}")
        
        # 位置差异统计
        print(f"\n位置差异统计:")
        print(f"  经度差异 (度):")
        print(f"    平均: {origin_df['lon_diff'].mean():.6f}")
        print(f"    标准差: {origin_df['lon_diff'].std():.6f}")
        print(f"  纬度差异 (度):")
        print(f"    平均: {origin_df['lat_diff'].mean():.6f}")
        print(f"    标准差: {origin_df['lat_diff'].std():.6f}")
        print(f"  深度差异 (km):")
        print(f"    平均: {origin_df['depth_diff'].mean():.6f}")
        print(f"    标准差: {origin_df['depth_diff'].std():.6f}")
        print(f"  水平距离差异 (km):")
        print(f"    平均: {origin_df['horizontal_dist_km'].mean():.3f}")
        print(f"    标准差: {origin_df['horizontal_dist_km'].std():.3f}")
        print(f"    最大: {origin_df['horizontal_dist_km'].max():.3f}")
    
    # 2. 分析震相到时差异
    if os.path.exists(f"{results_dir}/phase_time_differences.csv"):
        phase_df = pd.read_csv(f"{results_dir}/phase_time_differences.csv")
        
        print("\n【2. 震相到时差异统计】")
        print("-" * 40)
        print(f"震相记录数量: {len(phase_df)}")
        print(f"震相到时差异 (秒):")
        print(f"  平均值: {phase_df['time_diff_seconds'].mean():.3f}")
        print(f"  标准差: {phase_df['time_diff_seconds'].std():.3f}")
        print(f"  最小值: {phase_df['time_diff_seconds'].min():.3f}")
        print(f"  最大值: {phase_df['time_diff_seconds'].max():.3f}")
        print(f"  中位数: {phase_df['time_diff_seconds'].median():.3f}")
        
        # 按震相类型分析
        print(f"\n按震相类型分析:")
        for phase_type in phase_df['phase_type'].unique():
            phase_subset = phase_df[phase_df['phase_type'] == phase_type]
            print(f"  {phase_type} 波:")
            print(f"    数量: {len(phase_subset)}")
            print(f"    平均差异: {phase_subset['time_diff_seconds'].mean():.3f} 秒")
            print(f"    标准差: {phase_subset['time_diff_seconds'].std():.3f} 秒")
    
    # 3. 分析走时变化
    if os.path.exists(f"{results_dir}/travel_time_changes.csv"):
        travel_df = pd.read_csv(f"{results_dir}/travel_time_changes.csv")
        
        print("\n【3. 理论走时变化统计】")
        print("-" * 40)
        print(f"走时记录数量: {len(travel_df)}")
        print(f"走时变化 (秒):")
        print(f"  平均值: {travel_df['travel_time_change'].mean():.3f}")
        print(f"  标准差: {travel_df['travel_time_change'].std():.3f}")
        print(f"  最小值: {travel_df['travel_time_change'].min():.3f}")
        print(f"  最大值: {travel_df['travel_time_change'].max():.3f}")
        print(f"  中位数: {travel_df['travel_time_change'].median():.3f}")
        
        # 按震相类型分析走时变化
        print(f"\n按震相类型分析走时变化:")
        for phase_type in travel_df['phase_type'].unique():
            travel_subset = travel_df[travel_df['phase_type'] == phase_type]
            print(f"  {phase_type} 波:")
            print(f"    数量: {len(travel_subset)}")
            print(f"    平均变化: {travel_subset['travel_time_change'].mean():.3f} 秒")
            print(f"    标准差: {travel_subset['travel_time_change'].std():.3f} 秒")
            
        # 分析走时变化的分布
        print(f"\n走时变化分布:")
        reduced_count = (travel_df['travel_time_change'] < 0).sum()
        increased_count = (travel_df['travel_time_change'] > 0).sum()
        unchanged_count = (travel_df['travel_time_change'] == 0).sum()
        
        print(f"  走时减少: {reduced_count} ({reduced_count/len(travel_df)*100:.1f}%)")
        print(f"  走时增加: {increased_count} ({increased_count/len(travel_df)*100:.1f}%)")
        print(f"  走时不变: {unchanged_count} ({unchanged_count/len(travel_df)*100:.1f}%)")
    
    print("\n" + "="*70)
    print("分析结论：")
    print("="*70)
    
    if os.path.exists(f"{results_dir}/origin_time_differences.csv"):
        origin_df = pd.read_csv(f"{results_dir}/origin_time_differences.csv")
        avg_time_diff = origin_df['time_diff_seconds'].mean()
        avg_horizontal_dist = origin_df['horizontal_dist_km'].mean()
        
        print(f"1. 重定位后，地震发震时刻平均延迟 {avg_time_diff:.1f} 秒")
        print(f"2. 地震位置平均水平偏移 {avg_horizontal_dist:.2f} 公里")
        
    if os.path.exists(f"{results_dir}/travel_time_changes.csv"):
        travel_df = pd.read_csv(f"{results_dir}/travel_time_changes.csv")
        avg_travel_change = travel_df['travel_time_change'].mean()
        
        if avg_travel_change < 0:
            print(f"3. 重定位后，理论走时平均减少 {abs(avg_travel_change):.2f} 秒")
        else:
            print(f"3. 重定位后，理论走时平均增加 {avg_travel_change:.2f} 秒")
        
        print(f"4. 这表明重定位优化了震源位置，使理论走时更符合观测")
    
    print("\n注：正值表示重定位后时间增加，负值表示时间减少")

if __name__ == "__main__":
    analyze_timing_results()
