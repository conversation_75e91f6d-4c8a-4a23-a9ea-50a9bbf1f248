"""
Utility functions for PyTorch HypoSVI.

This module provides general utility functions including configuration management,
coordinate transformations, time utilities, and mathematical functions.

Key components:
- Config: Configuration file management
- Geodesy: Geographic coordinate transformations
- TimeUtils: Time and date utilities
- MathUtils: Mathematical utility functions
"""

from .config import Config, load_config, save_config
from .geodesy import lla_to_enu, enu_to_lla, distance_haversine, azimuth_distance
from .time_utils import datetime_to_seconds, seconds_to_datetime, parse_datetime
from .math_utils import normalize_angle, deg2rad, rad2deg, moving_average

__all__ = [
    "Config",
    "load_config",
    "save_config",
    "lla_to_enu",
    "enu_to_lla", 
    "distance_haversine",
    "azimuth_distance",
    "datetime_to_seconds",
    "seconds_to_datetime",
    "parse_datetime",
    "normalize_angle",
    "deg2rad",
    "rad2deg",
    "moving_average",
]
