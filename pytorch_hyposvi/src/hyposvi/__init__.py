"""
HypoSVI earthquake location module.

This module implements earthquake hypocenter location algorithms using
both MAP (Maximum A Posteriori) and SVI (Stein Variational Inference) methods.

Key components:
- HypoSVILocator: Main locator interface
- MAPSolver: MAP optimization solver
- SVISolver: Stein variational inference solver
- Distributions: Probability distributions for likelihood functions
- Kernels: Kernel functions for SVI
"""

from .locator import HypoSVILocator
from .map_solver import MAPSolver
from .svi_solver import SVISolver
from .distributions import HuberDensity, PGeneralizedGaussian
from .kernels import RBFKernel, IMQKernel
from .utils import get_origin_time, assign_likelihood, assign_kernel

__all__ = [
    "HypoSVILocator",
    "MAPSolver",
    "SVISolver",
    "HuberDensity",
    "PGeneralizedGaussian",
    "RBFKernel",
    "IMQKernel",
    "get_origin_time",
    "assign_likelihood",
    "assign_kernel",
]
