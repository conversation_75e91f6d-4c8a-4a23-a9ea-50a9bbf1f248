"""
Probability distributions for earthquake location.

This module implements various probability distributions used in
earthquake location, including robust distributions for handling outliers.
"""

import torch
import numpy as np
from typing import Union
from scipy.special import gamma
import math


class HuberDensity:
    """
    Huber density distribution for robust estimation.
    
    The Huber density combines Gaussian and Laplacian distributions,
    providing robustness to outliers while maintaining efficiency
    for inlier data.
    
    Args:
        delta: Threshold parameter for switching between quadratic and linear loss
    """
    
    def __init__(self, delta: float = 1.0):
        self.delta = delta
        
        # Calculate normalization constant ε
        # Based on Julia implementation
        from scipy.stats import norm
        y = 2.0 * norm.pdf(delta) / delta - 2.0 * norm.cdf(-delta)
        self.epsilon = y / (1.0 + y)
        
    def pdf(self, x: Union[float, np.ndarray, torch.Tensor]) -> Union[float, np.ndarray, torch.Tensor]:
        """
        Probability density function.
        
        Args:
            x: Input values
            
        Returns:
            Probability density values
        """
        is_torch = isinstance(x, torch.Tensor)
        if is_torch:
            x_np = x.detach().cpu().numpy()
        else:
            x_np = np.asarray(x)
            
        # Calculate Huber loss
        abs_x = np.abs(x_np)
        rho = np.where(
            abs_x < self.delta,
            0.5 * x_np**2,
            self.delta * abs_x - 0.5 * self.delta**2
        )
        
        # Calculate density
        density = (1.0 - self.epsilon) / np.sqrt(2.0 * np.pi) * np.exp(-rho)
        
        if is_torch:
            return torch.from_numpy(density).to(x.device).type(x.dtype)
        else:
            return density
            
    def log_pdf(self, x: Union[float, np.ndarray, torch.Tensor]) -> Union[float, np.ndarray, torch.Tensor]:
        """
        Log probability density function.
        
        Args:
            x: Input values
            
        Returns:
            Log probability density values
        """
        is_torch = isinstance(x, torch.Tensor)
        if is_torch:
            x_np = x.detach().cpu().numpy()
        else:
            x_np = np.asarray(x)
            
        # Calculate Huber loss
        abs_x = np.abs(x_np)
        rho = np.where(
            abs_x < self.delta,
            0.5 * x_np**2,
            self.delta * abs_x - 0.5 * self.delta**2
        )
        
        # Calculate log density
        log_density = np.log((1.0 - self.epsilon) / np.sqrt(2.0 * np.pi)) - rho
        
        if is_torch:
            return torch.from_numpy(log_density).to(x.device).type(x.dtype)
        else:
            return log_density


class PGeneralizedGaussian:
    """
    P-Generalized Gaussian distribution.
    
    A generalization of the Gaussian distribution with an additional
    shape parameter for controlling tail behavior.
    
    Args:
        loc: Location parameter (mean)
        scale: Scale parameter
        shape: Shape parameter (p)
    """
    
    def __init__(self, loc: float = 0.0, scale: float = 1.0, shape: float = 2.0):
        self.loc = loc
        self.scale = scale
        self.shape = shape
        
        # Calculate normalization constant
        self._calculate_normalization()
        
    def _calculate_normalization(self):
        """Calculate normalization constant for the distribution."""
        # Normalization constant for p-generalized Gaussian
        # C = p / (2 * scale * Γ(1/p))
        self.norm_const = self.shape / (2.0 * self.scale * gamma(1.0 / self.shape))
        
    def pdf(self, x: Union[float, np.ndarray, torch.Tensor]) -> Union[float, np.ndarray, torch.Tensor]:
        """
        Probability density function.
        
        Args:
            x: Input values
            
        Returns:
            Probability density values
        """
        is_torch = isinstance(x, torch.Tensor)
        if is_torch:
            x_np = x.detach().cpu().numpy()
        else:
            x_np = np.asarray(x)
            
        # Standardize
        z = (x_np - self.loc) / self.scale
        
        # Calculate density
        density = self.norm_const * np.exp(-np.abs(z)**self.shape)
        
        if is_torch:
            return torch.from_numpy(density).to(x.device).type(x.dtype)
        else:
            return density
            
    def log_pdf(self, x: Union[float, np.ndarray, torch.Tensor]) -> Union[float, np.ndarray, torch.Tensor]:
        """
        Log probability density function.
        
        Args:
            x: Input values
            
        Returns:
            Log probability density values
        """
        is_torch = isinstance(x, torch.Tensor)
        if is_torch:
            x_np = x.detach().cpu().numpy()
        else:
            x_np = np.asarray(x)
            
        # Standardize
        z = (x_np - self.loc) / self.scale
        
        # Calculate log density
        log_density = np.log(self.norm_const) - np.abs(z)**self.shape
        
        if is_torch:
            return torch.from_numpy(log_density).to(x.device).type(x.dtype)
        else:
            return log_density
            
    def logpdf(self, x: Union[float, np.ndarray, torch.Tensor]) -> Union[float, np.ndarray, torch.Tensor]:
        """Alias for log_pdf to match scipy interface."""
        return self.log_pdf(x)


class LaplaceDensity:
    """
    Laplace distribution for robust estimation.
    
    Args:
        loc: Location parameter
        scale: Scale parameter
    """
    
    def __init__(self, loc: float = 0.0, scale: float = 1.0):
        self.loc = loc
        self.scale = scale
        
    def log_pdf(self, x: Union[float, np.ndarray, torch.Tensor]) -> Union[float, np.ndarray, torch.Tensor]:
        """Log probability density function."""
        is_torch = isinstance(x, torch.Tensor)
        if is_torch:
            x_np = x.detach().cpu().numpy()
        else:
            x_np = np.asarray(x)
            
        # Laplace log pdf: -log(2*scale) - |x - loc| / scale
        log_density = -np.log(2.0 * self.scale) - np.abs(x_np - self.loc) / self.scale
        
        if is_torch:
            return torch.from_numpy(log_density).to(x.device).type(x.dtype)
        else:
            return log_density


class CauchyDensity:
    """
    Cauchy distribution for robust estimation.
    
    Args:
        loc: Location parameter
        scale: Scale parameter
    """
    
    def __init__(self, loc: float = 0.0, scale: float = 1.0):
        self.loc = loc
        self.scale = scale
        
    def log_pdf(self, x: Union[float, np.ndarray, torch.Tensor]) -> Union[float, np.ndarray, torch.Tensor]:
        """Log probability density function."""
        is_torch = isinstance(x, torch.Tensor)
        if is_torch:
            x_np = x.detach().cpu().numpy()
        else:
            x_np = np.asarray(x)
            
        # Cauchy log pdf: -log(π * scale) - log(1 + ((x - loc) / scale)²)
        z = (x_np - self.loc) / self.scale
        log_density = -np.log(np.pi * self.scale) - np.log(1.0 + z**2)
        
        if is_torch:
            return torch.from_numpy(log_density).to(x.device).type(x.dtype)
        else:
            return log_density


class GaussianDensity:
    """
    Gaussian (Normal) distribution.
    
    Args:
        loc: Location parameter (mean)
        scale: Scale parameter (standard deviation)
    """
    
    def __init__(self, loc: float = 0.0, scale: float = 1.0):
        self.loc = loc
        self.scale = scale
        
    def log_pdf(self, x: Union[float, np.ndarray, torch.Tensor]) -> Union[float, np.ndarray, torch.Tensor]:
        """Log probability density function."""
        is_torch = isinstance(x, torch.Tensor)
        if is_torch:
            x_np = x.detach().cpu().numpy()
        else:
            x_np = np.asarray(x)
            
        # Gaussian log pdf: -0.5 * log(2π) - log(scale) - 0.5 * ((x - loc) / scale)²
        z = (x_np - self.loc) / self.scale
        log_density = -0.5 * np.log(2.0 * np.pi) - np.log(self.scale) - 0.5 * z**2
        
        if is_torch:
            return torch.from_numpy(log_density).to(x.device).type(x.dtype)
        else:
            return log_density


def barron_loss(x: Union[float, np.ndarray, torch.Tensor], c: float = 1.0, alpha: float = 2.0) -> Union[float, np.ndarray, torch.Tensor]:
    """
    Barron loss function for robust estimation.
    
    Args:
        x: Input values
        c: Scale parameter
        alpha: Shape parameter
        
    Returns:
        Barron loss values
    """
    is_torch = isinstance(x, torch.Tensor)
    if is_torch:
        x_np = x.detach().cpu().numpy()
    else:
        x_np = np.asarray(x)
        
    # Barron loss: |α - 2| * (((x/c)² / |α - 2| + 1)^(α/2) - 1) / α
    z = x_np / c
    loss = np.abs(alpha - 2.0) * ((z**2 / np.abs(alpha - 2.0) + 1.0)**(alpha/2.0) - 1.0) / alpha
    
    if is_torch:
        return torch.from_numpy(loss).to(x.device).type(x.dtype)
    else:
        return loss
