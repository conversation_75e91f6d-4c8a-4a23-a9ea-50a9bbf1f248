#!/usr/bin/env python3
"""
EikoNet训练运行脚本

提供简单的命令行界面来选择训练模式
"""

import subprocess
import sys
from pathlib import Path


def main():
    """主函数"""
    print("=" * 60)
    print("EikoNet 训练脚本")
    print("=" * 60)
    print()
    print("请选择训练模式:")
    print("1. 测试模式 (10轮训练，快速验证)")
    print("2. 完整训练模式 (100轮训练)")
    print("3. 退出")
    print()
    
    while True:
        try:
            choice = input("请输入选择 (1/2/3): ").strip()
            
            if choice == '1':
                print("\n启动测试模式...")
                cmd = [sys.executable, "train_eikonet_simple.py", "--test"]
                break
            elif choice == '2':
                print("\n启动完整训练模式...")
                cmd = [sys.executable, "train_eikonet_simple.py"]
                break
            elif choice == '3':
                print("退出程序")
                return
            else:
                print("无效选择，请输入 1、2 或 3")
                continue
                
        except KeyboardInterrupt:
            print("\n\n程序被用户中断")
            return
    
    # 运行训练脚本
    try:
        result = subprocess.run(cmd, check=True)
        print(f"\n训练完成，退出码: {result.returncode}")
    except subprocess.CalledProcessError as e:
        print(f"\n训练失败，退出码: {e.returncode}")
    except KeyboardInterrupt:
        print("\n\n训练被用户中断")


if __name__ == "__main__":
    main()
