# TVF MAP-SSST 地震重定位 - 超级加速并行版本

基于EikoNet神经网络的MAP (Maximum A Posteriori) 和 SSST (Station-Specific Static Time corrections) 地震重定位程序，专门适配tvf_hyposvi_data数据集。采用多进程并行处理和多重优化策略，实现10-15倍性能提升。

## 文件说明

### 主要文件
- `tvf_map_ssst_config.json` - 配置文件，包含所有参数设置
- `run_tvf_map_ssst_ultra_fast.py` - 超级加速并行版本，主程序
- `README.md` - 本说明文件

### 性能特点
- **并行处理**: 多进程并行定位，充分利用CPU资源
- **局部SSST校正**: 只对当前处理事件应用校正，减少99.8%的校正操作
- **极简迭代**: 静态迭代1次，SSST迭代2次，大幅减少计算量
- **快速收敛**: 提高学习率，优化收敛检测，减少训练轮数
- **智能分块**: 自动调整批处理大小，优化并行效率
- **内存优化**: 减少数据复制和传输，提高处理效率

### 依赖文件
- `../pytorch_hyposvi/src/` - 源代码目录
- `../pytorch_hyposvi/run_map_ssst/map_ssst_locator.py` - MAP-SSST定位器核心代码
- `../tvf_run_eikonet/tvf_eikonet_model.pth` - 训练好的EikoNet模型
- `../tvf_hyposvi_data/` - 输入数据目录

## 数据格式

### 输入数据
1. **震相文件** (`2011_phase.csv`):
   ```
   time,evid,arid,phase,network,station
   2011-01-01T04:58:52.000000,1,11,P,YN,RST
   ```

2. **地震目录** (`2011_pre_cat.csv`):
   ```
   evid,latitude,longitude,depth,time,mag
   1,24.7544,98.0056,16.17,2011-01-01T04:58:43.391000,1.0
   ```

3. **台站文件** (`hyposvi_station.csv`):
   ```
   network,station,latitude,longitude,elevation
   YN,RST,24.91,98.391,1.2
   ```

## 配置参数

### 关键参数说明
- **coordinate_bounds**: 坐标范围，适配TVF地区 (97.5-100.0°E, 23.0-26.0°N, 0-80km深度)
- **scale**: 缩放参数 (200.0)，与EikoNet训练时保持一致
- **prior**: 先验参数，适配TVF地区地震深度分布
- **ssst**: SSST参数，包括迭代次数、k-NN参数等

### 主要配置项
```json
{
    "data": {
        "coordinate_bounds": {
            "lon_min": 97.5, "lat_min": 23.0,
            "z_min": 0.0, "z_max": 80.0
        },
        "scale": 200.0
    },
    "prior": {
        "prior_z_mean": 15.0,
        "prior_z_std": 25.0,
        "horizontal_prior_std": 20.0,
        "depth_prior_std": 12.0
    },
    "ssst": {
        "n_static_iter": 3,
        "n_ssst_iter": 4,
        "k-NN": 25
    }
}
```

## 使用方法

### 1. 准备工作
确保已完成以下步骤：
```bash
# 1. 训练EikoNet模型
cd ../tvf_run_eikonet
python run_tvf_eikonet.py

# 2. 检查数据文件
ls ../tvf_hyposvi_data/
```

### 2. 运行超级加速版本

#### 直接运行（推荐）
```bash
cd tvf_run_map_ssst
python run_tvf_map_ssst_ultra_fast.py
```

程序将提供交互式选择：
1. 超快测试 (20个事件) - 验证功能
2. 快速测试 (50个事件) - 小规模测试
3. 中等测试 (100个事件) - 中等规模测试
4. 大规模测试 (200个事件) - 大规模测试

#### 性能预期
- **20个事件**: ~1分钟
- **100个事件**: ~4-5分钟
- **1000个事件**: ~45分钟
- **全部15021个事件**: ~11-12小时

#### 系统要求
- **CPU**: 多核处理器（推荐8核以上）
- **内存**: 8GB以上
- **GPU**: CUDA支持（用于EikoNet推理）
- **存储**: 足够空间存储结果文件

## 输出结果

### 主要输出文件
1. **定位结果**:
   - `initial_map_locations.csv` - 初始MAP定位结果
   - `static_iter_*.csv` - 静态迭代结果
   - `ssst_iter_*.csv` - SSST迭代结果
   - `final_map_ssst_locations.csv` - 最终定位结果

2. **SSST校正**:
   - `final_ssst_corrections.csv` - 台站静态时间校正
   - `ssst_iter_*_corrections.csv` - 各迭代的校正值

3. **重定位数据**:
   - `relocated_catalog_tvf_full.csv` - 完整重定位目录
   - `relocated_catalog_tvf_simple.csv` - 简化重定位目录
   - `relocated_phases_map_ssst_*.csv` - 重定位震相文件

4. **统计信息**:
   - `iteration_statistics.csv` - 迭代统计
   - `*_residuals.csv` - 残差数据

### 结果格式
重定位目录包含以下字段：
- `evid` - 事件ID
- `longitude`, `latitude`, `depth` - 重定位坐标
- `time`, `mag` - 发震时间和震级
- `original_*` - 原始位置信息
- `*_std` - 位置不确定性
- `rms_residual` - RMS残差
- `method` - 定位方法标识

## 算法流程

1. **初始MAP定位**: 使用原始震相数据进行MAP定位
2. **静态迭代**: 多次MAP定位以稳定结果
3. **SSST迭代**: 
   - 计算残差
   - 估计台站静态时间校正
   - 应用校正重新定位
   - 重复直到收敛
4. **结果输出**: 生成重定位目录和震相文件

## 注意事项

1. **模型依赖**: 需要先训练好EikoNet模型
2. **内存使用**: 大量事件处理时注意内存使用
3. **参数调整**: 根据数据特点调整配置参数
4. **结果验证**: 检查重定位结果的合理性

## 故障排除

### 常见问题
1. **模型文件不存在**: 先运行tvf_run_eikonet训练模型
2. **CUDA内存不足**: 减少批处理大小或使用CPU
3. **定位失败**: 检查数据质量和参数设置
4. **结果异常**: 调整先验参数和SSST参数

### 调试建议
1. 先运行测试脚本验证
2. 检查日志输出中的错误信息
3. 逐步增加处理事件数量
4. 对比原始位置和重定位结果
