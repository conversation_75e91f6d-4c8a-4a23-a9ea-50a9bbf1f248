# PyTorch HypoSVI and EikoNet

PyTorch重构版本的HypoSVI地震定位系统和EikoNet神经网络，基于Julia原版代码重构而来。

## 项目结构

```
pytorch_hyposvi/
├── README.md                    # 项目说明文档
├── requirements.txt             # Python依赖包
├── setup.py                     # 安装脚本
├── config/                      # 配置文件目录
│   ├── eikonet_config.json     # EikoNet训练配置
│   └── hyposvi_config.json     # HypoSVI定位配置
├── src/                         # 源代码目录
│   ├── __init__.py
│   ├── eikonet/                # EikoNet神经网络模块
│   │   ├── __init__.py
│   │   ├── models.py           # EikoNet网络架构
│   │   ├── layers.py           # 自定义层（CylindricalSymmetry等）
│   │   ├── loss.py             # Eikonal损失函数
│   │   ├── trainer.py          # 训练器
│   │   └── utils.py            # 工具函数
│   ├── hyposvi/                # HypoSVI地震定位模块
│   │   ├── __init__.py
│   │   ├── locator.py          # 地震定位主类
│   │   ├── map_solver.py       # MAP方法求解器
│   │   ├── svi_solver.py       # SVI方法求解器
│   │   ├── kernels.py          # 核函数实现
│   │   ├── distributions.py    # 概率分布
│   │   └── utils.py            # 工具函数
│   ├── data/                   # 数据处理模块
│   │   ├── __init__.py
│   │   ├── loader.py           # 数据加载器
│   │   ├── preprocessor.py     # 数据预处理
│   │   ├── transforms.py       # 坐标变换
│   │   └── velocity_model.py   # 速度模型
│   ├── utils/                  # 通用工具模块
│   │   ├── __init__.py
│   │   ├── config.py           # 配置管理
│   │   ├── geodesy.py          # 地理坐标处理
│   │   ├── time_utils.py       # 时间处理
│   │   └── math_utils.py       # 数学工具
│   └── visualization/          # 可视化模块
│       ├── __init__.py
│       ├── plots.py            # 绘图函数
│       └── results.py          # 结果可视化
├── tests/                      # 测试目录
│   ├── __init__.py
│   ├── test_eikonet/          # EikoNet测试
│   ├── test_hyposvi/          # HypoSVI测试
│   ├── test_data/             # 数据处理测试
│   └── test_utils/            # 工具测试
├── examples/                   # 示例代码
│   ├── train_eikonet.py       # EikoNet训练示例
│   ├── locate_events.py       # 地震定位示例
│   └── notebooks/             # Jupyter笔记本示例
└── docs/                      # 文档目录
    ├── api/                   # API文档
    ├── tutorials/             # 教程
    └── installation.md        # 安装指南
```

## 核心模块设计

### 1. EikoNet神经网络模块
- **models.py**: EikoNet主网络架构，包含τ0和τ1计算
- **layers.py**: CylindricalSymmetry层、PosEncoding层等自定义层
- **loss.py**: Eikonal PDE损失函数实现
- **trainer.py**: 网络训练逻辑

### 2. HypoSVI地震定位模块
- **locator.py**: 统一的地震定位接口
- **map_solver.py**: MAP（最大后验）方法实现
- **svi_solver.py**: SVI（Stein变分推断）方法实现
- **kernels.py**: RBF、IMQ等核函数
- **distributions.py**: Huber、Laplace等概率分布

### 3. 数据处理模块
- **loader.py**: 地震数据、台站数据加载
- **preprocessor.py**: 数据预处理和格式转换
- **transforms.py**: 地理坐标变换（LLA↔ENU）
- **velocity_model.py**: 1D速度模型处理

### 4. 工具模块
- **config.py**: JSON配置文件管理
- **geodesy.py**: 地理坐标系统转换
- **time_utils.py**: 时间格式转换
- **math_utils.py**: 数学计算工具

## 主要特性

1. **完全兼容Julia版本**: 保持相同的算法逻辑和数学实现
2. **CUDA加速支持**: 充分利用GPU计算能力
3. **模块化设计**: 清晰的代码结构，易于维护和扩展
4. **类型提示**: 完整的Python类型注解
5. **全面测试**: 单元测试和集成测试覆盖
6. **详细文档**: API文档和使用教程

## 依赖关系

- PyTorch >= 2.0
- NumPy
- SciPy
- Pandas
- Matplotlib
- Seaborn
- PyProj (地理坐标转换)
- Scikit-learn
- Tqdm (进度条)

## 安装和使用

详见 `docs/installation.md` 和示例代码。
