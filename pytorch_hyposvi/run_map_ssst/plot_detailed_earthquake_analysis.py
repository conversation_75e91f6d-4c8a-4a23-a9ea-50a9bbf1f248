#!/usr/bin/env python3
"""
Detailed earthquake relocation analysis with multiple visualization types
Creates comprehensive plots showing relocation effects and statistics
"""

import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import os
from matplotlib.patches import Ellipse
import seaborn as sns

def load_earthquake_data():
    """Load original and relocated earthquake catalogs"""
    # Load original catalog
    original_file = '/home/<USER>/run_eikonet_hyposvi/hyposvi/pre_cat.csv'
    original_df = pd.read_csv(original_file)
    
    # Load relocated catalog
    relocated_file = '/home/<USER>/run_eikonet_hyposvi/pytorch_hyposvi/run_map_ssst/map_ssst_optimized_results/relocated_catalog_optimized_simple.csv'
    relocated_df = pd.read_csv(relocated_file)
    
    return original_df, relocated_df

def create_displacement_analysis(original_df, relocated_df, output_dir):
    """Create displacement analysis plots"""
    
    # Calculate displacements
    lon_diff = relocated_df['longitude'].values - original_df['longitude'].values
    lat_diff = relocated_df['latitude'].values - original_df['latitude'].values
    depth_diff = relocated_df['depth'].values - original_df['depth'].values
    
    # Convert to approximate distances (km)
    lon_dist_km = lon_diff * 111.0 * np.cos(np.radians(original_df['latitude'].mean()))
    lat_dist_km = lat_diff * 111.0
    horizontal_dist_km = np.sqrt(lon_dist_km**2 + lat_dist_km**2)
    
    # Create figure with subplots
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('Earthquake Relocation Displacement Analysis', fontsize=16, fontweight='bold')
    
    # Plot 1: Horizontal displacement vectors
    ax1 = axes[0, 0]
    quiver = ax1.quiver(original_df['longitude'], original_df['latitude'], 
                       lon_diff*1000, lat_diff*1000, horizontal_dist_km, 
                       scale=1, scale_units='xy', angles='xy', alpha=0.7, cmap='viridis')
    ax1.set_xlabel('Longitude (°)')
    ax1.set_ylabel('Latitude (°)')
    ax1.set_title('Horizontal Displacement Vectors\n(arrows scaled 1000x)')
    ax1.grid(True, alpha=0.3)
    plt.colorbar(quiver, ax=ax1, label='Horizontal Distance (km)')
    
    # Plot 2: Displacement magnitude histogram
    ax2 = axes[0, 1]
    ax2.hist(horizontal_dist_km, bins=30, alpha=0.7, color='skyblue', edgecolor='black')
    ax2.axvline(horizontal_dist_km.mean(), color='red', linestyle='--', 
                label=f'Mean: {horizontal_dist_km.mean():.3f} km')
    ax2.set_xlabel('Horizontal Displacement (km)')
    ax2.set_ylabel('Number of Events')
    ax2.set_title('Horizontal Displacement Distribution')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # Plot 3: Depth change histogram
    ax3 = axes[0, 2]
    ax3.hist(depth_diff, bins=30, alpha=0.7, color='lightcoral', edgecolor='black')
    ax3.axvline(depth_diff.mean(), color='red', linestyle='--', 
                label=f'Mean: {depth_diff.mean():.3f} km')
    ax3.set_xlabel('Depth Change (km)')
    ax3.set_ylabel('Number of Events')
    ax3.set_title('Depth Change Distribution')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # Plot 4: East-West vs North-South displacement
    ax4 = axes[1, 0]
    scatter = ax4.scatter(lon_dist_km, lat_dist_km, c=horizontal_dist_km, 
                         cmap='viridis', alpha=0.7, s=20)
    ax4.set_xlabel('East-West Displacement (km)')
    ax4.set_ylabel('North-South Displacement (km)')
    ax4.set_title('Horizontal Displacement Components')
    ax4.grid(True, alpha=0.3)
    ax4.axhline(0, color='black', linestyle='-', alpha=0.3)
    ax4.axvline(0, color='black', linestyle='-', alpha=0.3)
    plt.colorbar(scatter, ax=ax4, label='Total Horizontal Distance (km)')
    
    # Plot 5: Displacement vs original depth
    ax5 = axes[1, 1]
    ax5.scatter(original_df['depth'], horizontal_dist_km, alpha=0.6, s=15)
    ax5.set_xlabel('Original Depth (km)')
    ax5.set_ylabel('Horizontal Displacement (km)')
    ax5.set_title('Displacement vs Original Depth')
    ax5.grid(True, alpha=0.3)
    
    # Plot 6: Displacement statistics by depth bins
    ax6 = axes[1, 2]
    depth_bins = np.linspace(0, 15, 6)
    bin_centers = (depth_bins[:-1] + depth_bins[1:]) / 2
    bin_means = []
    bin_stds = []
    
    for i in range(len(depth_bins)-1):
        mask = (original_df['depth'] >= depth_bins[i]) & (original_df['depth'] < depth_bins[i+1])
        if np.sum(mask) > 0:
            bin_means.append(horizontal_dist_km[mask].mean())
            bin_stds.append(horizontal_dist_km[mask].std())
        else:
            bin_means.append(0)
            bin_stds.append(0)
    
    ax6.errorbar(bin_centers, bin_means, yerr=bin_stds, marker='o', capsize=5)
    ax6.set_xlabel('Depth Bin Center (km)')
    ax6.set_ylabel('Mean Horizontal Displacement (km)')
    ax6.set_title('Mean Displacement by Depth')
    ax6.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # Save the plot
    output_file = os.path.join(output_dir, 'earthquake_displacement_detailed_analysis.png')
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    print(f"Detailed displacement analysis saved to: {output_file}")
    plt.close()

def create_statistical_summary(original_df, relocated_df, output_dir):
    """Create statistical summary plots"""
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('Earthquake Catalog Statistical Comparison', fontsize=16, fontweight='bold')
    
    # Plot 1: Depth distribution comparison
    ax1 = axes[0, 0]
    ax1.hist(original_df['depth'], bins=30, alpha=0.6, label='Original', color='blue', density=True)
    ax1.hist(relocated_df['depth'], bins=30, alpha=0.6, label='Relocated', color='red', density=True)
    ax1.set_xlabel('Depth (km)')
    ax1.set_ylabel('Probability Density')
    ax1.set_title('Depth Distribution Comparison')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # Plot 2: Longitude distribution comparison
    ax2 = axes[0, 1]
    ax2.hist(original_df['longitude'], bins=30, alpha=0.6, label='Original', color='blue', density=True)
    ax2.hist(relocated_df['longitude'], bins=30, alpha=0.6, label='Relocated', color='red', density=True)
    ax2.set_xlabel('Longitude (°)')
    ax2.set_ylabel('Probability Density')
    ax2.set_title('Longitude Distribution Comparison')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # Plot 3: Latitude distribution comparison
    ax3 = axes[1, 0]
    ax3.hist(original_df['latitude'], bins=30, alpha=0.6, label='Original', color='blue', density=True)
    ax3.hist(relocated_df['latitude'], bins=30, alpha=0.6, label='Relocated', color='red', density=True)
    ax3.set_xlabel('Latitude (°)')
    ax3.set_ylabel('Probability Density')
    ax3.set_title('Latitude Distribution Comparison')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # Plot 4: Correlation matrix
    ax4 = axes[1, 1]
    
    # Calculate differences
    lon_diff = relocated_df['longitude'].values - original_df['longitude'].values
    lat_diff = relocated_df['latitude'].values - original_df['latitude'].values
    depth_diff = relocated_df['depth'].values - original_df['depth'].values
    
    # Create correlation data
    corr_data = pd.DataFrame({
        'Original_Lon': original_df['longitude'],
        'Original_Lat': original_df['latitude'],
        'Original_Depth': original_df['depth'],
        'Lon_Change': lon_diff,
        'Lat_Change': lat_diff,
        'Depth_Change': depth_diff
    })
    
    correlation_matrix = corr_data.corr()
    im = ax4.imshow(correlation_matrix, cmap='RdBu_r', vmin=-1, vmax=1)
    ax4.set_xticks(range(len(correlation_matrix.columns)))
    ax4.set_yticks(range(len(correlation_matrix.columns)))
    ax4.set_xticklabels(correlation_matrix.columns, rotation=45, ha='right')
    ax4.set_yticklabels(correlation_matrix.columns)
    ax4.set_title('Parameter Correlation Matrix')
    
    # Add correlation values to the plot
    for i in range(len(correlation_matrix.columns)):
        for j in range(len(correlation_matrix.columns)):
            text = ax4.text(j, i, f'{correlation_matrix.iloc[i, j]:.2f}',
                           ha="center", va="center", color="black", fontsize=8)
    
    plt.colorbar(im, ax=ax4, label='Correlation Coefficient')
    
    plt.tight_layout()
    
    # Save the plot
    output_file = os.path.join(output_dir, 'earthquake_statistical_summary.png')
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    print(f"Statistical summary saved to: {output_file}")
    plt.close()

def main():
    """Main function"""
    print("Loading earthquake catalogs for detailed analysis...")
    
    # Load data
    original_df, relocated_df = load_earthquake_data()
    
    # Set output directory
    output_dir = '/home/<USER>/run_eikonet_hyposvi/pytorch_hyposvi/run_map_ssst'
    
    # Create detailed analysis plots
    print("Creating detailed displacement analysis...")
    create_displacement_analysis(original_df, relocated_df, output_dir)
    
    print("Creating statistical summary...")
    create_statistical_summary(original_df, relocated_df, output_dir)
    
    print("\nDetailed analysis completed successfully!")
    print(f"All plots saved to: {output_dir}")

if __name__ == "__main__":
    main()
