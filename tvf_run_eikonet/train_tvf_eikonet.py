#!/usr/bin/env python3
"""
TVF EikoNet训练脚本 - PyTorch版本

使用TVF数据训练EikoNet神经网络模型，用于地震走时预测。
包含速度差异记录和可视化功能。
"""

import os
import sys
import json
import time
import torch
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, List, Tuple

# 添加src路径到Python路径
current_dir = Path(__file__).parent
src_dir = current_dir.parent / "pytorch_hyposvi" / "src"
sys.path.insert(0, str(src_dir))

from eikonet.models import EikoNet
from eikonet.trainer import EikoNetTrainer
from eikonet.loss import EikonalLoss, get_loss_function
from eikonet.utils import eikonal_pde
from data.velocity_model import VelocityModel1D, build_1d_training_data
from data.loader import StationDataLoader
from utils.config import load_config
from utils.logging import setup_logging


def load_tvf_config(config_path: str) -> dict:
    """
    加载TVF配置文件
    
    Args:
        config_path: 配置文件路径
        
    Returns:
        配置字典
    """
    with open(config_path, 'r') as f:
        config = json.load(f)
    
    print(f"TVF配置已加载:")
    print(f"  台站文件: {config['data']['station_file']}")
    print(f"  速度模型文件: {config['data']['velmod_file']}")
    print(f"  坐标边界: {config['data']['coordinate_bounds']}")
    print(f"  训练样本数: {config['training']['n_train']}")
    print(f"  测试样本数: {config['training']['n_test']}")
    print(f"  训练轮次: {config['training']['n_epochs']}")
    
    return config


def create_coordinate_bounds(config: dict) -> dict:
    """
    从配置创建坐标边界字典
    
    Args:
        config: 配置字典
        
    Returns:
        坐标边界字典
    """
    bounds = config['data']['coordinate_bounds']
    return {
        'lon_min': bounds['lon_min'],
        'lon_max': bounds['lon_max'],
        'lat_min': bounds['lat_min'],
        'lat_max': bounds['lat_max'],
        'z_min': bounds['z_min'],
        'z_max': bounds['z_max']
    }


def plot_training_history(history: dict, save_path: str):
    """
    绘制训练历史
    
    Args:
        history: 训练历史字典
        save_path: 保存路径
    """
    fig, ax = plt.subplots(1, 1, figsize=(10, 6))
    
    epochs = range(1, len(history['train_losses']) + 1)
    ax.plot(epochs, history['train_losses'], 'b-', label='Training Loss', linewidth=2)
    
    if history['val_losses'] and len(history['val_losses']) > 0:
        ax.plot(epochs, history['val_losses'], 'r-', label='Validation Loss', linewidth=2)
    
    ax.set_xlabel('Epoch')
    ax.set_ylabel('Loss')
    ax.set_title('TVF EikoNet Training History')
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"训练历史图已保存到: {save_path}")


class VelocityComparisonTracker:
    """
    速度对比跟踪器，用于每个epoch绘制速度对比图
    """

    def __init__(self, velocity_model: VelocityModel1D, coordinate_bounds: dict,
                 scale: float, device: torch.device, plot_dir: str):
        self.velocity_model = velocity_model
        self.coordinate_bounds = coordinate_bounds
        self.scale = scale
        self.device = device
        self.plot_dir = Path(plot_dir)
        self.plot_dir.mkdir(exist_ok=True)

        # 创建测试深度
        z_min = coordinate_bounds['z_min']
        z_max = coordinate_bounds['z_max']
        self.depths = np.linspace(z_min, z_max, 100)
        self.test_data = self._create_test_data()

    def _create_test_data(self) -> torch.Tensor:
        """创建用于测试的固定数据点"""
        n_test = len(self.depths)
        x_test = torch.zeros(7, n_test, device=self.device)

        # 固定源位置（中心点）
        x_test[0, :] = 0.5  # x_src
        x_test[1, :] = 0.5  # y_src
        x_test[2, :] = torch.tensor(self.depths / self.scale, device=self.device)  # z_src

        # 固定接收器位置（稍微偏移）
        x_test[3, :] = 0.6  # x_rec
        x_test[4, :] = 0.6  # y_rec
        x_test[5, :] = torch.tensor(self.depths / self.scale, device=self.device)  # z_rec

        return x_test

    def plot_epoch_comparison(self, model: torch.nn.Module, epoch: int):
        """
        绘制当前epoch的速度对比图

        Args:
            model: 训练中的模型
            epoch: 当前轮次
        """
        model.eval()

        # 测试P波和S波
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

        for phase_idx, (phase_name, phase_val) in enumerate([('P', 0), ('S', 1)]):
            ax = ax1 if phase_idx == 0 else ax2

            # 设置相位
            x_test = self.test_data.clone()
            x_test[6, :] = phase_val

            # 获取真实速度
            true_velocities = []
            for depth in self.depths:
                vel = self.velocity_model.get_velocity(depth, phase_name)
                true_velocities.append(vel)
            true_velocities = np.array(true_velocities)

            # 获取模型预测的走时
            with torch.no_grad():
                predicted_times = model(x_test)

            # 从预测走时反推速度
            distances = torch.sqrt(torch.sum((x_test[3:6] - x_test[0:3])**2, dim=0)) * self.scale
            predicted_velocities = (distances / predicted_times.squeeze()).cpu().numpy()

            # 绘图
            ax.plot(true_velocities, self.depths, 'b-', linewidth=2, label=f'True {phase_name}-wave')
            ax.plot(predicted_velocities, self.depths, 'r--', linewidth=2, label=f'Predicted {phase_name}-wave')

            ax.set_xlabel(f'{phase_name}-wave Velocity (km/s)')
            ax.set_ylabel('Depth (km)')
            ax.set_title(f'TVF {phase_name}-wave Velocity (Epoch {epoch+1})')
            ax.invert_yaxis()
            ax.legend()
            ax.grid(True, alpha=0.3)

        plt.tight_layout()
        save_path = self.plot_dir / f"velocity_comparison_epoch_{epoch+1:03d}.png"
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()


def plot_velocity_comparison(velocity_model: VelocityModel1D, model: EikoNet,
                           coordinate_bounds: dict, scale: float, save_path: str):
    """
    绘制速度模型对比图（预测vs真实）

    Args:
        velocity_model: 真实速度模型
        model: 训练好的EikoNet模型
        coordinate_bounds: 坐标边界
        scale: 缩放因子
        save_path: 保存路径
    """
    device = next(model.parameters()).device

    # 创建测试深度
    z_min = coordinate_bounds['z_min']
    z_max = coordinate_bounds['z_max']
    depths = np.linspace(z_min, z_max, 100)

    # 创建测试数据点（固定源和接收器位置，变化深度）
    n_test = len(depths)
    x_test = torch.zeros(7, n_test, device=device)

    # 固定源位置（中心点）
    x_test[0, :] = 0.5  # x_src
    x_test[1, :] = 0.5  # y_src
    x_test[2, :] = torch.tensor(depths / scale, device=device)  # z_src (normalized)

    # 固定接收器位置（稍微偏移）
    x_test[3, :] = 0.6  # x_rec
    x_test[4, :] = 0.6  # y_rec
    x_test[5, :] = torch.tensor(depths / scale, device=device)  # z_rec (normalized)

    # 测试P波和S波
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

    for phase_idx, (phase_name, phase_val) in enumerate([('P', 0), ('S', 1)]):
        ax = ax1 if phase_idx == 0 else ax2

        # 设置相位
        x_test[6, :] = phase_val

        # 获取真实速度
        true_velocities = []
        for depth in depths:
            vel = velocity_model.get_velocity(depth, phase_name)
            true_velocities.append(vel)
        true_velocities = np.array(true_velocities)

        # 获取模型预测的走时
        model.eval()
        with torch.no_grad():
            predicted_times = model(x_test)

        # 从预测走时反推速度（简化计算）
        distances = torch.sqrt(torch.sum((x_test[3:6] - x_test[0:3])**2, dim=0)) * scale
        predicted_velocities = (distances / predicted_times.squeeze()).cpu().numpy()

        # 绘图
        ax.plot(true_velocities, depths, 'b-', linewidth=2, label=f'True {phase_name}-wave')
        ax.plot(predicted_velocities, depths, 'r--', linewidth=2, label=f'Predicted {phase_name}-wave')

        ax.set_xlabel(f'{phase_name}-wave Velocity (km/s)')
        ax.set_ylabel('Depth (km)')
        ax.set_title(f'TVF {phase_name}-wave Velocity Comparison')
        ax.invert_yaxis()
        ax.legend()
        ax.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()

    print(f"速度对比图已保存到: {save_path}")


def train_with_velocity_tracking(
    trainer: EikoNetTrainer,
    train_loader,
    val_loader,
    velocity_tracker: VelocityComparisonTracker,
    n_epochs: int,
    save_path: str,
    checkpoint_interval: int = 10,
    early_stopping_patience: int = 20,
    plot_interval: int = 10,
    verbose: bool = True
) -> Dict[str, Any]:
    """
    带速度跟踪的训练函数

    Args:
        trainer: EikoNet训练器
        train_loader: 训练数据加载器
        val_loader: 验证数据加载器
        velocity_tracker: 速度对比跟踪器
        n_epochs: 训练轮次
        save_path: 模型保存路径
        checkpoint_interval: 检查点保存间隔
        early_stopping_patience: 早停耐心值
        plot_interval: 绘图间隔
        verbose: 是否详细输出

    Returns:
        训练历史字典
    """
    start_time = time.time()
    best_val_loss = float('inf')
    patience_counter = 0

    train_losses = []
    val_losses = []

    print(f"开始训练，总共 {n_epochs} 个epoch")
    print(f"每 {plot_interval} 个epoch绘制速度对比图")

    for epoch in range(n_epochs):
        epoch_start_time = time.time()

        # 训练一个epoch
        train_loss = trainer.train_epoch(train_loader, verbose=False)
        train_losses.append(train_loss)

        # 验证
        val_loss = None
        if val_loader is not None:
            val_loss = trainer.validate(val_loader)
            val_losses.append(val_loss)

        # 计算epoch时间
        epoch_time = time.time() - epoch_start_time

        # 估算剩余时间
        elapsed_time = time.time() - start_time
        avg_epoch_time = elapsed_time / (epoch + 1)
        remaining_epochs = n_epochs - epoch - 1
        estimated_remaining_time = avg_epoch_time * remaining_epochs

        # 输出进度
        if verbose:
            loss_str = f"Train Loss: {train_loss:.6f}"
            if val_loss is not None:
                loss_str += f", Val Loss: {val_loss:.6f}"

            print(f"Epoch {epoch+1:3d}/{n_epochs} | {loss_str} | "
                  f"Time: {epoch_time:.1f}s | "
                  f"ETA: {estimated_remaining_time/60:.1f}min")

        # 绘制速度对比图
        if (epoch + 1) % plot_interval == 0 or epoch == 0:
            velocity_tracker.plot_epoch_comparison(trainer.model, epoch)
            if verbose:
                print(f"  -> 速度对比图已保存 (Epoch {epoch+1})")

        # 检查最佳模型
        current_loss = val_loss if val_loss is not None else train_loss
        if current_loss < best_val_loss:
            best_val_loss = current_loss
            patience_counter = 0

            # 保存最佳模型
            if save_path is not None:
                trainer.save_model(save_path)
                if verbose and epoch > 0:
                    print(f"  -> 新的最佳模型已保存 (Loss: {best_val_loss:.6f})")
        else:
            patience_counter += 1

        # 早停检查
        if patience_counter >= early_stopping_patience:
            print(f"早停触发 (patience: {early_stopping_patience})")
            break

        # 保存检查点
        if (epoch + 1) % checkpoint_interval == 0:
            checkpoint_path = save_path.replace('.pth', f'_checkpoint_epoch_{epoch+1}.pth')
            trainer.save_model(checkpoint_path)
            if verbose:
                print(f"  -> 检查点已保存: {checkpoint_path}")

    total_time = time.time() - start_time

    # 构建历史字典
    history = {
        'train_losses': train_losses,
        'val_losses': val_losses,
        'best_loss': best_val_loss,
        'total_time': total_time,
        'n_epochs_trained': len(train_losses)
    }

    print(f"\n训练完成!")
    print(f"总训练时间: {total_time/60:.1f} 分钟")
    print(f"最佳损失: {best_val_loss:.6f}")

    return history


def main():
    """主训练函数"""
    # 设置路径
    config_path = "tvf_eikonet_config.json"
    output_dir = "."
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    plots_dir = os.path.join(output_dir, "plots")
    os.makedirs(plots_dir, exist_ok=True)
    
    print("=" * 60)
    print("TVF EikoNet 训练脚本 - PyTorch版本")
    print("=" * 60)
    
    # 加载配置
    print("\n1. 加载TVF配置文件...")
    config = load_tvf_config(config_path)
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() and config['device']['use_cuda'] else 'cpu')
    print(f"\n使用设备: {device}")
    if device.type == 'cuda':
        print(f"GPU: {torch.cuda.get_device_name()}")
        print(f"GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
    
    # 加载速度模型
    print("\n2. 加载TVF速度模型...")
    velocity_model = VelocityModel1D.from_csv(config['data']['velmod_file'])
    print(f"速度模型: {velocity_model}")
    
    # 绘制速度模型
    vel_plot_path = os.path.join(output_dir, "tvf_velocity_model.png")
    velocity_model.plot_model(save_path=vel_plot_path, max_depth=config['data']['coordinate_bounds']['z_max'])
    
    # 加载台站数据（用于验证）
    print("\n3. 加载TVF台站数据...")
    station_loader = StationDataLoader(config['data']['station_file'])
    print(f"加载了 {len(station_loader.data)} 个台站")
    
    # 创建坐标边界
    coordinate_bounds = create_coordinate_bounds(config)
    scale = config['data']['scale']
    
    print(f"\n坐标边界:")
    for key, value in coordinate_bounds.items():
        print(f"  {key}: {value}")
    print(f"缩放因子: {scale}")
    
    # 生成训练数据
    print("\n4. 生成训练数据...")
    train_loader, test_loader = build_1d_training_data(
        velocity_model=velocity_model,
        n_train=config['training']['n_train'],
        n_test=config['training']['n_test'],
        coordinate_bounds=coordinate_bounds,
        scale=scale,
        device=device
    )
    
    print(f"训练样本: {config['training']['n_train']}")
    print(f"测试样本: {config['training']['n_test']}")
    
    # 创建模型
    print("\n5. 创建TVF EikoNet模型...")
    model = EikoNet(scale=scale)
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 创建速度对比跟踪器
    velocity_tracker = VelocityComparisonTracker(
        velocity_model=velocity_model,
        coordinate_bounds=coordinate_bounds,
        scale=scale,
        device=device,
        plot_dir=plots_dir
    )

    # 创建训练器
    loss_fn = EikonalLoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=config['training']['lr'])

    trainer = EikoNetTrainer(
        model=model,
        loss_fn=loss_fn,
        optimizer=optimizer,
        device=device,
        scale=scale
    )
    
    # 开始训练
    print(f"\n6. 开始训练 ({config['training']['n_epochs']} epochs)...")
    model_save_path = os.path.join(output_dir, config['output']['model_save_path'])

    # 自定义训练循环，支持每个epoch绘制速度对比图
    history = train_with_velocity_tracking(
        trainer=trainer,
        train_loader=train_loader,
        val_loader=test_loader,
        velocity_tracker=velocity_tracker,
        n_epochs=config['training']['n_epochs'],
        save_path=model_save_path,
        checkpoint_interval=config['training']['checkpoint_interval'],
        early_stopping_patience=config['training']['early_stopping']['patience'],
        plot_interval=config['logging']['plot_interval'],
        verbose=True
    )
    
    # 保存训练历史
    print("\n7. 保存结果...")
    
    # 绘制训练历史
    history_plot_path = os.path.join(output_dir, "tvf_training_history.png")
    plot_training_history(history, history_plot_path)
    
    # 绘制速度对比图
    comparison_plot_path = os.path.join(output_dir, "tvf_velocity_comparison.pdf")
    plot_velocity_comparison(velocity_model, model, coordinate_bounds, scale, comparison_plot_path)
    
    # 保存配置和历史
    results = {
        'config': config,
        'coordinate_bounds': coordinate_bounds,
        'scale': scale,
        'training_history': history,
        'model_path': model_save_path
    }
    
    results_path = os.path.join(output_dir, config['output']['results_save_path'])
    with open(results_path, 'w') as f:
        # 转换numpy类型为Python原生类型以便JSON序列化
        def convert_numpy(obj):
            if isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            return obj
        
        json.dump(results, f, indent=2, default=convert_numpy)
    
    print(f"\n训练完成!")
    print(f"最佳损失: {history['best_loss']:.6f}")
    print(f"训练时间: {history['total_time']:.1f}秒")
    print(f"模型已保存到: {model_save_path}")
    print(f"结果已保存到: {results_path}")
    print("=" * 60)


if __name__ == "__main__":
    main()
