#!/usr/bin/env python3
"""
TVF EikoNet简化训练脚本 - PyTorch版本

使用TVF数据训练EikoNet神经网络模型的简化版本，用于快速测试。
"""

import os
import sys
import json
import time
import torch
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, List, Tuple

# 添加src路径到Python路径
current_dir = Path(__file__).parent
src_dir = current_dir.parent / "pytorch_hyposvi" / "src"
sys.path.insert(0, str(src_dir))

from eikonet.models import EikoNet
from eikonet.trainer import EikoNetTrainer
from eikonet.loss import EikonalLoss
from data.velocity_model import VelocityModel1D, build_1d_training_data
from data.loader import StationDataLoader


def load_tvf_config(config_path: str) -> dict:
    """加载TVF配置文件"""
    with open(config_path, 'r') as f:
        config = json.load(f)
    
    print(f"TVF配置已加载:")
    print(f"  台站文件: {config['data']['station_file']}")
    print(f"  速度模型文件: {config['data']['velmod_file']}")
    print(f"  训练样本数: {config['training']['n_train']}")
    print(f"  训练轮次: {config['training']['n_epochs']}")
    
    return config


def create_coordinate_bounds(config: dict) -> dict:
    """从配置创建坐标边界字典"""
    bounds = config['data']['coordinate_bounds']
    return {
        'lon_min': bounds['lon_min'],
        'lon_max': bounds['lon_max'],
        'lat_min': bounds['lat_min'],
        'lat_max': bounds['lat_max'],
        'z_min': bounds['z_min'],
        'z_max': bounds['z_max']
    }


def plot_training_history(history: dict, save_path: str):
    """绘制训练历史"""
    fig, ax = plt.subplots(1, 1, figsize=(10, 6))
    
    epochs = range(1, len(history['train_losses']) + 1)
    ax.plot(epochs, history['train_losses'], 'b-', label='Training Loss', linewidth=2)
    
    if history['val_losses'] and len(history['val_losses']) > 0:
        ax.plot(epochs, history['val_losses'], 'r-', label='Validation Loss', linewidth=2)
    
    ax.set_xlabel('Epoch')
    ax.set_ylabel('Loss')
    ax.set_title('TVF EikoNet Training History (Simple)')
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"训练历史图已保存到: {save_path}")


def main():
    """主训练函数"""
    # 设置路径
    config_path = "tvf_eikonet_config.json"
    output_dir = "."
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    print("=" * 60)
    print("TVF EikoNet 简化训练脚本 - PyTorch版本")
    print("=" * 60)
    
    # 加载配置
    print("\n1. 加载TVF配置文件...")
    config = load_tvf_config(config_path)
    
    # 修改配置为快速测试版本
    config['training']['n_train'] = 5000  # 减少训练样本
    config['training']['n_test'] = 1000   # 减少测试样本
    config['training']['n_epochs'] = 20   # 减少训练轮次
    config['training']['checkpoint_interval'] = 5
    
    print(f"简化配置: 训练样本={config['training']['n_train']}, "
          f"测试样本={config['training']['n_test']}, "
          f"训练轮次={config['training']['n_epochs']}")
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() and config['device']['use_cuda'] else 'cpu')
    print(f"\n使用设备: {device}")
    if device.type == 'cuda':
        print(f"GPU: {torch.cuda.get_device_name()}")
        print(f"GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
    
    # 加载速度模型
    print("\n2. 加载TVF速度模型...")
    velocity_model = VelocityModel1D.from_csv(config['data']['velmod_file'])
    print(f"速度模型: {velocity_model}")
    
    # 绘制速度模型
    vel_plot_path = os.path.join(output_dir, "tvf_velocity_model_simple.png")
    velocity_model.plot_model(save_path=vel_plot_path, max_depth=config['data']['coordinate_bounds']['z_max'])
    
    # 加载台站数据
    print("\n3. 加载TVF台站数据...")
    station_loader = StationDataLoader(config['data']['station_file'])
    print(f"加载了 {len(station_loader.data)} 个台站")
    
    # 创建坐标边界
    coordinate_bounds = create_coordinate_bounds(config)
    scale = config['data']['scale']
    
    print(f"\n坐标边界:")
    for key, value in coordinate_bounds.items():
        print(f"  {key}: {value}")
    print(f"缩放因子: {scale}")
    
    # 生成训练数据
    print("\n4. 生成训练数据...")
    train_loader, test_loader = build_1d_training_data(
        velocity_model=velocity_model,
        n_train=config['training']['n_train'],
        n_test=config['training']['n_test'],
        coordinate_bounds=coordinate_bounds,
        scale=scale,
        device=device
    )
    
    print(f"训练样本: {config['training']['n_train']}")
    print(f"测试样本: {config['training']['n_test']}")
    
    # 创建模型
    print("\n5. 创建TVF EikoNet模型...")
    model = EikoNet(scale=scale)
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 创建训练器
    loss_fn = EikonalLoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=config['training']['lr'])
    
    trainer = EikoNetTrainer(
        model=model,
        loss_fn=loss_fn,
        optimizer=optimizer,
        device=device,
        scale=scale
    )
    
    # 开始训练
    print(f"\n6. 开始简化训练 ({config['training']['n_epochs']} epochs)...")
    model_save_path = os.path.join(output_dir, "tvf_eikonet_model_simple.pth")
    
    start_time = time.time()
    
    history = trainer.train(
        train_loader=train_loader,
        val_loader=test_loader,
        n_epochs=config['training']['n_epochs'],
        save_path=model_save_path,
        checkpoint_interval=config['training']['checkpoint_interval'],
        early_stopping_patience=config['training']['early_stopping']['patience'],
        verbose=True
    )
    
    training_time = time.time() - start_time
    
    # 保存训练历史
    print("\n7. 保存结果...")
    
    # 绘制训练历史
    history_plot_path = os.path.join(output_dir, "tvf_training_history_simple.png")
    plot_training_history(history, history_plot_path)
    
    # 保存配置和历史
    results = {
        'config': config,
        'coordinate_bounds': coordinate_bounds,
        'scale': scale,
        'training_history': history,
        'model_path': model_save_path,
        'training_time': training_time
    }
    
    results_path = os.path.join(output_dir, "tvf_training_results_simple.json")
    with open(results_path, 'w') as f:
        # 转换numpy类型为Python原生类型以便JSON序列化
        def convert_numpy(obj):
            if isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            return obj
        
        json.dump(results, f, indent=2, default=convert_numpy)
    
    print(f"\n简化训练完成!")
    print(f"最佳损失: {history['best_loss']:.6f}")
    print(f"训练时间: {training_time:.1f}秒 ({training_time/60:.1f}分钟)")
    print(f"模型已保存到: {model_save_path}")
    print(f"结果已保存到: {results_path}")
    print("=" * 60)


if __name__ == "__main__":
    main()
