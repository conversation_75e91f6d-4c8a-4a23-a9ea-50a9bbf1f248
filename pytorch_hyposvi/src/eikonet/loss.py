"""
Loss functions for EikoNet training.

This module implements the Eikonal loss function and other physics-informed
loss functions for training the EikoNet neural network.
"""

import torch
import torch.nn as nn
from typing import Optional
from .utils import eikonal_pde


class EikonalLoss(nn.Module):
    """
    Eikonal loss function for physics-informed neural network training.
    
    The loss combines L1 and L2 terms to match the Julia implementation:
    Loss = α * L1_loss + β * L2_loss
    where:
    - L1_loss = sum(|ŝ - s|) / sum(|s|)
    - L2_loss = sqrt(sum((ŝ - s)²) / sum(s²))
    
    Args:
        l1_weight: Weight for L1 loss term (default: 0.95)
        l2_weight: Weight for L2 loss term (default: 0.05)
        epsilon: Small value for numerical stability
    """
    
    def __init__(
        self, 
        l1_weight: float = 0.95,
        l2_weight: float = 0.05,
        epsilon: float = 1e-8
    ):
        super().__init__()
        self.l1_weight = l1_weight
        self.l2_weight = l2_weight
        self.epsilon = epsilon
        
    def forward(
        self,
        x: torch.Tensor,
        target_slowness: torch.Tensor,
        model: nn.Module,
        scale: float = 200.0
    ) -> torch.Tensor:
        """
        Calculate Eikonal loss.
        
        Args:
            x: Input coordinates tensor
            target_slowness: True slowness values
            model: EikoNet model
            scale: Coordinate scaling factor
            
        Returns:
            Combined Eikonal loss
        """
        # Get predicted slowness from Eikonal PDE
        predicted_slowness = eikonal_pde(x, model, scale)
        
        # Ensure shapes match
        if predicted_slowness.shape != target_slowness.shape:
            if target_slowness.dim() == 1:
                target_slowness = target_slowness.unsqueeze(0)
            if predicted_slowness.dim() == 1:
                predicted_slowness = predicted_slowness.unsqueeze(0)
                
        # Calculate residuals
        residuals = predicted_slowness - target_slowness
        abs_residuals = torch.abs(residuals)
        
        # L1 loss component: sum(|ŝ - s|) / sum(|s|)
        l1_numerator = torch.sum(abs_residuals)
        l1_denominator = torch.sum(torch.abs(target_slowness)) + self.epsilon
        l1_loss = l1_numerator / l1_denominator
        
        # L2 loss component: sqrt(sum((ŝ - s)²) / sum(s²))
        l2_numerator = torch.sum(residuals**2)
        l2_denominator = torch.sum(target_slowness**2) + self.epsilon
        l2_loss = torch.sqrt(l2_numerator / l2_denominator)
        
        # Combined loss
        total_loss = self.l1_weight * l1_loss + self.l2_weight * l2_loss
        
        return total_loss


class MSELoss(nn.Module):
    """
    Mean Squared Error loss for comparison.
    
    Simple MSE loss between predicted and target slowness values.
    """
    
    def __init__(self):
        super().__init__()
        self.mse = nn.MSELoss()
        
    def forward(
        self,
        x: torch.Tensor,
        target_slowness: torch.Tensor,
        model: nn.Module,
        scale: float = 200.0
    ) -> torch.Tensor:
        """
        Calculate MSE loss.
        
        Args:
            x: Input coordinates tensor
            target_slowness: True slowness values
            model: EikoNet model
            scale: Coordinate scaling factor
            
        Returns:
            MSE loss
        """
        predicted_slowness = eikonal_pde(x, model, scale)
        
        # Ensure shapes match
        if predicted_slowness.shape != target_slowness.shape:
            if target_slowness.dim() == 1:
                target_slowness = target_slowness.unsqueeze(0)
            if predicted_slowness.dim() == 1:
                predicted_slowness = predicted_slowness.unsqueeze(0)
                
        return self.mse(predicted_slowness, target_slowness)


class HuberLoss(nn.Module):
    """
    Huber loss for robust training.
    
    Combines L1 and L2 loss for robustness to outliers.
    
    Args:
        delta: Threshold for switching between L1 and L2 loss
    """
    
    def __init__(self, delta: float = 1.0):
        super().__init__()
        self.delta = delta
        self.huber = nn.HuberLoss(delta=delta)
        
    def forward(
        self,
        x: torch.Tensor,
        target_slowness: torch.Tensor,
        model: nn.Module,
        scale: float = 200.0
    ) -> torch.Tensor:
        """
        Calculate Huber loss.
        
        Args:
            x: Input coordinates tensor
            target_slowness: True slowness values
            model: EikoNet model
            scale: Coordinate scaling factor
            
        Returns:
            Huber loss
        """
        predicted_slowness = eikonal_pde(x, model, scale)
        
        # Ensure shapes match
        if predicted_slowness.shape != target_slowness.shape:
            if target_slowness.dim() == 1:
                target_slowness = target_slowness.unsqueeze(0)
            if predicted_slowness.dim() == 1:
                predicted_slowness = predicted_slowness.unsqueeze(0)
                
        return self.huber(predicted_slowness, target_slowness)


def get_loss_function(loss_type: str = "eikonal", **kwargs) -> nn.Module:
    """
    Factory function to get loss function by name.
    
    Args:
        loss_type: Type of loss function ("eikonal", "mse", "huber")
        **kwargs: Additional arguments for loss function
        
    Returns:
        Loss function module
    """
    if loss_type.lower() == "eikonal":
        return EikonalLoss(**kwargs)
    elif loss_type.lower() == "mse":
        return MSELoss()
    elif loss_type.lower() == "huber":
        return HuberLoss(**kwargs)
    else:
        raise ValueError(f"Unknown loss type: {loss_type}")


class LossTracker:
    """
    Utility class for tracking loss values during training.
    """
    
    def __init__(self):
        self.train_losses = []
        self.val_losses = []
        self.best_loss = float('inf')
        self.best_epoch = 0
        
    def update(self, train_loss: float, val_loss: Optional[float] = None, epoch: int = 0):
        """
        Update loss tracking.
        
        Args:
            train_loss: Training loss value
            val_loss: Validation loss value (optional)
            epoch: Current epoch number
        """
        self.train_losses.append(train_loss)
        
        if val_loss is not None:
            self.val_losses.append(val_loss)
            
            if val_loss < self.best_loss:
                self.best_loss = val_loss
                self.best_epoch = epoch
                
    def get_best(self) -> tuple:
        """
        Get best validation loss and epoch.
        
        Returns:
            Tuple of (best_loss, best_epoch)
        """
        return self.best_loss, self.best_epoch
        
    def plot_losses(self, save_path: str = "loss_curves.png"):
        """
        Plot training and validation loss curves.
        
        Args:
            save_path: Path to save the plot
        """
        try:
            import matplotlib.pyplot as plt
        except ImportError:
            print("Matplotlib not available for plotting")
            return
            
        plt.figure(figsize=(10, 6))
        plt.plot(self.train_losses, label='Training Loss')
        
        if self.val_losses:
            plt.plot(self.val_losses, label='Validation Loss')
            
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.legend()
        plt.title('Training Progress')
        plt.yscale('log')
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig(save_path)
        plt.close()
        
        print(f"Loss curves saved to {save_path}")
