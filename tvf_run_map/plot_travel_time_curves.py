#!/usr/bin/env python3
"""
绘制P波与S波走时曲线图
基于重定位数据和TauP理论模型对比分析
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from obspy.taup import TauPyModel
import os
from math import radians, cos, sin, asin, sqrt

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']  
plt.rcParams['axes.unicode_minus'] = False

def haversine_distance(lat1, lon1, lat2, lon2):
    """计算两点间的球面距离 (km)"""
    R = 6371  # 地球半径 km
    
    lat1, lon1, lat2, lon2 = map(radians, [lat1, lon1, lat2, lon2])
    dlat = lat2 - lat1
    dlon = lon2 - lon1
    
    a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
    c = 2 * asin(sqrt(a))
    
    return R * c

def degree_distance(lat1, lon1, lat2, lon2):
    """计算震中距 (度)"""
    return haversine_distance(lat1, lon1, lat2, lon2) / 111.32

def create_travel_time_curves():
    """创建P波和S波走时曲线图"""
    
    print("正在生成P波与S波走时曲线图...")
    
    # 创建输出目录
    output_dir = "travel_time_curves"
    os.makedirs(output_dir, exist_ok=True)
    
    # 检查是否有重定位结果数据
    relocated_catalog_path = "quick_test_results/relocated_catalog.csv"
    relocated_phases_path = "quick_test_results/relocated_phases.csv"
    station_path = "../tvf_hyposvi_data/hyposvi_station.csv"
    
    if not all(os.path.exists(path) for path in [relocated_catalog_path, relocated_phases_path, station_path]):
        print("警告: 找不到必要的数据文件，将使用理论模型生成走时曲线")
        create_theoretical_curves_only()
        return
    
    # 读取数据
    catalog_df = pd.read_csv(relocated_catalog_path)
    phases_df = pd.read_csv(relocated_phases_path)
    stations_df = pd.read_csv(station_path)
    
    print(f"加载了 {len(catalog_df)} 个地震事件")
    print(f"加载了 {len(phases_df)} 个震相记录")
    print(f"加载了 {len(stations_df)} 个台站")
    
    # 合并数据
    merged_df = phases_df.merge(catalog_df[['evid', 'lat', 'lon', 'depth']], on='evid')
    merged_df = merged_df.merge(stations_df[['station', 'lat', 'lon']], 
                               left_on='sta', right_on='station', suffixes=('_event', '_station'))
    
    # 计算震中距
    merged_df['distance_km'] = merged_df.apply(
        lambda row: haversine_distance(row['lat_event'], row['lon_event'], 
                                     row['lat_station'], row['lon_station']), axis=1
    )
    merged_df['distance_deg'] = merged_df['distance_km'] / 111.32
    
    # 分离P波和S波数据
    p_waves = merged_df[merged_df['phase'] == 'P'].copy()
    s_waves = merged_df[merged_df['phase'] == 'S'].copy()
    
    print(f"P波记录: {len(p_waves)} 个")
    print(f"S波记录: {len(s_waves)} 个")
    
    # 创建图表
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('P波与S波走时曲线分析', fontsize=16, fontweight='bold')
    
    # 1. 距离-走时散点图
    if len(p_waves) > 0:
        axes[0,0].scatter(p_waves['distance_km'], p_waves['travel_time'], 
                         alpha=0.6, s=30, color='blue', label=f'P波 (n={len(p_waves)})')
    
    if len(s_waves) > 0:
        axes[0,0].scatter(s_waves['distance_km'], s_waves['travel_time'], 
                         alpha=0.6, s=30, color='red', label=f'S波 (n={len(s_waves)})')
    
    # 添加理论走时曲线
    distances = np.linspace(0, 300, 100)
    
    # 简化模型
    p_simple = distances / 6.0  # P波速度 6.0 km/s
    s_simple = distances / 3.5  # S波速度 3.5 km/s
    
    axes[0,0].plot(distances, p_simple, 'b--', linewidth=2, alpha=0.8, label='P波理论 (6.0 km/s)')
    axes[0,0].plot(distances, s_simple, 'r--', linewidth=2, alpha=0.8, label='S波理论 (3.5 km/s)')
    
    axes[0,0].set_xlabel('震中距 (km)')
    axes[0,0].set_ylabel('走时 (秒)')
    axes[0,0].set_title('实际走时 vs 理论走时曲线')
    axes[0,0].legend()
    axes[0,0].grid(True, alpha=0.3)
    
    # 2. TauP理论模型对比
    try:
        model = TauPyModel(model="iasp91")
        
        # 计算不同深度的理论走时
        depths = [5, 10, 20]  # km
        colors_p = ['lightblue', 'blue', 'darkblue']
        colors_s = ['lightcoral', 'red', 'darkred']
        
        distance_range = np.linspace(0.1, 3.0, 50)  # 震中距范围 (度)
        
        for i, depth in enumerate(depths):
            p_times = []
            s_times = []
            
            for dist in distance_range:
                try:
                    # P波
                    arrivals_p = model.get_travel_times(source_depth_in_km=depth,
                                                       distance_in_degree=dist,
                                                       phase_list=['P'])
                    if arrivals_p:
                        p_times.append(arrivals_p[0].time)
                    else:
                        p_times.append(np.nan)
                    
                    # S波
                    arrivals_s = model.get_travel_times(source_depth_in_km=depth,
                                                       distance_in_degree=dist,
                                                       phase_list=['S'])
                    if arrivals_s:
                        s_times.append(arrivals_s[0].time)
                    else:
                        s_times.append(np.nan)
                        
                except Exception:
                    p_times.append(np.nan)
                    s_times.append(np.nan)
            
            # 转换度数到公里
            distance_km = distance_range * 111.32
            
            axes[0,1].plot(distance_km, p_times, color=colors_p[i], linewidth=2,
                          label=f'P波 {depth}km深度')
            axes[0,1].plot(distance_km, s_times, color=colors_s[i], linewidth=2,
                          label=f'S波 {depth}km深度')
        
        axes[0,1].set_xlabel('震中距 (km)')
        axes[0,1].set_ylabel('走时 (秒)')
        axes[0,1].set_title('TauP理论走时曲线 (iasp91模型)')
        axes[0,1].legend()
        axes[0,1].grid(True, alpha=0.3)
        
    except Exception as e:
        print(f"TauP模型加载失败: {e}")
        axes[0,1].text(0.5, 0.5, 'TauP模型不可用', 
                      transform=axes[0,1].transAxes, ha='center', va='center')
    
    # 3. 走时残差分析
    if len(p_waves) > 0 or len(s_waves) > 0:
        # 计算理论走时残差
        if len(p_waves) > 0:
            p_waves['theoretical_time'] = p_waves['distance_km'] / 6.0
            p_waves['residual'] = p_waves['travel_time'] - p_waves['theoretical_time']
            
            axes[1,0].scatter(p_waves['distance_km'], p_waves['residual'], 
                             alpha=0.6, s=30, color='blue', label='P波残差')
        
        if len(s_waves) > 0:
            s_waves['theoretical_time'] = s_waves['distance_km'] / 3.5
            s_waves['residual'] = s_waves['travel_time'] - s_waves['theoretical_time']
            
            axes[1,0].scatter(s_waves['distance_km'], s_waves['residual'], 
                             alpha=0.6, s=30, color='red', label='S波残差')
        
        axes[1,0].axhline(y=0, color='black', linestyle='--', alpha=0.5)
        axes[1,0].set_xlabel('震中距 (km)')
        axes[1,0].set_ylabel('走时残差 (秒)')
        axes[1,0].set_title('走时残差分布')
        axes[1,0].legend()
        axes[1,0].grid(True, alpha=0.3)
    
    # 4. 统计分析
    stats_text = ""
    
    if len(p_waves) > 0:
        p_mean_residual = p_waves['residual'].mean()
        p_std_residual = p_waves['residual'].std()
        stats_text += f"P波统计:\n"
        stats_text += f"  样本数: {len(p_waves)}\n"
        stats_text += f"  平均残差: {p_mean_residual:.3f}s\n"
        stats_text += f"  残差标准差: {p_std_residual:.3f}s\n"
        stats_text += f"  距离范围: {p_waves['distance_km'].min():.1f}-{p_waves['distance_km'].max():.1f}km\n\n"
    
    if len(s_waves) > 0:
        s_mean_residual = s_waves['residual'].mean()
        s_std_residual = s_waves['residual'].std()
        stats_text += f"S波统计:\n"
        stats_text += f"  样本数: {len(s_waves)}\n"
        stats_text += f"  平均残差: {s_mean_residual:.3f}s\n"
        stats_text += f"  残差标准差: {s_std_residual:.3f}s\n"
        stats_text += f"  距离范围: {s_waves['distance_km'].min():.1f}-{s_waves['distance_km'].max():.1f}km\n\n"
    
    axes[1,1].text(0.05, 0.95, stats_text, transform=axes[1,1].transAxes, 
                   fontsize=11, verticalalignment='top', fontfamily='monospace',
                   bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))
    
    axes[1,1].set_title('统计信息')
    axes[1,1].axis('off')
    
    plt.tight_layout()
    plt.savefig(f'{output_dir}/travel_time_curves_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"已生成: {output_dir}/travel_time_curves_analysis.png")
    
    # 创建简化的走时曲线图
    create_simplified_curves(output_dir)

def create_simplified_curves(output_dir):
    """创建简化的P波S波走时曲线对比图"""
    
    fig, ax = plt.subplots(1, 1, figsize=(12, 8))
    
    # 距离范围
    distances = np.linspace(0, 300, 100)
    
    # 不同模型的走时计算
    # 1. 简化均匀模型
    p_simple = distances / 6.0  # P波速度 6.0 km/s
    s_simple = distances / 3.5  # S波速度 3.5 km/s
    
    # 2. 地壳模型 (更接近实际)
    p_crust = distances / 6.2  # 地壳P波速度
    s_crust = distances / 3.6  # 地壳S波速度
    
    # 3. 上地幔模型
    p_mantle = distances / 8.0  # 上地幔P波速度
    s_mantle = distances / 4.5  # 上地幔S波速度
    
    # 绘制曲线
    ax.plot(distances, p_simple, 'b-', linewidth=2, label='P波 - 简化模型 (6.0 km/s)')
    ax.plot(distances, s_simple, 'r-', linewidth=2, label='S波 - 简化模型 (3.5 km/s)')
    
    ax.plot(distances, p_crust, 'b--', linewidth=2, alpha=0.7, label='P波 - 地壳 (6.2 km/s)')
    ax.plot(distances, s_crust, 'r--', linewidth=2, alpha=0.7, label='S波 - 地壳 (3.6 km/s)')
    
    ax.plot(distances, p_mantle, 'b:', linewidth=2, alpha=0.7, label='P波 - 上地幔 (8.0 km/s)')
    ax.plot(distances, s_mantle, 'r:', linewidth=2, alpha=0.7, label='S波 - 上地幔 (4.5 km/s)')
    
    # 添加TauP理论曲线
    try:
        model = TauPyModel(model="iasp91")
        distance_deg = np.linspace(0.1, 2.5, 50)
        depth = 10  # km
        
        p_taup = []
        s_taup = []
        
        for dist in distance_deg:
            try:
                # P波
                arrivals_p = model.get_travel_times(source_depth_in_km=depth,
                                                   distance_in_degree=dist,
                                                   phase_list=['P'])
                if arrivals_p:
                    p_taup.append(arrivals_p[0].time)
                else:
                    p_taup.append(np.nan)
                
                # S波
                arrivals_s = model.get_travel_times(source_depth_in_km=depth,
                                                   distance_in_degree=dist,
                                                   phase_list=['S'])
                if arrivals_s:
                    s_taup.append(arrivals_s[0].time)
                else:
                    s_taup.append(np.nan)
                    
            except Exception:
                p_taup.append(np.nan)
                s_taup.append(np.nan)
        
        distance_km_taup = distance_deg * 111.32
        
        ax.plot(distance_km_taup, p_taup, 'bo', markersize=4, alpha=0.6, 
               label='P波 - TauP iasp91')
        ax.plot(distance_km_taup, s_taup, 'ro', markersize=4, alpha=0.6, 
               label='S波 - TauP iasp91')
        
    except Exception as e:
        print(f"无法加载TauP模型: {e}")
    
    ax.set_xlabel('震中距 (km)', fontsize=12)
    ax.set_ylabel('走时 (秒)', fontsize=12)
    ax.set_title('P波与S波走时曲线对比\n(不同速度模型)', fontsize=14, fontweight='bold')
    ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    ax.grid(True, alpha=0.3)
    
    # 设置合理的轴范围
    ax.set_xlim(0, 280)
    ax.set_ylim(0, 80)
    
    plt.tight_layout()
    plt.savefig(f'{output_dir}/travel_time_curves_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"已生成: {output_dir}/travel_time_curves_comparison.png")

def create_theoretical_curves_only():
    """仅使用理论模型创建走时曲线"""
    
    output_dir = "travel_time_curves"
    os.makedirs(output_dir, exist_ok=True)
    
    print("使用理论模型生成走时曲线...")
    create_simplified_curves(output_dir)

if __name__ == "__main__":
    create_travel_time_curves()
    print("\nP波与S波走时曲线图生成完成！")
    print("输出文件:")
    print("1. travel_time_curves/travel_time_curves_analysis.png - 综合分析图")
    print("2. travel_time_curves/travel_time_curves_comparison.png - 模型对比图")
