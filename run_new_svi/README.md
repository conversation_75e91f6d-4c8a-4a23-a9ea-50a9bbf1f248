# 地震重定位处理器

## 📁 目录结构

```
run_new_svi/
├── fast_single_processor.py    # 主要处理器文件
├── parallel_config.json        # 配置文件
├── fast_single_results/        # 输出结果目录
└── README.md                   # 使用说明
```

## 🚀 使用方法

### 基本使用
```bash
cd /home/<USER>/run_eikonet_hyposvi/run_new_svi
python fast_single_processor.py
```

### 功能特点
- ✅ **独立事件处理**: 每个地震事件独立处理，使用各自真实的发震时间
- ✅ **EikoNet理论走时**: 基于重定位后的震源位置重新计算理论震相到时
- ✅ **科学正确**: 不依赖原始观测时间，消除观测误差
- ✅ **完全自动化**: 一键完成重定位和输出文件生成

## 📊 输出文件

处理完成后，在 `fast_single_results/` 目录下生成：

1. **`fast_single_results.csv`** - 原始重定位结果
2. **`relocated_catalog.csv`** - 重定位地震目录文件
3. **`relocated_phases.csv`** - 基于EikoNet计算的理论震相文件

### 地震目录格式
```csv
evid,latitude,longitude,depth,time,mag
1,35.9638,-117.6707,4.92,2000-12-13T15:00:44.691961,1.0
```

### 震相文件格式
```csv
time,evid,arid,phase,network,station,theoretical_travel_time,calculation_method,origin_time
2000-12-13T15:00:53.783577,1,1,P,X,ST0,9.091615676879883,EikoNet_theoretical,2000-12-13T15:00:44.691961
```

## ⚙️ 配置文件

`parallel_config.json` 包含处理参数配置，主要参数：
- `data.scale`: EikoNet模型缩放参数
- `svi.num_samples`: SVI采样数量
- `svi.warmup_steps`: 预热步数

## 📋 依赖要求

- Python 3.8+
- PyTorch (GPU支持)
- HypoSVI定位器
- EikoNet模型

## 🔧 处理流程

1. **初始化**: 加载EikoNet模型和配置
2. **数据预加载**: 加载台站坐标和坐标转换器
3. **地震重定位**: 使用HypoSVI方法逐个重定位事件
4. **理论走时计算**: 使用EikoNet重新计算所有震相走时
5. **文件生成**: 自动生成标准格式的目录和震相文件

## 💡 核心改进

相比传统方法，本处理器的主要改进：

1. **独立时间处理**: 不使用统一时间基准，每个事件独立处理
2. **理论走时重算**: 基于EikoNet重新计算，而非调整原始观测
3. **物理一致性**: 震源位置和震相时间基于同一速度模型
4. **数据完整性**: 保留原始事件属性，增加理论计算信息

## 📈 性能指标

- **处理速度**: 约2-5事件/分钟（取决于硬件）
- **内存优化**: 数据复用，减少重复加载
- **GPU加速**: 支持CUDA加速计算

## ⚠️ 注意事项

1. 确保有足够的GPU内存用于EikoNet计算
2. 确保输出目录有足够的磁盘空间
3. 处理大量事件时建议监控系统资源使用情况
