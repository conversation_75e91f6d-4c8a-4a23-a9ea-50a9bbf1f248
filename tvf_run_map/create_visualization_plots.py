#!/usr/bin/env python3
"""
生成重定位差异的可视化分析图表
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import os

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']  
plt.rcParams['axes.unicode_minus'] = False

def create_visualization_plots():
    """创建重定位差异的可视化图表"""
    
    results_dir = "timing_analysis_results"
    output_dir = "timing_analysis_plots"
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 设置图表样式
    plt.style.use('seaborn-v0_8-whitegrid')
    
    print("正在生成重定位差异可视化图表...")
    
    # 1. 发震时刻差异分析图
    if os.path.exists(f"{results_dir}/origin_time_differences.csv"):
        origin_df = pd.read_csv(f"{results_dir}/origin_time_differences.csv")
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('地震发震时刻和位置重定位差异分析', fontsize=16, fontweight='bold')
        
        # 发震时刻差异分布
        axes[0,0].hist(origin_df['time_diff_seconds'], bins=10, alpha=0.7, color='skyblue', edgecolor='black')
        axes[0,0].set_xlabel('发震时刻差异 (秒)')
        axes[0,0].set_ylabel('频次')
        axes[0,0].set_title('发震时刻差异分布')
        axes[0,0].axvline(origin_df['time_diff_seconds'].mean(), color='red', linestyle='--', 
                         label=f'平均值: {origin_df["time_diff_seconds"].mean():.2f}s')
        axes[0,0].legend()
        
        # 水平位置偏移
        axes[0,1].scatter(origin_df['evid'], origin_df['horizontal_dist_km'], 
                         alpha=0.7, s=100, color='orange')
        axes[0,1].set_xlabel('地震事件ID')
        axes[0,1].set_ylabel('水平位置偏移 (km)')
        axes[0,1].set_title('水平位置偏移')
        axes[0,1].axhline(origin_df['horizontal_dist_km'].mean(), color='red', linestyle='--',
                         label=f'平均值: {origin_df["horizontal_dist_km"].mean():.2f}km')
        axes[0,1].legend()
        
        # 经纬度变化散点图
        axes[1,0].scatter(origin_df['lon_diff'], origin_df['lat_diff'], 
                         alpha=0.7, s=100, color='green')
        axes[1,0].set_xlabel('经度差异 (度)')
        axes[1,0].set_ylabel('纬度差异 (度)')
        axes[1,0].set_title('经纬度位置变化')
        axes[1,0].axhline(0, color='black', linestyle='-', alpha=0.3)
        axes[1,0].axvline(0, color='black', linestyle='-', alpha=0.3)
        
        # 深度变化
        axes[1,1].scatter(origin_df['evid'], origin_df['depth_diff'], 
                         alpha=0.7, s=100, color='purple')
        axes[1,1].set_xlabel('地震事件ID')
        axes[1,1].set_ylabel('深度差异 (km)')
        axes[1,1].set_title('深度变化')
        axes[1,1].axhline(0, color='red', linestyle='--')
        
        plt.tight_layout()
        plt.savefig(f'{output_dir}/origin_time_location_analysis.png', dpi=300, bbox_inches='tight')
        plt.close()
        print(f"已生成: {output_dir}/origin_time_location_analysis.png")
    
    # 2. 震相到时差异分析图
    if os.path.exists(f"{results_dir}/phase_time_differences.csv"):
        phase_df = pd.read_csv(f"{results_dir}/phase_time_differences.csv")
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('震相到时差异分析', fontsize=16, fontweight='bold')
        
        # 震相到时差异分布
        axes[0,0].hist(phase_df['time_diff_seconds'], bins=15, alpha=0.7, color='lightcoral', edgecolor='black')
        axes[0,0].set_xlabel('震相到时差异 (秒)')
        axes[0,0].set_ylabel('频次')
        axes[0,0].set_title('震相到时差异分布')
        axes[0,0].axvline(phase_df['time_diff_seconds'].mean(), color='blue', linestyle='--',
                         label=f'平均值: {phase_df["time_diff_seconds"].mean():.2f}s')
        axes[0,0].legend()
        
        # 按震相类型分析
        phase_types = phase_df['phase_type'].unique()
        colors = ['skyblue', 'lightgreen']
        
        for i, phase_type in enumerate(phase_types):
            subset = phase_df[phase_df['phase_type'] == phase_type]
            axes[0,1].hist(subset['time_diff_seconds'], bins=10, alpha=0.6, 
                          label=f'{phase_type} 波 (n={len(subset)})', color=colors[i])
        
        axes[0,1].set_xlabel('震相到时差异 (秒)')
        axes[0,1].set_ylabel('频次')
        axes[0,1].set_title('按震相类型分析到时差异')
        axes[0,1].legend()
        
        # 箱线图对比
        phase_df.boxplot(column='time_diff_seconds', by='phase_type', ax=axes[1,0])
        axes[1,0].set_xlabel('震相类型')
        axes[1,0].set_ylabel('到时差异 (秒)')
        axes[1,0].set_title('震相类型到时差异箱线图')
        plt.suptitle('')  # 移除自动标题
        
        # 按地震事件分析
        event_stats = phase_df.groupby('evid')['time_diff_seconds'].agg(['mean', 'std', 'count'])
        axes[1,1].scatter(event_stats.index, event_stats['mean'], s=event_stats['count']*10, alpha=0.7)
        axes[1,1].set_xlabel('地震事件ID')
        axes[1,1].set_ylabel('平均到时差异 (秒)')
        axes[1,1].set_title('各地震事件的平均到时差异\n(圆圈大小代表震相数量)')
        
        plt.tight_layout()
        plt.savefig(f'{output_dir}/phase_time_analysis.png', dpi=300, bbox_inches='tight')
        plt.close()
        print(f"已生成: {output_dir}/phase_time_analysis.png")
    
    # 3. 走时变化分析图
    if os.path.exists(f"{results_dir}/travel_time_changes.csv"):
        travel_df = pd.read_csv(f"{results_dir}/travel_time_changes.csv")
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('理论走时变化分析', fontsize=16, fontweight='bold')
        
        # 走时变化分布
        axes[0,0].hist(travel_df['travel_time_change'], bins=15, alpha=0.7, color='gold', edgecolor='black')
        axes[0,0].set_xlabel('走时变化 (秒)')
        axes[0,0].set_ylabel('频次')
        axes[0,0].set_title('走时变化分布')
        axes[0,0].axvline(travel_df['travel_time_change'].mean(), color='red', linestyle='--',
                         label=f'平均值: {travel_df["travel_time_change"].mean():.2f}s')
        axes[0,0].axvline(0, color='black', linestyle='-', alpha=0.5, label='无变化线')
        axes[0,0].legend()
        
        # 按震相类型分析走时变化
        for i, phase_type in enumerate(travel_df['phase_type'].unique()):
            subset = travel_df[travel_df['phase_type'] == phase_type]
            axes[0,1].hist(subset['travel_time_change'], bins=10, alpha=0.6,
                          label=f'{phase_type} 波 (n={len(subset)})', color=colors[i])
        
        axes[0,1].set_xlabel('走时变化 (秒)')
        axes[0,1].set_ylabel('频次')
        axes[0,1].set_title('按震相类型分析走时变化')
        axes[0,1].axvline(0, color='black', linestyle='-', alpha=0.5)
        axes[0,1].legend()
        
        # 原始走时 vs 重定位后走时
        axes[1,0].scatter(travel_df['original_travel_time'], travel_df['relocated_travel_time'], 
                         alpha=0.7, s=50)
        
        # 添加对角线 (y=x)
        min_val = min(travel_df['original_travel_time'].min(), travel_df['relocated_travel_time'].min())
        max_val = max(travel_df['original_travel_time'].max(), travel_df['relocated_travel_time'].max())
        axes[1,0].plot([min_val, max_val], [min_val, max_val], 'r--', alpha=0.7, label='y=x (无变化)')
        
        axes[1,0].set_xlabel('原始走时 (秒)')
        axes[1,0].set_ylabel('重定位后走时 (秒)')
        axes[1,0].set_title('原始走时 vs 重定位后走时')
        axes[1,0].legend()
        
        # 走时变化箱线图
        travel_df.boxplot(column='travel_time_change', by='phase_type', ax=axes[1,1])
        axes[1,1].set_xlabel('震相类型')
        axes[1,1].set_ylabel('走时变化 (秒)')
        axes[1,1].set_title('震相类型走时变化箱线图')
        axes[1,1].axhline(0, color='red', linestyle='--', alpha=0.7)
        plt.suptitle('')  # 移除自动标题
        
        plt.tight_layout()
        plt.savefig(f'{output_dir}/travel_time_analysis.png', dpi=300, bbox_inches='tight')
        plt.close()
        print(f"已生成: {output_dir}/travel_time_analysis.png")
    
    # 4. 创建综合对比图
    if (os.path.exists(f"{results_dir}/origin_time_differences.csv") and 
        os.path.exists(f"{results_dir}/phase_time_differences.csv") and
        os.path.exists(f"{results_dir}/travel_time_changes.csv")):
        
        origin_df = pd.read_csv(f"{results_dir}/origin_time_differences.csv")
        phase_df = pd.read_csv(f"{results_dir}/phase_time_differences.csv")
        travel_df = pd.read_csv(f"{results_dir}/travel_time_changes.csv")
        
        fig, axes = plt.subplots(1, 3, figsize=(18, 6))
        fig.suptitle('重定位差异综合对比', fontsize=16, fontweight='bold')
        
        # 时间差异对比
        time_data = [
            origin_df['time_diff_seconds'].values,
            phase_df['time_diff_seconds'].values,
            travel_df['travel_time_change'].values
        ]
        
        labels = ['发震时刻差异', '震相到时差异', '理论走时变化']
        colors = ['skyblue', 'lightcoral', 'gold']
        
        axes[0].boxplot(time_data, labels=labels, patch_artist=True,
                       boxprops=dict(facecolor='lightblue', alpha=0.7))
        axes[0].set_ylabel('时间差异 (秒)')
        axes[0].set_title('各类时间差异对比')
        axes[0].axhline(0, color='red', linestyle='--', alpha=0.5)
        
        # 统计摘要
        stats_data = {
            '类型': labels,
            '平均值': [np.mean(data) for data in time_data],
            '标准差': [np.std(data) for data in time_data],
            '样本数': [len(data) for data in time_data]
        }
        
        stats_df = pd.DataFrame(stats_data)
        
        # 在图上显示统计信息
        axes[1].axis('tight')
        axes[1].axis('off')
        table = axes[1].table(cellText=[[f'{val:.3f}' if isinstance(val, float) else str(val) 
                                        for val in row] for row in stats_df.values],
                             colLabels=stats_df.columns,
                             cellLoc='center',
                             loc='center')
        table.auto_set_font_size(False)
        table.set_fontsize(12)
        table.scale(1.2, 1.5)
        axes[1].set_title('统计摘要表')
        
        # 改进情况可视化
        improvement_metrics = {
            '水平位置偏移': origin_df['horizontal_dist_km'].mean(),
            '发震时刻调整': origin_df['time_diff_seconds'].mean(),
            '走时改进': abs(travel_df['travel_time_change'].mean())
        }
        
        axes[2].bar(improvement_metrics.keys(), improvement_metrics.values(), 
                   color=['green', 'blue', 'orange'], alpha=0.7)
        axes[2].set_ylabel('数值')
        axes[2].set_title('重定位改进指标')
        axes[2].tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        plt.savefig(f'{output_dir}/comprehensive_comparison.png', dpi=300, bbox_inches='tight')
        plt.close()
        print(f"已生成: {output_dir}/comprehensive_comparison.png")
    
    print(f"\n所有可视化图表已保存到: {output_dir}/")
    print("图表包括：")
    print("1. origin_time_location_analysis.png - 发震时刻和位置重定位差异")
    print("2. phase_time_analysis.png - 震相到时差异分析")
    print("3. travel_time_analysis.png - 理论走时变化分析")
    print("4. comprehensive_comparison.png - 综合对比分析")

if __name__ == "__main__":
    create_visualization_plots()
